import React, { useState } from 'react';
import PrimaryBar from 'components/Navigation/primaryBar';
import { useClass } from 'hooks';
import classNames from 'classnames';
import { useDispatch, useSelector } from 'react-redux';
import { debounceFunc } from 'utils/time';
import { Icon } from '@fluentui/react';
import { setSidebarStick } from 'store/actions/application';

const HamburgerButton = () => {
  const css = useClass(styles);
  const dispatch = useDispatch();
  const { step } = useSelector((state) => state.tutorial);
  const isSidebarStick = useSelector((state) => state.application.isSidebarStick);
  const [forceShow, setForceShow] = useState(false);

  const toggleStickSidebar = () => dispatch(setSidebarStick(!isSidebarStick));

  return (
    <div
      className={classNames(css.hamburgerButton, {
        [css.hamburgerButtonForceShow]: forceShow,
        active: isSidebarStick,
      })}
    >
      <div
        data-isbutton="button"
        onClick={toggleStickSidebar}
        className={'header-item-menu-img'}
        role="button"
        tabIndex={0}
      >
        <Icon iconName="MenuHamberger" />
      </div>

      <div
        className={classNames('primary-bar', css.primaryWrap, {
          'force-show': step === 3 || forceShow,
          'sidebar-stick': isSidebarStick,
        })}
      >
        <PrimaryBar setForceShow={debounceFunc(setForceShow, 0)} />
      </div>
    </div>
  );
};

const styles = (theme) => {
  return {
    primaryWrap: {
      top: theme?.variable?.headerHeight,
      height: `calc(100vh - ${theme?.variable?.headerHeight}px)`,
    },
    hamburgerButton: {
      height: '100%',
      '.header-item-menu-img': {
        height: '100%',
        padding: '15px 16px 11px 16px',
        cursor: 'pointer',
        display: 'block',
      },
      '&:hover': {
        '.primary-bar': {
          left: 0,
        },
      },
      '&:hover, &.active': {
        '.header-item-menu-img': {
          backgroundColor: theme.darkMode
            ? theme.palette.primaryColorShade4
            : theme.palette.primaryColorShade7,
        },
      },

      '.primary-bar': {
        position: 'fixed',
        left: -theme?.variable?.secondarySideBarWidth,
        zIndex: theme?.variable?.sideBarZIndex,
        transition: 'left 0.33s',

        '&.sidebar-stick': {
          color: '#FFFFFF',
        },
      },
      '.primary-bar.sidebar-stick, .primary-bar.force-show': {
        zIndex: theme?.variable?.sidebarStickZIndex,
        left: 0,
      },
    },
    hamburgerButtonForceShow: {
      '.header-item-menu-img': {
        backgroundColor: theme.darkMode
          ? theme.palette.primaryColorShade4
          : theme.palette.primaryColorShade7,
      },
    },
  };
};

export default HamburgerButton;
