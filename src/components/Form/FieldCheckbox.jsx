import MyField from 'components/Form/Field';
import { useClass } from 'hooks';
import styles from './styles';
import FieldLayout from './FieldLayout';
import cn from 'classnames';
import { PROGRESS_NOTE_VIEW } from 'constants/progressNote';
import { Checkbox } from 'components/RpaTracker';
import React, { Fragment } from 'react';
import FieldText from './FieldText';

const FieldCheckBox = ({
  title,
  name,
  fieldType,
  defaultValue,
  list,
  options,
  vertical = false,
  required,
  checkByKey = '', // if you dont want to check by boolean
  className,
  fieldClassName = '',
  formatCheckedOption = (item) => item,
  formatValue,
  rpaData = {},
  onChange,
  isFast,
  tabId,
  readOnly,
  ...props
}) => {
  const css = useClass(styles);

  const checkBoxProps = {
    styles: props.styles,
    className: cn(
      className,
      { [css.listStyle]: fieldType === PROGRESS_NOTE_VIEW.LIST },
      { [css.checkboxReadonly]: readOnly },
    ),
    ...props,
  };

  const renderCheckBoxInList = (value, setFieldValue, item, index) => {
    const labelContent = item?.isHtml ? (
      <div dangerouslySetInnerHTML={{ __html: item?.text }} />
    ) : (
      item?.text
    );

    return (
      <Fragment key={item?.key}>
        <Checkbox
          checked={
            (formatValue ? formatValue(value, item, index) : value?.[index]?.key === item?.key) ||
            false
          }
          label={labelContent}
          rpaData={{ id: `${name}[${index || 0}]`, title: item?.text, fieldName: name, ...rpaData }}
          {...checkBoxProps}
          onChange={
            readOnly
              ? undefined
              : (_, checked) =>
                  onChange
                    ? onChange({ name, index, item, value, checked, setFieldValue })
                    : setFieldValue(`${name}[${index}]`, formatCheckedOption(item, checked))
          }
        />
        {item.hasDetail && value?.includes(item.key) && <FieldText name={item.detailsName} />}
      </Fragment>
    );
  };

  const renderCheckbox = (value, setFieldValue, checkByKey) => {
    return (
      <Checkbox
        checked={
          (formatValue ? formatValue(value) : checkByKey ? checkByKey === value : Boolean(value)) ||
          false
        }
        rpaData={{ id: `${name}`, title: title || props?.label, fieldName: name, ...rpaData }}
        {...checkBoxProps}
        onChange={
          readOnly
            ? undefined
            : (_, checked) =>
                onChange
                  ? onChange({ value, setFieldValue, checked, name })
                  : setFieldValue(name, checkByKey || formatCheckedOption(checked))
        }
      />
    );
  };

  const opts = list || options;

  return (
    //using key to mount again if bgImage change cause component not update when value dont change
    <MyField name={name} isFast={isFast}>
      {({ field: { value }, form, meta }) => {
        return (
          <FieldLayout
            title={title}
            type={fieldType}
            value={value}
            required={required}
            name={name}
            form={form}
            meta={meta}
            tabId={tabId}
            className={fieldClassName}
            readOnly={readOnly}
          >
            {opts ? (
              <div className={cn(css.listCheckbox, { [css.listCheckboxVertical]: vertical })}>
                {opts.map((item, index) =>
                  renderCheckBoxInList(value, form.setFieldValue, item, index),
                )}
              </div>
            ) : (
              renderCheckbox(value, form.setFieldValue, checkByKey)
            )}
          </FieldLayout>
        );
      }}
    </MyField>
  );
};

export default FieldCheckBox;
