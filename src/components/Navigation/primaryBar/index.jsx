import React, { useMemo } from 'react';
import { TEST_ID } from 'constants/testId';
import { useSelector } from 'react-redux';
import SideBar from 'components/SideBar';
import NavigationItem from '../navigationItem';
import useNavigator from 'hooks/useNavigator';
import orderBy from 'lodash/orderBy';
import { useClass } from 'hooks';
import { ROUTE_NAME } from 'constants/routes';
import useRequest from 'hooks/useRequest';
import { API_DYNAMIC_FORM, API_DYNAMIC_PROGRESS_NOTE } from 'constants/urlRequest';
import { ORDER_DIRECTION } from 'constants/index';

const PrimaryBar = ({ setForceShow }) => {
  const css = useClass(styles);
  const { navigate, pathname, searchParams } = useNavigator();
  const items = useSelector((state) => state.settings.navigationItems);

  const { data: dynamicFormData } = useRequest({
    key: 'DYNAMIC_FORM_NAVIGATION',
    url: API_DYNAMIC_FORM.DEFAULT,
    params: {
      PageSize: 30,
      Page: 0,
      OrderBy: 'createDate',
      OrderDirection: ORDER_DIRECTION.DESC,
      IncludedVersion: 'V2',
      Status: 'Complete',
    },
  });

  const { data: dynamicProgressNoteData } = useRequest({
    key: 'DYNAMIC_PROGRESS_NOTE_NAVIGATION',
    url: API_DYNAMIC_PROGRESS_NOTE.DEFAULT,
    params: {
      OrderBy: 'createDate',
      PageSize: 30,
      Page: 0,
      OrderDirection: ORDER_DIRECTION.DESC,
      Status: 'Complete',
    },
    requiredParams: {
      PatientId: searchParams.patientId,
    },
  });

  const activatedItems = useMemo(() => {
    let cloneItems = [...items];

    const isAllowDF = items?.find((item) => item?.key === ROUTE_NAME.DYNAMIC_FORM);
    if (isAllowDF) {
      cloneItems.push({
        key: ROUTE_NAME.DYNAMIC_FORMS,
        path: ROUTE_NAME.DYNAMIC_FORMS,
        text: 'Dynamic Forms',
        suffixIconProps: { iconName: 'ChevronRight' },
        subMenu: (dynamicFormData?.items || []).map((item) => ({
          key: item?.formId,
          text: item?.formName,
          onClick: () => navigate({ url: `${ROUTE_NAME.DYNAMIC_FORM_VIEWER}/${item.formId}` }),
        })),
      });
    }

    const progressNoteItem = items?.find((item) => item?.key === ROUTE_NAME.DYNAMIC_PROGRESS_NOTE);
    if (progressNoteItem) {
      cloneItems = cloneItems.filter((item) => item?.key !== ROUTE_NAME.DYNAMIC_PROGRESS_NOTE);
      cloneItems.push({
        key: ROUTE_NAME.DYNAMIC_PROGRESS_NOTE,
        path: ROUTE_NAME.DYNAMIC_PROGRESS_NOTE,
        text: 'Dynamic Progress Notes',
        suffixIconProps: { iconName: 'ChevronRight' },
        subMenu: (progressNoteItem?.subMenu || []).concat(
          (dynamicProgressNoteData?.items || []).map((item) => ({
            key: item?.formId,
            text: item?.formName,
            onClick: () =>
              navigate({ url: `${ROUTE_NAME.DYNAMIC_PROGRESS_NOTE_VIEWER}/${item.formId}` }),
          })),
        ),
      });
    }

    return orderBy(
      cloneItems.map((item) => ({ ...item, suffixIconProps: { iconName: 'ChevronRight' } })),
      [(item) => item?.text?.toLowerCase()],
      ['asc'],
    );
  }, [items, dynamicFormData, dynamicProgressNoteData]);

  const getActiveIndex = (item) =>
    item?.path === pathname ||
    (pathname.startsWith(ROUTE_NAME.BATCH) && item?.path === ROUTE_NAME.BATCH) ||
    (pathname.startsWith(ROUTE_NAME.ANALYTIC) && item?.path === ROUTE_NAME.ANALYTIC);

  const onClick = (item) => {
    if (item?.onClick) {
      item.onClick();
      return;
    }
    if (getActiveIndex(item)) return;

    navigate({
      clearSearchParams: true,
      pathname: item?.path,
      search: { patientId: searchParams.patientId },
      hash: '',
    });
  };

  return (
    <div className={css.wrapper} data-testid={TEST_ID.NAVIGATION.PRIMARY_BAR_WRAPPER}>
      <SideBar
        className={css.sidebarWrapper}
        withSearch
        items={activatedItems}
        render={(items) =>
          items.map((item) => (
            <div key={item?.key} className="nav-sidebar-hover-item">
              <NavigationItem
                itemId={item?.key}
                key={item?.key}
                checked={getActiveIndex(item)}
                data-isbutton="button"
                onClick={() => onClick(item)}
                setForceShow={setForceShow}
                isHoverable
                {...item}
                iconProps={{}}
              />
            </div>
          ))
        }
      />
    </div>
  );
};

export default PrimaryBar;

const styles = () => ({
  wrapper: {
    position: 'relative',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    height: '100%',

    '.nav-sidebar-hover-item': {
      height: 36,
    },
  },

  sidebarWrapper: {
    height: '100%',

    '.ms-SearchBox': {
      margin: 4,
      border: 'none',
      color: 'black',
    },
  },
});
