import React, { useEffect, useState, useCallback, useContext } from 'react';
import { ActionButton, DirectionalHint, IconButton, Stack } from '@fluentui/react';
import cn from 'classnames';
import useCalculateMoreItems from 'hooks/useCalculateMoreItems';
import useClass from 'hooks/useClass';
import useModal from 'hooks/useModal';
import useNavigator from 'hooks/useNavigator';
import useRequest from 'hooks/useRequest';
import useSetting from 'hooks/useSetting';
import { useDispatch, useSelector } from 'react-redux';
import { t } from 'utils/string';
import { setGlobalSettings, setNavigationSettings } from 'store/actions/settings';
import debounce from 'lodash/debounce';
import { SETTING_KEYS } from 'constants/settingKeys';
import SelectorPrimary from 'components/Selector';
import { TabsSetForm } from 'components/Setting/components';
import { MODAL_SIZE } from 'constants/modal';
import LoadingWrapper from 'components/Loading/LoadingWrapper';
import { ROUTE_NAME } from 'constants/routes';
import { API_GLOBAL_TABS_SET } from 'constants/urlRequest';
import { SplitScreenContext } from 'contexts/SplitScreenContext';
import { orderBy } from 'lodash';

const NAVIGATION_ITEM_ID = 'NAVIGATION_ITEM_ID';

const HorizontalBar = () => {
  const { showModal } = useModal();
  const css = useClass(styles);
  const dispatch = useDispatch();
  const { navigate, pathname, searchParams } = useNavigator();

  const isSetLoading = useSelector(({ settings }) => settings.isSetLoading);
  const items = useSelector((state) => state.settings.navigationItems);
  const settings = useSelector((state) => state.settings.settings);
  const [listItem, setListItem] = useState(items);
  const { isSplitScreen } = useContext(SplitScreenContext);

  const [tabDisplay, tabSet = [], tabSetActive] = useSetting(
    SETTING_KEYS.NAVIGATION_DETAIL,
    SETTING_KEYS.NAVIGATION_TABS_SET,
    SETTING_KEYS.TABS_SET_ACTIVE,
  );

  const { data = [], loading } = useRequest({
    key: [API_GLOBAL_TABS_SET.DEFAULT(), 'me'],
    url: API_GLOBAL_TABS_SET.DEFAULT(),
    params: { showAll: false },
    shouldCache: true,
  });

  const tabSetKey = tabSetActive || tabSet?.find((i) => i?.active)?.key;
  const activeTabSet = [...data, ...tabSet].find((i) => i?.key === tabSetKey);

  const getActiveIndex = (item) =>
    item?.path === pathname ||
    (pathname.startsWith(ROUTE_NAME.BATCH) && item?.path === ROUTE_NAME.BATCH) ||
    (pathname.startsWith(ROUTE_NAME.ANALYTIC) && item?.path === ROUTE_NAME.ANALYTIC);

  const { ref, visibleItems, invisibleItems } = useCalculateMoreItems({
    items: listItem,
    getActiveIndex,
    itemSpacing: 20,
    moreSpacing: 280,
    lastInLastOut: false,
  });

  const onClick = (item) => {
    if (item?.onClick) {
      item.onClick();
      return;
    }
    if (getActiveIndex(item)) return;

    navigate({
      clearSearchParams: true,
      pathname: item?.path,
      search: { patientId: searchParams.patientId },
      hash: '',
    });
  };

  const onChangeTab = (key) => {
    const set = tabSet?.map((i) => ({ ...i, active: false }));

    dispatch(
      setGlobalSettings(
        {
          ...settings,
          [SETTING_KEYS.NAVIGATION_TABS_SET]: set,
          [SETTING_KEYS.TABS_SET_ACTIVE]: key,
        },
        { noToast: true },
      ),
    );
  };

  const handleSubmitChangeListNavigation = useCallback(
    debounce(
      (submitListNavigationItem) => dispatch(setNavigationSettings(submitListNavigationItem)),
      500,
    ),
    [],
  );

  const calculateListItems = [...visibleItems, ...invisibleItems].filter((i) => !!i?.key);

  useEffect(() => {
    setListItem(calculateListItems);
    if (tabDisplay && activeTabSet) return;

    //save list to BE
    handleSubmitChangeListNavigation(calculateListItems);
  }, [JSON.stringify(calculateListItems.map((item) => item?.key)), tabDisplay, activeTabSet]);

  useEffect(() => {
    if (tabDisplay && activeTabSet && !!activeTabSet.pages?.length) {
      const PAGE = items.reduce((acc, item) => ({ ...acc, [item.key]: item }), {});
      const formatItems = activeTabSet?.pages?.map((t) => {
        if (!t?.dynamicId) return PAGE[t?.key];

        return {
          isCardViewEnable: true,
          showCardView: true,
          subMenu: [],
          key: t?.key,
          text: t?.dynamicId
            ? t?.text?.replace(
                new RegExp('Dynamic Form - |Dynamic Service Plan -|Dynamic Progress Note -', 'g'),
                '',
              )
            : t?.text,
          path: t?.key,
          pageId: t?.dynamicId,
        };
      });
      setListItem(formatItems);
    } else {
      setListItem(items);
    }
  }, [JSON.stringify(items.map((item) => item?.key)), tabDisplay, activeTabSet]);

  return (
    <div ref={ref} className={cn(css.wrapper, { [css.hide]: isSplitScreen })}>
      <Stack horizontal className={css.stack}>
        <div className={css.borderWrapper} style={{ width: 16 }} />
        <div className={cn('child-item', css.cmClass.borderColor)}>
          {visibleItems?.map((item, index) => (
            <div
              key={item?.key}
              className={cn('item-wrapper', css.cmClass.backgroundGrey300, {
                active: getActiveIndex(item),
              })}
            >
              <div
                id={`${NAVIGATION_ITEM_ID}-${index}`}
                data-isbutton="button"
                onClick={() => onClick(item)}
                className="item"
                role="button"
                tabIndex={0}
              >
                <span>{item?.text}</span>
              </div>
            </div>
          ))}
        </div>
        <div className={cn('flex-1', css.borderWrapper)} />
        {!!invisibleItems.length && (
          <SelectorPrimary
            className={cn(css.btnTabsSet, css.btnMore)}
            showClear={false}
            options={orderBy(invisibleItems, (page) => page.text.toLowerCase())}
            textFieldProps={{ value: t('More') }}
            defaultWidth={300}
            onChange={onClick}
            calloutProps={{ directionalHint: DirectionalHint.leftTopEdge }}
          />
        )}
        {!!tabDisplay && (
          <React.Fragment>
            <div className={css.borderWrapper} style={{ width: 8 }} />
            <IconButton
              iconProps={{ iconName: 'Edit' }}
              className={cn(css.cmClass.border, css.btnEdit)}
              data-isbutton="button"
              onClick={() => showModal({ content: <TabsSetForm isEdit />, size: MODAL_SIZE.SMALL })}
            />
            <div className={css.borderWrapper} style={{ width: 8 }} />
            <LoadingWrapper loading={isSetLoading || loading}>
              <SelectorPrimary
                className={css.btnTabsSet}
                showClear={false}
                options={[...data, ...tabSet]}
                value={tabSetKey}
                textFieldProps={{ value: t('Tabs Set') }}
                defaultWidth={250}
                onChange={onChangeTab}
                formatData={(d) => d?.key}
                renderLabel={(item) => (
                  <React.Fragment>
                    <section>{item?.text}</section>
                    <span className={cn('text-12-16', css.cmClass.colorSecondary)}>
                      {item?.description}
                    </span>
                  </React.Fragment>
                )}
                onRenderFooter={({ setHideSelector }) => (
                  <ActionButton
                    className={css.btnAddTabsSet}
                    iconProps={{ iconName: 'Add' }}
                    data-isbutton="button"
                    onClick={() => {
                      setHideSelector();
                      showModal({ content: <TabsSetForm />, size: MODAL_SIZE.SMALL });
                    }}
                  >
                    {t('Add New Tabs Set')}
                  </ActionButton>
                )}
              />
            </LoadingWrapper>
          </React.Fragment>
        )}
        <div className={css.borderWrapper} style={{ width: 16 }} />
      </Stack>
    </div>
  );
};

export default HorizontalBar;

const styles = (theme) => ({
  wrapper: {
    width: '100%',
  },
  hide: {
    display: 'none',
  },
  stack: {
    display: 'flex',
    flexDirection: 'row',
    overflow: 'hidden',

    '.item-wrapper': {
      marginRight: 5,
      borderRadius: '2px 2px 0 0',
      border: `1px solid ${
        theme.darkMode
          ? theme.palette.backgroundColorShade4
          : theme.highContrast
          ? theme.custom.grey1000
          : theme.custom.grey400
      }`,
      borderBottom: 'none',
    },

    '.item-wrapper.active': {
      borderBottom: 'none',
      borderTop: 'none',
      position: 'relative',

      ':before': {
        position: 'absolute',
        backgroundColor: theme.semanticColors.bodyBackground,
        content: '""',
        height: 2,
        width: '100%',
        bottom: -1,
      },

      '.item': {
        color: theme.darkMode ? '#FFFFFF' : theme.custom.grey700,
        borderTop: `3px solid ${theme.palette.primaryColor} !important`,
        padding: '5px 8px 8px',
        backgroundColor: theme.semanticColors.bodyBackground,

        span: {
          fontWeight: 600,
          color: theme.semanticColors.bodyText,
        },
      },
    },

    '.item': {
      borderRadius: '2px 2px 0 0',
      padding: 7,
      cursor: 'pointer',

      span: {
        whiteSpace: 'nowrap',
      },
    },

    '.item:hover': {
      backgroundColor: theme.semanticColors.buttonBackgroundHovered,
    },

    '.child-item': {
      display: 'flex',
      height: 36,
      boxSizing: 'border-box',
      borderBottom: `1px solid`,
    },
  },

  btnEdit: {
    height: 36,
    width: 34,
    backgroundColor: theme.darkMode ? 'rgba(255, 255, 255, 0.10)' : theme.palette.backgroundColor,
    borderRadius: '2px 2px 0 0',
  },

  btnMore: {
    '&&& .ms-TextField': {
      width: 76,
    },
  },

  btnTabsSet: {
    '.ms-TextField': {
      width: 94,
    },
    '& > div, .ms-TextField-fieldGroup': {
      height: 36,
    },
    '& .ms-TextField-fieldGroup': {
      borderRadius: '2px 2px 0 0',
      backgroundColor: theme.darkMode ? 'rgba(255, 255, 255, 0.10)' : theme.palette.backgroundColor,
    },
    '& .ms-TextField-fieldGroup:hover': {
      backgroundColor: theme.semanticColors.buttonBackgroundHovered,
      borderColor: theme.darkMode ? theme.custom.white100 : theme.custom.grey300,
    },
  },

  btnAddTabsSet: {
    width: '100%',
    '.ms-Button-label, .ms-Button-icon, &:hover .ms-Button-icon': {
      color: theme.darkMode ? theme.palette.primaryColorShade8 : theme.palette.primaryColor,
    },
  },

  borderWrapper: {
    borderBottom: `1px solid ${
      theme.darkMode
        ? theme.custom.white100
        : theme.highContrast
        ? theme.custom.grey1000
        : theme.custom.grey300
    }`,
  },
});
