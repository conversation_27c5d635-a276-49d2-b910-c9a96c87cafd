import React, { useMemo, useState } from 'react';
import QuestionnaireForm from 'modules/Questionnaire/Form';
import ActiveQuestionnaires from 'modules/Questionnaire/ActiveQuestionnaires';
import QuestionnaireHistory from 'modules/Questionnaire/History';
import CollapseVertical from 'components/Collapse/CollapseVertical';
import { t } from 'utils/string';
import { MainContentWrapper } from '../components';
import useRequest from 'hooks/useRequest';
import useUser from 'hooks/useUser';
import { API_SECURITY_FORMS } from 'constants/urlRequest';

const Questionnaire = () => {
  const { info } = useUser();
  const [formDesc, setFormDesc] = useState('');
  const [editItem, setEditItem] = useState();

  const { data } = useRequest({
    key: `${API_SECURITY_FORMS.BY_USER}-${info?.username}`,
    url: API_SECURITY_FORMS.BY_USER,
    requiredParams: { userName: info?.username },
  });

  const security = useMemo(() => {
    return data?.find(
      (d) => d?.specificSecurityName === formDesc && d?.secTypeDesc === 'Questionnaire',
    );
  }, [data, formDesc]);

  console.log(data, security);

  return (
    <MainContentWrapper pageSections={[]} disabledPageSections title={t('Questionnaire')} pageName>
      <ActiveQuestionnaires setFormDesc={setFormDesc} />
      {!!formDesc && (
        <CollapseVertical title={formDesc} open className="mb-24">
          <QuestionnaireHistory formDesc={formDesc} setEditItem={setEditItem} security={security} />
          <QuestionnaireForm
            formDesc={formDesc}
            editItem={editItem}
            setEditItem={setEditItem}
            security={security}
          />
        </CollapseVertical>
      )}
    </MainContentWrapper>
  );
};

export default Questionnaire;
