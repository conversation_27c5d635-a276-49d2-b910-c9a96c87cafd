import React, { useCallback } from 'react';
import { MainContentWrapper } from '../../components';
import { t } from 'utils/string';
import useRequest from 'hooks/useRequest';
import { API_DYNAMIC_FORM } from 'constants/urlRequest';
import { ORDER_DIRECTION } from 'constants/index';
import QUERY_KEY from 'constants/queryKey';
import Table from 'components/Table';
import useClass from 'hooks/useClass';
import { SearchBox, Stack } from '@fluentui/react';
import { debounceFunc } from 'utils/time';
import { formatToFluentOptions } from 'utils';
import SelectorFilter from 'components/Selector/SelectorFilter';
import useNavigator from 'hooks/useNavigator';
import { ROUTE_NAME } from 'constants/routes';

const DynamicForm = () => {
  const css = useClass(styles);
  const { navigate } = useNavigator();

  const { data, loading, params, updateParams } = useRequest({
    key: QUERY_KEY.DYNAMIC_FORM_COMPLETE,
    url: API_DYNAMIC_FORM.DEFAULT,
    params: {
      PageSize: 5,
      Page: 0,
      IncludedVersion: 'V2',
      Status: 'Complete',
      OrderBy: 'createDate',
      OrderDirection: ORDER_DIRECTION.DESC,
    },
    autoRefetch: true,
  });

  const apiCategories = useRequest({ url: API_DYNAMIC_FORM.CATEGORIES, shouldCache: true });

  const menuProps = (item) => ({
    items: [
      {
        key: 'view',
        text: t('View'),
        onClick: () => navigate({ url: `${ROUTE_NAME.DYNAMIC_FORM_VIEWER}/${item.formId}` }),
      },
    ],
  });

  const onSearch = useCallback(
    debounceFunc((SearchTerm) => updateParams({ SearchTerm, Page: 0 }), 500),
    [],
  );

  return (
    <MainContentWrapper
      pageSections={[]}
      pageName
      title={t('Dynamic Forms')}
      className={css.background}
    >
      <Stack horizontal tokens={{ childrenGap: 12 }} className="mb-16">
        <SearchBox
          placeholder={t('Search')}
          iconProps={{ iconName: 'Zoom' }}
          defaultValue={params?.SearchTerm}
          onChange={(_, e) => onSearch(e)}
        />
        <SelectorFilter
          ariaLabel={t('Dynamic Form Category')}
          placeholder={t('Category')}
          options={formatToFluentOptions(apiCategories?.data || [])}
          selectedKey={params?.category}
          onChange={(_, item) => updateParams({ category: item.key, Page: 0 })}
        />
      </Stack>
      <Table
        items={data?.items || []}
        totalItems={data?.totalItems}
        loading={loading}
        metadata={params}
        onMetadataChange={updateParams}
        columns={_columns}
        getMenuProps={menuProps}
      />
    </MainContentWrapper>
  );
};

export default DynamicForm;

const styles = (theme) => ({
  background: {
    backgroundColor: theme.semanticColors.bodyBackground,
    height: '100%',
    boxSizing: 'border-box',
  },
});

const _columns = [
  { name: 'Form Name', fieldName: 'formName', sortable: true },
  { name: 'Description', fieldName: 'description', sortable: true },
  { name: 'Category', fieldName: 'category', sortable: true },
  {
    name: 'Created Date',
    fieldName: 'createDate',
    sortable: true,
    renderItem: (i, _, c, render) =>
      render({ date: i.createDate, user: i.createUser, withTime: true }),
  },
  {
    name: 'Updated Date',
    fieldName: 'updateDate',
    sortable: true,
    renderItem: (i, _, c, render) =>
      render({ date: i.updateDate, user: i.createUser, withTime: true }),
  },
  { key: 'Action', name: 'Action', fieldName: 'Action' },
];
