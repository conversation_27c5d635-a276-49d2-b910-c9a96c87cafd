import React from 'react';
import { MainContentWrapper } from '../components';
import { usePageSections } from 'hooks';
import { ROUTE_NAME } from 'constants/routes';

const FundingSource = () => {
  const { pageSections, mapContent } = usePageSections({ key: ROUTE_NAME.FUNDING_SOURCE });
  return (
    <MainContentWrapper pageSections={pageSections} pageName>
      {mapContent()}
    </MainContentWrapper>
  );
};

export default FundingSource;
