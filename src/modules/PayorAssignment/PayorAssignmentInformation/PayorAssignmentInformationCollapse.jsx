import React, { useContext, useEffect, useMemo, useState } from 'react';
import CollapseVertical from 'components/Collapse/CollapseVertical';
import { useNavigator, useRequest } from 'hooks';
import { useFormikContext } from 'formik';
import PolicyInformation from './PolicyInformation';
import ServiceType from './ServiceType';
import { API_METHOD, API_RCM_PAYOR_ASSIGNMENT } from 'constants/urlRequest';
import AdditionalInformation from './AdditionalInformation';
import Comment from './Comment';
import { handleValidationBE } from 'utils/form';
import { toast } from 'react-toastify';
import { Stack, Text } from '@fluentui/react';
import { TEXT_TEMPLATE } from 'constants/texts';
import { fetchByQueryKey } from 'apis/queryClient';
import QUERY_KEY from 'constants/queryKey';
import { v4 as uuidv4 } from 'uuid';
import { t } from 'utils/string';
import FormUndo from 'components/FormUndo';
import { FORM_ID } from 'constants/formId';
import FormTemplate from 'components/FormUndo/FormTemplate';
import { removeUndefinedFromObject } from 'utils';
import { PayorAssignmentContext } from 'contexts/PayorAssignmentContext';
import ELEMENT_ID from 'constants/elementId';
import { globalScrollToId } from 'utils/globalScroll';
import LoadingWrapper from 'components/Loading/LoadingWrapper';
import FundingSourceHistory from 'modules/FundingSource/FundingSourceHistory';

export const PayorAssignmentInformationContext = React.createContext({});

const CheckingData = ({ initValues, serviceTypes }) => {
  const { editId: detailId } = useContext(PayorAssignmentContext);
  const { setValues } = useFormikContext();
  const { searchParams } = useNavigator();

  const api = useRequest({
    url: `${API_RCM_PAYOR_ASSIGNMENT.DEFAULT}/${detailId}`,
    requiredParams: { patientId: searchParams.patientId },
    enabled: !!detailId,
  });

  useEffect(() => {
    setValues(
      api.data
        ? {
            ...api.data,
            authCodes: api?.data?.authCodesDetails?.map((item) => ({
              ...item,
              key: uuidv4(),
              serviceCodes: item.authcodes?.map((i) => ({
                key: i.serviceId,
                text: i.serviceName,
              })),
              authcodes: undefined,
            })),
            authCodesDetails: undefined,
            serviceTypes: serviceTypes.map((i) => {
              const serviceType = (api.data?.policyDetails || []).find((j) => j.code === i.key);
              return {
                ...i,
                code: i.key,
                ...(serviceType && { ...serviceType, selected: true }),
              };
            }),
          }
        : initValues,
    );
  }, [api.data]);

  return null;
};

function PayorAssignmentInformationCollapse({ onAfterSave }) {
  const { setEditId } = useContext(PayorAssignmentContext);
  const { searchParams } = useNavigator();
  const [updateAuthorizationCodeItem, setUpdateAuthorizationCodeItem] = useState();

  const { loading, data } = useRequest({
    url: API_RCM_PAYOR_ASSIGNMENT.SERVICE_TYPES,
    shouldCache: true,
  });

  const initValues = useMemo(
    () => ({
      patientId: searchParams.patientId,
      guarantor: 1,
      benefitsAssigned: 1,
      consentOnFile: 1,
      relation: 1,
      serviceTypes: data?.map((i) => ({ code: i.key, description: i.description })),
    }),
    [data, searchParams.patientId],
  );

  const apiInputData = useRequest({
    url: API_RCM_PAYOR_ASSIGNMENT.INPUT_DATA,
    shouldCache: true,
  });

  const apiSave = useRequest({
    url: API_RCM_PAYOR_ASSIGNMENT.DEFAULT,
    method: API_METHOD.POST,
  });

  const clearForm = (form) => {
    form.setValues(initValues);
    setEditId();
    setUpdateAuthorizationCodeItem({ timeStamp: new Date() });
  };

  const submit = (values, form) => {
    apiSave.mutateAsync(
      {
        ...removeUndefinedFromObject(values),
        authCodes: (values?.authCodes || []).map((item) => ({
          ...item,
          serviceCodes: item.serviceCodes?.map((code) => code.key),
        })),
        serviceTypes: values.serviceTypes.filter((i) => i.selected),
      },
      {
        onError: (err) => handleValidationBE(form, err),
        onSuccess: () => {
          toast.success(<Text>{TEXT_TEMPLATE.SAVE_SUCCESSFULLY()}</Text>);
          clearForm(form);
          globalScrollToId(ELEMENT_ID.PAYOR_ASSIGNMENT.HISTORY);
          fetchByQueryKey(QUERY_KEY.PAYOR_ASSIGNMENT_HISTORY);
          onAfterSave?.();
        },
      },
    );
  };

  return (
    <PayorAssignmentInformationContext.Provider value={{ apiInputData }}>
      <FormUndo
        initialValues={initValues}
        onSubmit={submit}
        enableReinitialize
        formId={FORM_ID.PAYOR_ASSIGNMENT}
        autoShowFormTemplate={false}
      >
        <CollapseVertical
          id={ELEMENT_ID.PAYOR_ASSIGNMENT.INFORMATION}
          open
          title={t('Payor Assignment Information')}
          className="mb-24"
        >
          <LoadingWrapper loading={loading || apiSave.loading}>
            <Stack tokens={{ childrenGap: 16 }}>
              <FormTemplate />
              <CheckingData initValues={initValues} serviceTypes={data || []} />
              <PolicyInformation />
              <FundingSourceHistory title={t('Associated Funding Source')} />
              <ServiceType
                updateAuthorizationCodeItem={updateAuthorizationCodeItem}
                setUpdateAuthorizationCodeItem={setUpdateAuthorizationCodeItem}
                serviceTypes={data || []}
              />
              <AdditionalInformation />
              <Comment clearForm={clearForm} />
            </Stack>
          </LoadingWrapper>
        </CollapseVertical>
      </FormUndo>
    </PayorAssignmentInformationContext.Provider>
  );
}

export default PayorAssignmentInformationCollapse;
