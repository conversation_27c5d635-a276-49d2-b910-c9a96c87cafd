import React from 'react';
import { DefaultButton, Stack } from '@fluentui/react';
import ModalLayout from 'components/GlobalModal/ModalLayout';
import { useModal } from 'hooks';
import FundingSourceHistory from 'modules/FundingSource/FundingSourceHistory';
import FundingSourceInput from 'modules/FundingSource/FundingSourceInput';
import { FormEditProvider } from 'contexts/FormEditContext';
import { t } from 'utils/string';

const FundingSourceModal = ({ payorAssignmentId }) => {
  const { hideModal } = useModal();

  return (
    <FormEditProvider>
      <ModalLayout
        title={t('Funding Source')}
        footerRightSide={<DefaultButton text={t('Close')} onClick={hideModal} />}
      >
        <Stack tokens={{ padding: 16 }}>
          <FundingSourceHistory />
          <FundingSourceInput />
        </Stack>
      </ModalLayout>
    </FormEditProvider>
  );
};

export default FundingSourceModal;
