import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PrimaryButton, Dropdown } from '@fluentui/react';
import { useModal, useNavigator, useRequest } from 'hooks';
import ModalLayout from 'components/GlobalModal/ModalLayout';
import Table from 'components/Table';
import { Formik } from 'formik';
import FieldText from 'components/Form/FieldText';
import FieldDate from 'components/Form/FieldDate';
import FieldDropdown from 'components/Form/FieldDropdown';
import { t } from 'utils/string';
import { toast } from 'react-toastify';
import { TEXT_TEMPLATE } from 'constants/texts';
import CollapseVertical from 'components/Collapse/CollapseVertical';

const FundingSourceModal = ({ payorAssignmentId }) => {
  const { searchParams } = useNavigator();
  const { hideModal } = useModal();

  // Mock data for demonstration - replace with actual API calls
  const { data: historyData, loading: historyLoading } = useRequest({
    url: '/api/funding-source/history', // Replace with actual endpoint
    params: { patientId: searchParams.patientId, payorAssignmentId },
    enabled: false, // Enable when you have the actual API
  });

  const fundingSourceOptions = [
    { key: 'medicaid', text: 'Medicaid' },
    { key: 'medicare', text: 'Medicare' },
    { key: 'private', text: 'Private Insurance' },
    { key: 'self-pay', text: 'Self Pay' },
    { key: 'other', text: 'Other' },
  ];

  const authorizationOptions = [
    { key: 'pending', text: 'Pending' },
    { key: 'approved', text: 'Approved' },
    { key: 'denied', text: 'Denied' },
  ];

  const statusOptions = [
    { key: 'active', text: 'Active' },
    { key: 'inactive', text: 'Inactive' },
    { key: 'pending', text: 'Pending' },
  ];

  // Mock history data - replace with actual data
  const mockHistoryData = [
    {
      fundingSource: 'Medicaid',
      authorization: 'AUTH001',
      authorizationEntry: 'Entry001',
      effectiveDate: '2024-01-01',
      endDate: '2024-12-31',
      status: 'Approved',
      comment: 'Initial authorization',
      action: 'Select',
    },
    {
      fundingSource: 'Medicare',
      authorization: 'AUTH002',
      authorizationEntry: 'Entry002',
      effectiveDate: '2024-02-01',
      endDate: '2024-11-30',
      status: 'Pending',
      comment: 'Renewal pending',
      action: 'Select',
    },
  ];

  const historyColumns = [
    { key: 'fundingSource', name: 'Funding Source', fieldName: 'fundingSource', minWidth: 120 },
    { key: 'authorization', name: 'Authorization', fieldName: 'authorization', minWidth: 120 },
    {
      key: 'authorizationEntry',
      name: 'Authorization Entry',
      fieldName: 'authorizationEntry',
      minWidth: 140,
    },
    { key: 'effectiveDate', name: 'Effective Date', fieldName: 'effectiveDate', minWidth: 120 },
    { key: 'endDate', name: 'End Date', fieldName: 'endDate', minWidth: 100 },
    {
      key: 'status',
      name: 'Status',
      fieldName: 'status',
      minWidth: 100,
      renderItem: ({ status }) => (
        <span className={`status-badge ${status?.toLowerCase()}`}>{status}</span>
      ),
    },
    { key: 'comment', name: 'Comment', fieldName: 'comment', minWidth: 150 },
    {
      key: 'action',
      name: 'Action',
      fieldName: 'action',
      minWidth: 100,
      renderItem: () => (
        <Dropdown
          placeholder="Select"
          options={[
            { key: 'edit', text: 'Edit' },
            { key: 'delete', text: 'Delete' },
            { key: 'view', text: 'View' },
          ]}
          styles={{ root: { minWidth: 80 } }}
        />
      ),
    },
  ];

  const initialValues = {
    fundingSource: '',
    authorization: '',
    authorizationCodeOverride: '',
    startDate: '',
    endDate: '',
    status: '',
    comment: '',
  };

  const handleSubmit = async (values, { resetForm }) => {
    try {
      // Replace with actual API call
      console.log('Submitting funding source:', values);
      toast.success(TEXT_TEMPLATE.SAVE_SUCCESS('Funding Source'));
      resetForm();
      hideModal();
    } catch (error) {
      toast.error(TEXT_TEMPLATE.SAVE_ERROR('Funding Source'));
    }
  };

  const tabContent = [
    {
      id: 'history',
      text: t('Funding Source History'),
      child: (
        <Table
          loading={historyLoading}
          columns={historyColumns}
          items={mockHistoryData}
          pagination={false}
          allowScrollX={true}
        />
      ),
    },
    {
      id: 'input',
      text: t('Funding Source Input'),
      child: (
        <Formik initialValues={initialValues} onSubmit={handleSubmit} enableReinitialize>
          {({ submitForm, resetForm }) => (
            <Stack tokens={{ childrenGap: 16 }}>
              <Stack horizontal tokens={{ childrenGap: 16 }}>
                <FieldDropdown
                  name="fundingSource"
                  label="Funding Source"
                  placeholder="Select"
                  options={fundingSourceOptions}
                  required
                  styles={{ root: { flex: 1 } }}
                />
                <FieldDropdown
                  name="authorization"
                  label="Authorization"
                  placeholder="Select"
                  options={authorizationOptions}
                  styles={{ root: { flex: 1 } }}
                />
                <FieldText
                  name="authorizationCodeOverride"
                  label="Authorization Code Override"
                  styles={{ root: { flex: 1 } }}
                />
              </Stack>

              <Stack horizontal tokens={{ childrenGap: 16 }}>
                <FieldDate
                  name="startDate"
                  label="Start Date"
                  placeholder="mm/dd/yyyy"
                  styles={{ root: { flex: 1 } }}
                />
                <FieldDate
                  name="endDate"
                  label="End Date"
                  placeholder="mm/dd/yyyy"
                  styles={{ root: { flex: 1 } }}
                />
                <FieldDropdown
                  name="status"
                  label="Status"
                  placeholder="Select"
                  options={statusOptions}
                  styles={{ root: { flex: 1 } }}
                />
              </Stack>

              <FieldText name="comment" label="Comment" multiline rows={4} resizable={false} />

              <Stack horizontal tokens={{ childrenGap: 8 }} horizontalAlign="start">
                <PrimaryButton text={t('Submit')} onClick={submitForm} />
                <DefaultButton text={t('Cancel')} onClick={resetForm} />
              </Stack>
            </Stack>
          )}
        </Formik>
      ),
    },
  ];

  return (
    <ModalLayout
      title={t('Funding Source')}
      footerRightSide={<DefaultButton text={t('Close')} onClick={hideModal} />}
    >
      <Stack tokens={{ childrenGap: 16, padding: 16 }}>
        <CollapseVertical>
        </CollapseVertical>
        <Table
          loading={historyLoading}
          columns={historyColumns}
          items={mockHistoryData}
          pagination={false}
          allowScrollX={true}
        />
      </Stack>
    </ModalLayout>
  );
};

export default FundingSourceModal;
