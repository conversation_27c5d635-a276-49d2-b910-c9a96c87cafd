import { DefaultButton, Stack } from '@fluentui/react';
import ModalLayout from 'components/GlobalModal/ModalLayout';
import { useModal } from 'hooks';
import FundingSourceHistory from 'modules/FundingSource/FundingSourceHistory';
import FundingSourceInput from 'modules/FundingSource/FundingSourceInput';
import { t } from 'utils/string';

const FundingSourceModal = ({ payorAssignmentId }) => {
  const { hideModal } = useModal();

  return (
    <ModalLayout
      title={t('Funding Source')}
      footerRightSide={<DefaultButton text={t('Close')} onClick={hideModal} />}
    >
      <Stack tokens={{ padding: 16 }}>
        <FundingSourceHistory />
        <FundingSourceInput />
      </Stack>
    </ModalLayout>
  );
};

export default FundingSourceModal;
