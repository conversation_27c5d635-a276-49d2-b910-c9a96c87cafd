import React, { useContext, useCallback, useState } from 'react';
import { Stack, SearchBox, DefaultButton, Dropdown } from '@fluentui/react';
import { useClass, useRequest, useNavigator, useModal } from 'hooks';
import Table from 'components/Table';
import PerfectScroll from 'utils/PerfectScroll';
import debounce from 'lodash/debounce';
import payorAssignmentHistoryColumns from './_column';
import Text from 'components/Text';
import CustomFieldModal from 'modules/Batch/components/CustomFieldModal';
import { MODAL_SIZE } from 'constants/modal';
import ModalConfirm from 'components/Modal/ModalConfirm';
import { TEXT_TEMPLATE } from 'constants/texts';
import queryClient from 'apis/queryClient';
import QUERY_KEY from 'constants/queryKey';
import { toast } from 'react-toastify';
import { API_RCM_PAYOR_ASSIGNMENT, API_METHOD } from 'constants/urlRequest';
import AssociatedModal from './Associated';
import { ColumnCell } from './_column';
import { t } from 'utils/string';
import { onPrint } from 'utils/file';
import { PayorAssignmentContext } from 'contexts/PayorAssignmentContext';
import { globalScrollToId } from 'utils/globalScroll';
import ELEMENT_ID from 'constants/elementId';
import ModalViewItem from 'components/Modal/ModalView/ModalViewItem';
import FundingSourceModal from './FundingSourceModal';

const StatusOptions = [
  { key: '', text: t('All') },
  { key: true, text: t('Active') },
  { key: false, text: t('Inactive') },
];

const PayorAssignmentHistoryContent = ({ onAfterEdit, isRcm = false }) => {
  const css = useClass(styles);
  const { searchParams } = useNavigator();
  const { setEditId } = useContext(PayorAssignmentContext);

  const [columns, setColumns] = useState(payorAssignmentHistoryColumns);
  const { showModal } = useModal();
  const [isSectionView, setIsSectionView] = useState(false);

  const { data, loading, updateParams, params, refetch } = useRequest({
    key: QUERY_KEY.PAYOR_ASSIGNMENT_HISTORY,
    url: API_RCM_PAYOR_ASSIGNMENT.DEFAULT,
    params: { Page: 0, PageSize: 5, isActive: true, OrderBy: 'createDate', OrderDirection: 'DESC' },
    requiredParams: { patientId: searchParams.patientId },
    autoRefetch: true,
  });

  const onSearch = useCallback(
    debounce((searchTerm) => updateParams({ searchTerm, Page: 0 }), 500),
    [],
  );

  const apiPayorRequestDelete = useRequest({
    key: API_RCM_PAYOR_ASSIGNMENT.DEFAULT + 'delete',
    url: API_RCM_PAYOR_ASSIGNMENT.DEFAULT,
    method: API_METHOD.DELETE,
    enabled: false,
  });

  const apiPayorRequestVerify = useRequest({
    key: API_RCM_PAYOR_ASSIGNMENT.DEFAULT + 'verify',
    url: API_RCM_PAYOR_ASSIGNMENT.DEFAULT,
    method: API_METHOD.POST,
    enabled: false,
  });

  const showTableDisplayModal = () =>
    showModal({
      content: <CustomFieldModal fields={columns} setFields={setColumns} />,
      size: MODAL_SIZE.X_SMALL,
      allowOutsideClick: true,
    });

  const showAssociatedFilesModal = (item) =>
    showModal({
      content: <AssociatedModal payorAssignmentId={item?.payorAssignmentId} />,
      size: MODAL_SIZE.MEDIUM,
      allowOutsideClick: true,
    });

  const showFundingSourceModal = (item) =>
    showModal({
      content: <FundingSourceModal payorAssignmentId={item?.payorAssignmentId} />,
      allowOutsideClick: true,
    });

  const showModalView = (id, isAuthor) => {
    showModal({
      content: (
        <ModalViewItem
          url={`${API_RCM_PAYOR_ASSIGNMENT.DEFAULT}/${id}/${
            isAuthor ? 'authorization/html' : 'html'
          }`}
          title={isAuthor ? t('View Authorization') : t('View')}
          params={{ patientId: searchParams?.patientId }}
          itemKey={`payor-assignment-${id}`}
        />
      ),
      size: MODAL_SIZE.MEDIUM,
      allowOutsideClick: true,
    });
  };

  const filterColumns = columns.filter((i) => i?.active);

  const getMenu = () => ({
    items: [
      {
        key: 'verified',
        text: 'Verified',
        onClick: async (item) => {
          await apiPayorRequestVerify.request({
            url: `${API_RCM_PAYOR_ASSIGNMENT.DEFAULT}/${item?.payorAssignmentId}/verify`,
          });
          toast.success(<Text>{TEXT_TEMPLATE.VERIFY_SUCCESSFULLY(t('Payor assignment'))}</Text>);
        },
      },
      {
        key: 'edit',
        text: 'Edit',
        onClick: (item) => {
          setEditId(item?.payorAssignmentId);
          globalScrollToId(ELEMENT_ID.PAYOR_ASSIGNMENT.INFORMATION);
          onAfterEdit?.();
        },
      },
      {
        key: 'delete',
        text: 'Delete',
        onClick: (item) => {
          showModal({
            content: (
              <ModalConfirm
                message={TEXT_TEMPLATE.DELETE_CONFIRMATION('this Payor assignment')}
                onOk={async () => {
                  await apiPayorRequestDelete.request({
                    url: `${API_RCM_PAYOR_ASSIGNMENT.DEFAULT}/${item?.payorAssignmentId}`,
                  });
                  toast.success(
                    <Text>{TEXT_TEMPLATE.DELETE_SUCCESSFULLY('This Payor assignment')}</Text>,
                  );
                  queryClient.invalidateQueries([QUERY_KEY.PAYOR_ASSIGNMENT_HISTORY]);
                }}
              />
            ),
            size: MODAL_SIZE.X_SMALL,
            isReplace: false,
          });
        },
      },
      {
        key: 'associatedFiles',
        text: 'Associated Files',
        onClick: showAssociatedFilesModal,
      },
      {
        key: 'view',
        text: 'View',
        onClick: ({ payorAssignmentId }) => showModalView(payorAssignmentId, false),
      },
      {
        key: 'viewAuthorization',
        text: 'View Authorization',
        onClick: ({ payorAssignmentId }) => showModalView(payorAssignmentId, true),
      },
      {
        key: 'print',
        text: 'Print',
        onClick: async (item) => {
          onPrint({
            url: `${API_RCM_PAYOR_ASSIGNMENT.DEFAULT}/${item?.payorAssignmentId}/html`,
            queryString: {
              patientId: searchParams.patientId,
            },
          });
        },
      },
      {
        key: 'printAuthorization',
        text: 'Print Authorization',
        onClick: async (item) => {
          onPrint({
            url: `${API_RCM_PAYOR_ASSIGNMENT.DEFAULT}/${item?.payorAssignmentId}/authorization/html`,
            queryString: {
              patientId: searchParams.patientId,
            },
          });
        },
      },
      { key: 'fundingSource', text: 'Funding Source', onClick: showFundingSourceModal },
    ],
    shouldFocusOnMount: false,
  });

  const sectionViewColumn = [
    {
      key: 'payorInformation',
      name: 'Payor Information',
      fieldName: 'payorInformation',
      renderItem: (item, _, column) => <ColumnCell list={item} column={column} />,
      subFields: [...(columns[0]?.subFields || []), ...filterColumns],
    },
    { key: 'Action', name: 'Action', fieldName: 'action', minWidth: 80, maxWidth: 80 },
  ];

  return (
    <div className={css.container}>
      <Stack className={css.content} tokens={{ childrenGap: 12 }}>
        <PerfectScroll style={{ overflowX: 'hidden' }} className="h-100">
          <Stack tokens={{ childrenGap: 16 }}>
            <Stack horizontal={!isRcm} horizontalAlign="space-between" tokens={{ childrenGap: 16 }}>
              <SearchBox
                className={!isRcm && css.cmClass.minWidth300}
                placeholder={t('Search...')}
                iconProps={{ iconName: 'Zoom' }}
                defaultValue={params.searchTerm}
                onChange={(_, newValue) => onSearch(newValue)}
              />
              <Stack horizontal tokens={{ childrenGap: 16 }}>
                <Dropdown
                  onChange={(_, { key }) => updateParams({ isActive: key, Page: 0 })}
                  defaultSelectedKey={params.isActive ?? true}
                  options={StatusOptions}
                  prefix="Status:"
                  dropdownWidth="auto"
                />
                <DefaultButton
                  data-isbutton="button"
                  onClick={() => setIsSectionView(!isSectionView)}
                  iconProps={{ iconName: 'TableComputed', className: css.icon }}
                  text={t('Change View')}
                />
                <DefaultButton
                  iconProps={{ iconName: 'Table', className: css.icon }}
                  data-isbutton="button"
                  onClick={showTableDisplayModal}
                  text={t('Custom Field')}
                />
              </Stack>
            </Stack>
            <Table
              loading={loading}
              columns={isSectionView ? sectionViewColumn : filterColumns}
              items={data?.items || []}
              totalItems={data?.totalItems}
              metadata={params}
              onMetadataChange={updateParams}
              getMenuProps={getMenu}
              refetch={refetch}
            />
          </Stack>
        </PerfectScroll>
      </Stack>
    </div>
  );
};

const styles = (theme) => ({
  container: {
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-between',
    overflow: 'hidden',
    '.ms-DetailsRow-fields': {
      cursor: 'pointer',
    },
  },
  content: {
    overflow: 'auto',
    padding: 12,
  },

  icon: {
    color: theme.palette.themePrimary,
  },
});

export default PayorAssignmentHistoryContent;
