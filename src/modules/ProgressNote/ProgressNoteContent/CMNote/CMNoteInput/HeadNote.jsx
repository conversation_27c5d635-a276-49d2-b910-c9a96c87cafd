import { PROGRESS_NOTE_VIEW } from 'constants/progressNote';
import useNavigator from 'hooks/useNavigator';
import useRequest from 'hooks/useRequest';
import { API_CM3 } from 'constants/urlRequest';
import React, { useMemo } from 'react';
import MyField from 'components/Form/Field';
import { t } from 'utils/string';
import FieldServiceDate from 'components/NoteBasic/Service/components/FieldServiceDate';
import FieldServiceSite from 'components/NoteBasic/Service/components/FieldServiceSite';
import FieldSelectorPrimary from 'components/Form/FieldSelectorPrimary';
import FieldServiceType from 'components/NoteBasic/Service/components/FieldServiceType';
import uniqBy from 'lodash/uniqBy';
import ResponsiveGrid from 'components/Layout/Responsive/Container';
import ResponsiveItem from 'components/Layout/Responsive/Item';
import ProgramDrivenField from 'components/NoteBasic/NoteInformation/ProgramDrivenField';
import FieldText from 'components/Form/FieldText';

const HeadNote = ({ data: endUseData, fieldType = PROGRESS_NOTE_VIEW.BOX }) => {
  const {
    searchParams: { patientId },
  } = useNavigator();

  const { data: orgDataConfig } = useRequest({
    url: API_CM3.GET_ORG_DATA_CONFIG,
    shouldCache: true,
  });

  const { data } = useRequest({
    url: API_CM3.GET_IDENTIFIED_NEEDS,
    requiredParams: {
      patientId,
    },
    autoRefetch: true,
  });

  const identifiedNeedsOptions = useMemo(() => {
    let temp = [{ key: 0, text: t('Allow All Services') }];
    for (const i of data || []) {
      temp.push({ key: i.objValueId, text: `${i.needValue} (${i.objValue})` });
    }
    return temp;
  }, [data]);

  const encounterFormId = endUseData?.baseInformation?.encounterFormId;
  const flagProgram = endUseData?.baseInformation?.programDriven;

  const fields = [
    flagProgram && <ProgramDrivenField encounterFormId={encounterFormId} />,
    <FieldServiceDate />,
    <FieldSelectorPrimary
      isFast={false}
      placeholder={t('Select an option')}
      name={['serviceNeeds', 0, 'servicePlanFieldValueId']}
      title={t('Clinical Identified Needs')}
      options={uniqBy(identifiedNeedsOptions, 'key')}
      fieldType={fieldType}
      onChange={({ form, field, data }) => {
        form.setFieldValue(field?.name, data?.key);
        form.setFieldValue('services', []);
      }}
    />,
    <FieldServiceSite urlSiteLookup={API_CM3.GET_SERVICE_SITES} />,
    <MyField name="baseInformation.patientServiceType" isFast={false}>
      {({ field }) => field.value && <FieldServiceType />}
    </MyField>,
    ...(orgDataConfig?.showOrganizationDataProgressNote
      ? orgDataConfig?.encounterOrganizationFields?.map((i) => (
          <FieldText
            title={t(i?.encounterFieldDisplayName)}
            name={`encounter.${i?.encounterFieldKey}`}
            required={i?.required}
            type={i?.encounterFieldType}
          />
        ))
      : []),
  ];

  if (fieldType === PROGRESS_NOTE_VIEW.LIST) {
    return <ul>{fields?.filter((item) => item).map((item) => item)}</ul>;
  }

  return (
    <ResponsiveGrid id="cm-top">
      {fields
        ?.filter((item) => item)
        .map((item, index) => (
          <ResponsiveItem key={index} md={4}>
            {item}
          </ResponsiveItem>
        ))}
    </ResponsiveGrid>
  );
};

export default HeadNote;
