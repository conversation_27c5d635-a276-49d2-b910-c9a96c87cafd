import React, { useCallback, useEffect } from 'react';
import { PrimaryButton, SearchBox, Stack } from '@fluentui/react';
import Table from './Table';
import useModal from 'hooks/useModal';
import useRequest from 'hooks/useRequest';
import useNavigator from 'hooks/useNavigator';
import { API_METHOD, API_PROGRESS_NOTE } from 'constants/urlRequest';
import { t } from 'utils/string';
import CollapseVertical from 'components/Collapse/CollapseVertical';
import { formatToFluentOptions } from 'utils';
import { DEFAULT_PAGINATION_PARAMS } from 'constants/index';
import { debounceFunc } from 'utils/time';
import { PROGRESS_NOTE_FORM_TYPE } from 'constants/progressNote';
import SelectorPrimary from 'components/Selector';
import QUERY_KEY from 'constants/queryKey';
import { MODAL_SIZE } from 'constants/modal';
import ModalConfirm from 'components/Modal/ModalConfirm';
import { toast } from 'react-toastify';
import { TEXT_TEMPLATE } from 'constants/texts';
import useProgressNoteTypes from 'hooks/useProgressNoteTypes';

const ProgressNoteAutoSave = ({ sectionList }) => {
  const { searchParams, pathname } = useNavigator();
  const { showModal } = useModal();

  const apiProgressNoteAutoSave = useRequest({
    key: [QUERY_KEY.PROGRESS_NOTE_AUTO_SAVE, searchParams.patientId],
    url: API_PROGRESS_NOTE.AUTO_SAVE,
    params: { FormTypeIds: [], OrderBy: 'createDate', ...DEFAULT_PAGINATION_PARAMS },
    autoRefetch: true,
  });

  const apiProgressNoteTypes = useProgressNoteTypes();

  const apiDeleteAll = useRequest({
    url: API_PROGRESS_NOTE.AUTO_SAVE,
    method: API_METHOD.DELETE,
  });

  const handleDeleteAll = () => {
    showModal({
      content: (
        <ModalConfirm
          title={t('Clear All AutoSaves')}
          message={TEXT_TEMPLATE.DELETE_CONFIRMATION(t('all autosaves'))}
          okText={t('Yes')}
          cancelText={t('No')}
          onOk={() =>
            apiDeleteAll.request({
              queryString: {
                formTypeId: apiProgressNoteAutoSave.params?.FormTypeId ?? '',
              },
              options: {
                onSuccess: () => {
                  toast.success(TEXT_TEMPLATE.DELETE_SUCCESSFULLY(t('All autosaves')));
                  apiProgressNoteAutoSave.refetch();
                },
              },
            })
          }
        />
      ),
      size: MODAL_SIZE.X_SMALL,
      isReplace: false,
    });
  };

  const onSearch = useCallback(
    debounceFunc((SearchTerm) => apiProgressNoteAutoSave.updateParams({ SearchTerm }), 500),
    [],
  );

  useEffect(() => {
    if (!apiProgressNoteTypes.data?.length) return;

    const formName = Object.entries(PROGRESS_NOTE_FORM_TYPE).find(([_, value]) =>
      pathname?.includes(value.route),
    )?.[0];

    const FormTypeId = apiProgressNoteTypes?.data?.find((i) => i?.description === formName)?.key;
    if (!FormTypeId) return;

    apiProgressNoteAutoSave.updateParams({ FormTypeIds: [FormTypeId] });
  }, [apiProgressNoteTypes.data]);

  return (
    <CollapseVertical
      open
      title={t('Progress Note Auto Save')}
      className="mb-24"
      rightSide={
        <Stack
          horizontal
          tokens={{ childrenGap: 12 }}
          wrap
          verticalAlign="center"
          horizontalAlign="end"
        >
          <PrimaryButton text={t('Clear All')} onClick={handleDeleteAll} />
          <SelectorPrimary
            multiSelect
            showOptionAll
            placeholder={t('Progress Note Type')}
            options={formatToFluentOptions(apiProgressNoteTypes?.data)}
            value={apiProgressNoteAutoSave.params?.FormTypeIds || []}
            formatData={(v) => v?.key}
            onChange={(key) => apiProgressNoteAutoSave.updateParams({ FormTypeIds: key, Page: 0 })}
            defaultWidth={300}
          />
          <SearchBox
            placeholder={t('Search...')}
            iconProps={{ iconName: 'Zoom' }}
            onChange={(e, value) => onSearch(value)}
          />
        </Stack>
      }
    >
      <Table api={apiProgressNoteAutoSave} sectionList={sectionList} />
    </CollapseVertical>
  );
};

export default ProgressNoteAutoSave;
