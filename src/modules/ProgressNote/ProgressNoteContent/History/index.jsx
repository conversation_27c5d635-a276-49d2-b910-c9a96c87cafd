import React, { useCallback, useEffect, useState, useRef } from 'react';
import { DefaultButton, Stack } from '@fluentui/react';
import DatePicker from 'components/DatePicker';
import { DEFAULT_PAGINATION_PARAMS } from 'constants/index';
import { SETTING_KEYS } from 'constants/settingKeys';
import { API_PROGRESS_NOTE } from 'constants/urlRequest';
import { useNavigator, useRequest, useSetting, useUser } from 'hooks';
import moment from 'moment';
import { formatToFluentOptions } from 'utils';
import HistoryTable from './HistoryTable';
import { t } from 'utils/string';
import CollapseVertical from 'components/Collapse/CollapseVertical';
import { PROGRESS_NOTE_FORM_NAME, PROGRESS_NOTE_FORM_TYPE } from 'constants/progressNote';
import QUERY_KEY from 'constants/queryKey';
import SelectorPrimary from 'components/Selector';
import Summary from './Summary';
import ELEMENT_ID from 'constants/elementId';
import useRole from 'hooks/useRole';
import useModal from 'hooks/useModal';
import CompareModal from './CompareModal';
import useProgressNoteTypes from 'hooks/useProgressNoteTypes';

//sectionList for page support checking
const ProgressNoteHistory = ({ sectionList }) => {
  const { searchParams, pathname } = useNavigator();
  const { showModal } = useModal();
  const [formatDate, defaultNoteType, defaultDayBack] = useSetting(
    SETTING_KEYS.DATE_FORMAT,
    SETTING_KEYS.PROGRESS_NOTE_DEFAULT_TYPE,
    SETTING_KEYS.PROGRESS_NOTE_DEFAULT_DEFAULT_DAY_BACK,
  );
  const { isAllowUseSidebar } = useRole();
  const { info } = useUser();
  const [selectedItems, setSelectedItems] = useState([]);
  const historyTableRef = useRef();

  const formName = Object.entries(PROGRESS_NOTE_FORM_TYPE).find(([_, value]) =>
    pathname?.includes(value.route),
  )?.[0];
  const isGroupNote = formName === PROGRESS_NOTE_FORM_NAME.GROUP_NOTE;

  const { data } = useProgressNoteTypes();

  const apiHistory = useRequest({
    key: [QUERY_KEY.PROGRESS_NOTE_HISTORY, formName, searchParams.patientId],
    url: API_PROGRESS_NOTE.HISTORY,
    params: {
      ...DEFAULT_PAGINATION_PARAMS,
      FormTypeIds: [],
      FromDateTime: moment().subtract(defaultDayBack, 'days').startOf('day').toISOString(),
      OrderBy: 'createDate',
      EmployeeId: isGroupNote ? info.ehrUserId : '',
    },
    requiredParams: {
      ...(isGroupNote ? {} : { patientId: searchParams.patientId }),
    },
    enabled: !!data,
    saveHistory: false,
    autoRefetch: true,
  });

  useEffect(() => {
    if (defaultNoteType === 'All') return;
    if (defaultNoteType !== 'Current') {
      apiHistory.updateParams({ FormTypeIds: [defaultNoteType] });
      return;
    }
    if (!data?.length) return;

    const FormTypeId = data?.find((i) => i?.description === formName)?.key;
    if (!FormTypeId) return;

    apiHistory.updateParams({ FormTypeIds: [FormTypeId] });
  }, [data, defaultNoteType]);

  const onViewDetail = useCallback(() => {
    showModal({
      content: (
        <CompareModal
          encounterIds={selectedItems.map((i) => i.encounterId)}
          historyTableRef={historyTableRef}
        />
      ),
    });
  }, [selectedItems]);

  return (
    <CollapseVertical
      id={ELEMENT_ID.NOTE.ALL_NOTE_HISTORY}
      open
      title={t('All Progress Note History')}
      className="mb-24"
    >
      <Stack
        horizontal
        tokens={{ childrenGap: 16 }}
        className="mb-16"
        verticalAlign="end"
        horizontalAlign="space-between"
      >
        <DefaultButton
          text={t('Compare')}
          onClick={onViewDetail}
          disabled={selectedItems.length === 0}
        />
        <Stack horizontal verticalAlign="end" tokens={{ childrenGap: 16 }}>
          {!isGroupNote && (
            <SelectorPrimary
              multiSelect
              showOptionAll
              label={t('Progress Note Type')}
              placeholder={t('Select...')}
              options={formatToFluentOptions(data)}
              value={apiHistory.params?.FormTypeIds || []}
              formatData={(v) => v?.key}
              onChange={(key) => apiHistory.updateParams({ FormTypeIds: key, Page: 0 })}
              defaultWidth={300}
            />
          )}
          <DatePicker
            label={t('Days Back')}
            placeholder={t('Days Back')}
            formatDate={(date) => moment(date).format(formatDate)}
            value={apiHistory.params.FromDateTime && new Date(apiHistory.params.FromDateTime)}
            onSelectDate={(item) => apiHistory?.updateParams({ FromDateTime: item, Page: 0 })}
            disableAutoFocus={false}
            isDayBack
          />
        </Stack>
      </Stack>
      {isAllowUseSidebar && <Summary apiHistory={apiHistory} />}
      <HistoryTable
        ref={historyTableRef}
        sectionList={sectionList}
        apiHistory={apiHistory}
        setSelectedItems={setSelectedItems}
      />
    </CollapseVertical>
  );
};

export default ProgressNoteHistory;
