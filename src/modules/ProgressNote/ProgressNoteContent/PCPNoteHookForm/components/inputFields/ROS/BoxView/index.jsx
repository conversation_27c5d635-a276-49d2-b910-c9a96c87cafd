import React, { useState } from 'react';
import { Command<PERSON>ar<PERSON>utton, DefaultButton, Dropdown, Stack, StackItem } from '@fluentui/react';
import './exam-template.scss';
import { useClass, useModal, useRequest } from 'hooks';
import TableEmotion from 'modules/ProgressNote/ProgressNoteContent/PCPNoteHookForm/components/TableEmotion';
import { _get } from 'utils/form';
import FieldText from 'components/HookForm/FieldText';
import { formatToFluentOptions } from 'utils';
import SaveTemplateModal from 'modules/ProgressNote/ProgressNoteContent/components/SaveTemplateModal';
import { PCP_SECTION_ID, PROGRESS_NOTE_TEMPLATE_NAME } from 'constants/progressNote';
import { MODAL_SIZE } from 'constants/modal';
import { API_METHOD, API_PCP_NOTE } from 'constants/urlRequest';
import LoadingWrapper from 'components/Loading/LoadingWrapper';
import CalloutConfirmation from 'components/Callout/CalloutConfirmation';
import { TEXT_TEMPLATE } from 'constants/texts';
import { toast } from 'react-toastify';
import FieldError from 'components/HookForm/FieldError';
import cn from 'classnames';
import { t } from 'utils/string';
import FieldChoice from 'components/HookForm/FieldChoice';
import { useFormContext, useWatch } from 'react-hook-form';

const ExamTemplate = ({
  name,
  template = [],
  onSelectTemplate,
  shortcutName,
  data,
  withTemplate,
}) => {
  const css = useClass(styles);
  const ros = _get(data, name, {});
  const [selected, setSelected] = useState();
  const { showModal } = useModal();
  const { setValue, getValues } = useFormContext();
  const { request, loading } = useRequest({
    url: API_PCP_NOTE.SAVE_PCP_NOTE_EXAM_TEMPLATE,
    method: API_METHOD.DELETE,
    enabled: false,
  });

  const onSaveTemplate = (value) => {
    showModal({
      content: <SaveTemplateModal values={value} type={PROGRESS_NOTE_TEMPLATE_NAME.ROS} />,
      size: MODAL_SIZE.X_SMALL,
    });
  };

  const fieldValue = useWatch({ name });
  const rosSettingValue = useWatch({ name: '_rosSetting' });

  return (
    <div className="exam-template-container">
      <FieldError name={name?.slice(0, -2)} tabId={PCP_SECTION_ID.EXAM} />
      <div className="template-tool">
        {withTemplate && (
          <React.Fragment>
            <Dropdown
              placeholder={t('Select a template')}
              options={formatToFluentOptions(template, 'templateName', 'templateId')}
              selectedKey={selected}
              onChange={(_, option) => {
                onSelectTemplate(option);
                setSelected(option.key);
              }}
            />
            <LoadingWrapper loading={loading}>
              <CalloutConfirmation
                description={TEXT_TEMPLATE.DELETE_CONFIRMATION(t('this template'))}
                onOk={async () => {
                  await request({ url: `${API_PCP_NOTE.SAVE_PCP_NOTE_EXAM_TEMPLATE}/${selected}` });
                  toast.success(TEXT_TEMPLATE.DELETE_SUCCESSFULLY(t('Template')));
                }}
              >
                <DefaultButton disabled={!selected}>{t('Delete Template')}</DefaultButton>
              </CalloutConfirmation>
            </LoadingWrapper>
            <DefaultButton data-isbutton="button" onClick={() => onSaveTemplate(fieldValue)}>
              {t('Save Template')}
            </DefaultButton>
          </React.Fragment>
        )}
        <CalloutConfirmation
          description={TEXT_TEMPLATE.CLEAR_CONFIRMATION(t('this section'))}
          onOk={() => {
            setValue(name, {});
            setValue(shortcutName, '');
          }}
        >
          <DefaultButton>{t('Clear')}</DefaultButton>
        </CalloutConfirmation>
      </div>

      {shortcutName && <FieldText title={t('Shortcut/ Manual Entry')} name={shortcutName} />}

      <FieldChoice
        name="_rosSetting"
        isFast={false}
        horizontal
        options={[
          { key: TEMPLATE_ACTION.ALL_NEGATIVE, text: 'All Reviewed And Negative' },
          { key: TEMPLATE_ACTION.ALL_NEGATIVE_EXCEPT, text: 'All Reviewed And Negative, Except' },
        ]}
        onChange={({ key }) => {
          setValue('_rosSetting', key);
          const formValues = getValues();
          const temp = _get(formValues, 'examSection.ros[0].mentalStatusTypes', []);
          for (const mental of temp) {
            for (const option of mental.options) {
              option.abnormal = 0;
              option.selected = true;
            }
          }
          setValue('examSection.ros[0].mentalStatusTypes', temp);
        }}
      />
      {rosSettingValue !== TEMPLATE_ACTION.ALL_NEGATIVE && (
        <div className="template-content">
          {(ros.mentalStatusTypes || []).map((item, index) => (
            <div key={item.mentalStatusTypeId} className="template-table">
              <Stack
                horizontal
                horizontalAlign="space-between"
                className={cn(css.headerClassName, css.cmClass.backgroundColor, css.cmClass.border)}
              >
                <StackItem data-istitle="title" grow className={css.title}>
                  {item.mentalStatusTypeValue}
                </StackItem>
                <CalloutConfirmation
                  description={TEXT_TEMPLATE.CLEAR_CONFIRMATION(t('this section'))}
                  onOk={() => setValue(`${name}.mentalStatusTypes.${index}.options`, [])}
                >
                  <CommandBarButton text={t('Clear')} className={css.clearButton} />
                </CalloutConfirmation>
              </Stack>
              <TableEmotion fieldName={`${name}.mentalStatusTypes.${index}`} item={item} />
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

const TEMPLATE_ACTION = {
  ALL_NEGATIVE: 'all_negative',
  ALL_NEGATIVE_EXCEPT: 'all_negative_except',
};

const styles = (theme) => {
  return {
    headerClassName: {
      padding: '12px 16px',
      borderBottom: '1px solid',
    },
    title: {
      fontWeight: 600,
      fontSize: '16px',
      lineHeight: '20px',
    },
    clearButton: {
      fontSize: '14px',
      lineHeight: '20px',
      color: theme.custom.red6,
    },
  };
};

export default React.memo(ExamTemplate);
