import React, { useState } from 'react';
import { Command<PERSON>ar<PERSON>utton, DefaultButton, Dropdown, Stack, StackItem } from '@fluentui/react';
import './exam-template.scss';
import { useClass, useModal, useRequest } from 'hooks';
import TableEmotion from 'modules/ProgressNote/ProgressNoteContent/components/TableEmotion';
import { _get } from 'utils/form';
import FieldText from 'components/Form/FieldText';
import { formatToFluentOptions } from 'utils';
import MyField from 'components/Form/Field';
import SaveTemplateModal from 'modules/ProgressNote/ProgressNoteContent/components/SaveTemplateModal';
import { PCP_SECTION_ID, PROGRESS_NOTE_TEMPLATE_NAME } from 'constants/progressNote';
import { MODAL_SIZE } from 'constants/modal';
import { API_METHOD, API_PCP_NOTE } from 'constants/urlRequest';
import LoadingWrapper from 'components/Loading/LoadingWrapper';
import CalloutConfirmation from 'components/Callout/CalloutConfirmation';
import { TEXT_TEMPLATE } from 'constants/texts';
import { toast } from 'react-toastify';
import FieldError from 'components/Form/FieldError';
import cn from 'classnames';
import { t } from 'utils/string';
import FieldChoice from 'components/Form/FieldChoice';

const ExamTemplate = ({
  name,
  template = [],
  onSelectTemplate,
  shortcutName,
  data,
  withTemplate,
}) => {
  const css = useClass(styles);
  const ros = _get(data, name, {});
  const [selected, setSelected] = useState();
  const { showModal } = useModal();

  const { request, loading } = useRequest({
    url: API_PCP_NOTE.SAVE_PCP_NOTE_EXAM_TEMPLATE,
    method: API_METHOD.DELETE,
    enabled: false,
  });

  const onSaveTemplate = (value) => {
    showModal({
      content: <SaveTemplateModal values={value} type={PROGRESS_NOTE_TEMPLATE_NAME.ROS} />,
      size: MODAL_SIZE.X_SMALL,
    });
  };

  return (
    <div className="exam-template-container">
      <FieldError name={name?.slice(0, -2)} tabId={PCP_SECTION_ID.EXAM} />
      <div className="template-tool">
        {withTemplate && (
          <React.Fragment>
            <Dropdown
              placeholder={t('Select a template')}
              options={formatToFluentOptions(template, 'templateName', 'templateId')}
              selectedKey={selected}
              onChange={(_, option) => {
                onSelectTemplate(option);
                setSelected(option.key);
              }}
            />
            <LoadingWrapper loading={loading}>
              <CalloutConfirmation
                description={TEXT_TEMPLATE.DELETE_CONFIRMATION(t('this template'))}
                onOk={async () => {
                  await request({ url: `${API_PCP_NOTE.SAVE_PCP_NOTE_EXAM_TEMPLATE}/${selected}` });
                  toast.success(TEXT_TEMPLATE.DELETE_SUCCESSFULLY(t('Template')));
                }}
              >
                <DefaultButton disabled={!selected}>{t('Delete Template')}</DefaultButton>
              </CalloutConfirmation>
            </LoadingWrapper>
            <MyField name={name}>
              {({ field: { value } }) => (
                <DefaultButton data-isbutton="button" onClick={() => onSaveTemplate(value)}>
                  {t('Save Template')}
                </DefaultButton>
              )}
            </MyField>
          </React.Fragment>
        )}
        <MyField name={name}>
          {({ form: { setFieldValue } }) => (
            <CalloutConfirmation
              description={TEXT_TEMPLATE.CLEAR_CONFIRMATION(t('this section'))}
              onOk={() => {
                setFieldValue(name, {});
                setFieldValue(shortcutName, '');
              }}
            >
              <DefaultButton>{t('Clear')}</DefaultButton>
            </CalloutConfirmation>
          )}
        </MyField>
      </div>
      {shortcutName && <FieldText title={t('Shortcut/ Manual Entry')} name={shortcutName} />}

      <FieldChoice
        name="_rosSetting"
        isFast={false}
        horizontal
        options={[
          { key: TEMPLATE_ACTION.ALL_NEGATIVE, text: 'All Reviewed And Negative' },
          { key: TEMPLATE_ACTION.ALL_NEGATIVE_EXCEPT, text: 'All Reviewed And Negative, Except' },
        ]}
        onChange={({ key }, name, { values, setFieldValue }) => {
          setFieldValue(name, key);
          const temp = _get(values, 'examSection.ros[0].mentalStatusTypes', []);
          for (const mental of temp) {
            for (const option of mental.options) {
              option.abnormal = 0;
              option.selected = true;
            }
          }
          setFieldValue(dataKey, temp);
        }}
      />
      <MyField name="_rosSetting" isFast={false}>
        {({ field }) =>
          field.value !== TEMPLATE_ACTION.ALL_NEGATIVE && (
            <div className="template-content">
              {(ros.mentalStatusTypes || []).map((item, index) => (
                <div key={item.mentalStatusTypeId} className="template-table">
                  <Stack
                    horizontal
                    horizontalAlign="space-between"
                    className={cn(
                      css.headerClassName,
                      css.cmClass.backgroundColor,
                      css.cmClass.border,
                    )}
                  >
                    <StackItem data-istitle="title" grow className={css.title}>
                      {item.mentalStatusTypeValue}
                    </StackItem>
                    <MyField name={`${name}.mentalStatusTypes.${index}.options`}>
                      {({ field: { name }, form: { setFieldValue } }) => (
                        <CalloutConfirmation
                          description={TEXT_TEMPLATE.CLEAR_CONFIRMATION(t('this section'))}
                          onOk={() => setFieldValue(name, [])}
                        >
                          <CommandBarButton text={t('Clear')} className={css.clearButton} />
                        </CalloutConfirmation>
                      )}
                    </MyField>
                  </Stack>
                  <TableEmotion fieldName={`${name}.mentalStatusTypes.${index}`} item={item} />
                </div>
              ))}
            </div>
          )
        }
      </MyField>
    </div>
  );
};

const TEMPLATE_ACTION = {
  ALL_NEGATIVE: 'all_negative',
  ALL_NEGATIVE_EXCEPT: 'all_negative_except',
};

const styles = (theme) => {
  return {
    headerClassName: {
      padding: '12px 16px',
      borderBottom: '1px solid',
    },
    title: {
      fontWeight: 600,
      fontSize: '16px',
      lineHeight: '20px',
    },
    clearButton: {
      fontSize: '14px',
      lineHeight: '20px',
      color: theme.custom.red6,
    },
  };
};

export default React.memo(ExamTemplate);
