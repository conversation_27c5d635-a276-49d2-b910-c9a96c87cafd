import React, { useState } from 'react';
import { useClass, useRequest } from 'hooks';
import { API_CALOCUS } from 'constants/urlRequest';
import { DefaultButton, PrimaryButton, Stack } from '@fluentui/react';
import SubCollapse from 'components/Collapse/SubCollapse';
import { t } from 'utils/string';
import LoadingWrapper from 'components/Loading/LoadingWrapper';
import HorizontalRule from 'components/HorizontalRule';
import FieldCheckBox from 'components/Form/FieldCheckbox';
import cn from 'classnames';
import MyField from 'components/Form/Field';
import { computeDimScoreAndStuffs, validate } from './constant';
import { toast } from 'react-toastify';

function Evaluating({ back, next, error, setError }) {
  const css = useClass(styles);
  const apiBaseForm = useRequest({ url: API_CALOCUS.BASE_FORM, shouldCache: true });
  const [collapse, setCollapse] = useState(true);

  return (
    <LoadingWrapper loading={apiBaseForm.loading}>
      <Stack tokens={{ childrenGap: 16 }}>
        <Stack horizontal tokens={{ childrenGap: 16 }} horizontalAlign="end">
          <PrimaryButton
            text={t('Collapse All')}
            data-isbutton="button"
            onClick={() => setCollapse(false)}
          />
          <DefaultButton
            text={t('Expand All')}
            data-isbutton="button"
            onClick={() => setCollapse(true)}
          />
        </Stack>
        {apiBaseForm?.data?.map((i, sectionIndex) => (
          <SubCollapse
            key={i.sectionId}
            title={
              <span className={cn({ 'color-error': error === `section-${i.order}` })}>
                {i.sectionName}
              </span>
            }
            open={collapse}
            id={`section-${i.order}`}
            className={css.sectionBackground}
          >
            {i.description && (
              <div className={css.section} dangerouslySetInnerHTML={{ __html: i.description }} />
            )}
            {i.questions.map((ques, quesIndex) => {
              const isMakeIndex = ques.questionText != 'Child is emancipated';
              return (
                <Stack key={ques.questionId}>
                  {(i.description || quesIndex > 0) && (
                    <HorizontalRule mt={i.description ? 16 : quesIndex ? 16 : 0} mb={16} />
                  )}
                  <div
                    className={cn(
                      'p-16',
                      css.cmClass.border,
                      css.cmClass.borderRadius8,
                      css.cmClass.backgroundGrey100,
                    )}
                  >
                    <FieldCheckBox
                      vertical
                      name={['locus', 'evaluating', sectionIndex, 'questions', quesIndex, 'answer']}
                      onChange={({ setFieldValue, checked, name, item, value, index }) => {
                        let temp = value ? [...value] : [];
                        temp[index] = checked ? item : undefined;
                        setFieldValue(name, temp);
                        setFieldValue(['locus', 'evaluating', sectionIndex, 'order'], i.order);
                      }}
                      formatValue={(value, item, index) => value?.[index]?.key === item.key}
                      title={`${quesIndex + 1} - ${ques.questionText}`}
                      options={ques.answers.map((ans, ansIndex) => ({
                        key: ans.answerId,
                        text: ans.answerText,
                        point: isMakeIndex ? +ansIndex + 1 : 0,
                        value: ans.answerValue,
                        precedence: ques.precedence,
                        answerId: ans.answerId,
                        questionId: ques.questionId,
                      }))}
                    />
                  </div>
                </Stack>
              );
            })}
          </SubCollapse>
        ))}
        <Stack horizontal tokens={{ childrenGap: 16 }}>
          <MyField isFast={false}>
            {({ form: { values, setFieldValue } }) => (
              <PrimaryButton
                text={t('Compute Scores')}
                data-isbutton="button"
                onClick={() => {
                  let errorSection = '';
                  if ((values?.locus?.evaluating || []).length < 8) {
                    toast.error(
                      'Each dimensions must have at least one response. Please make a selection.',
                    );
                    errorSection = validate(values?.locus?.evaluating || []);
                    setError(errorSection);
                  }

                  if (!errorSection) {
                    setFieldValue(
                      'locus.recommendedDispositionLevel',
                      computeDimScoreAndStuffs(values?.locus?.evaluating),
                    );
                    next();
                  }
                }}
              />
            )}
          </MyField>
          <DefaultButton text={t('Back To Start')} data-isbutton="button" onClick={back} />
          <MyField>
            {({ form: { setFieldValue } }) => (
              <DefaultButton
                text={t('Cancel Evaluation')}
                data-isbutton="button"
                onClick={() => {
                  back();
                  setFieldValue('locus', {});
                }}
              />
            )}
          </MyField>
        </Stack>
      </Stack>
    </LoadingWrapper>
  );
}

const styles = (theme) => ({
  section: {
    p: { margin: '0 4px' },
  },
  sectionBackground: {
    backgroundColor: theme.darkMode ? theme.palette.backgroundColorShade3 : '#fff',
  },
});

export default Evaluating;
