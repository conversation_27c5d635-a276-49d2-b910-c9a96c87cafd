import { scrollIntoViewId } from 'utils/globalScroll';

export const validate = (scores) => {
  for (let index = 1; index <= 8; index++) {
    if (!scores[index - 1]) {
      scrollIntoViewId(`section-${index}`, 'start');
      return `section-${index}`;
    }
    let hasAnswer = false;
    for (const ques of scores[index - 1].questions.filter(Boolean)) {
      if (ques.answer.filter(Boolean).length) hasAnswer = true;
    }
    if (!hasAnswer) {
      scrollIntoViewId(`section-${scores[index - 1].order}`, 'start');
      return `section-${scores[index - 1].order}`;
    }
  }
};

export const computeDimScoreAndStuffs = (scores = []) => {
  let sectionScore = [];
  let actPresent;
  let noPrior;
  for (const [sectionIndex, section] of scores.filter(Boolean).entries()) {
    let highestScore = 0;
    for (const ques of section.questions.filter(Boolean)) {
      for (const ans of ques.answer.filter(<PERSON><PERSON><PERSON>)) {
        if (ans.point > highestScore) {
          highestScore = ans.point;
        }
        if (sectionIndex === 4) {
          if (ans.text.includes('Effective involvement in a Wraparound Process')) actPresent = 1;
        } else if (sectionIndex === 5) {
          if (ans.text.includes('There has been no prior experience with treatment or recovery'))
            noPrior = 1;
        } else {
          break;
        }
      }
    }
    sectionScore[sectionIndex] = highestScore;
  }

  // WILL USE LATE
  // const withOutLastElement = sectionScore.slice(0, -1);
  // const composite = withOutLastElement.reduce(
  //   (prevValue, curValue) => prevValue + parseInt(curValue),
  //   0,
  // );
  // const total23 = withOutLastElement
  //   .map((i) => (i === 2 ? 1 : 0))
  //   .reduce((prevValue, curValue) => prevValue + parseInt(curValue), 0);

  return calcLOC(...sectionScore, actPresent, noPrior);
};

function calcLOC(I, II, III, IVa, IVb, V, VI, Family = 0, ACTPresent, NoPrior) {
  if (ACTPresent == 1) IVb = 1; //if ACT/Wraparound is selected, this pre-empts higher ratings

  if (V >= 2) NoPrior = 0; //cannot be "No Prior Experience" if another item with a score of 2 or more was also selected

  if (Family > VI) VI = Family; //for CALOCUS, Dimension VI is the higher of the Child Subscale or the Family Subscale

  var Level = 0;
  var Composite = I + II + III + IVa + IVb + V + VI;

  var Total23 = 0;
  if (I == 2) Total23++;
  if (II == 2) Total23++;
  if (III == 2) Total23++;
  if (IVa == 2) Total23++;
  if (IVb == 2) Total23++;
  if (V == 2) Total23++;
  if (VI == 2) Total23++;

  let Block = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];

  if (Composite <= 16 && I <= 3 && II <= 3 && III <= 3) Block[0] = 1; //Block[0] = 1 means use Entry Point A; Block[0] = 0 means use Entry Point B
  if (I <= 2 && II <= 2 && III <= 2 && IVa <= 2 && IVb <= 2 && V <= 2 && VI <= 2) Block[1] = 1;
  if (Composite >= 14) Block[2] = 1;
  if (NoPrior == 0) Block[3] = 1; //NoPrior = False is equivalent to CompletedTreatment = True
  if (Composite >= 10) Block[4] = 1;
  if (I <= 2 && III <= 2 && VI <= 2 && II <= 3) Block[5] = 1;
  if (IVa + IVb <= 4) Block[6] = 1;
  if (IVa >= 3 || IVb >= 3 || V >= 3) Block[7] = 1;
  if (IVb <= 2) Block[8] = 1;
  if (V <= 2 && IVa + IVb <= 5) Block[9] = 1;
  if (I == 3 || II == 3 || III == 3) Block[10] = 1;
  if (I == 5 || II == 5 || III == 5) Block[11] = 1;
  if (Composite >= 28) Block[12] = 1;
  if (I == 4) Block[13] = 1;
  if (II == 4 || III == 4) Block[14] = 1;
  if (IVa == 1 && IVb == 1) Block[15] = 1;
  if (I >= 4 || II >= 4 || III >= 4 || IVa >= 4 || IVb >= 4 || V >= 4 || VI >= 4) Block[16] = 1;
  if (V >= 4 || VI >= 4) Block[17] = 1;
  if (Composite >= 20 && Composite <= 22) Block[18] = 1;
  if (Composite >= 23) Block[19] = 1;
  if (Composite >= 17) Block[20] = 1;
  if (IVa == 1 && IVb == 1) Block[21] = 1;
  if (ACTPresent == 1 && IVa <= 2) Block[22] = 1;
  if (Total23 >= 2) Block[23] = 1;
  if (IVa + IVb <= 5) Block[24] = 1;

  //****** ENTRY POINT B EXCEPTIONS ******
  if (Block[11] == 1) Level = 6;
  else if (Block[12] == 1) Level = 6;
  else if (Block[13] == 1) Level = 5;
  else if (Block[14] == 1 && Block[15] == 0) Level = 5;
  else if (
    Block[0] == 0 &&
    Block[14] == 0 &&
    Block[16] == 1 &&
    Block[17] == 1 &&
    Block[21] == 0 &&
    Block[22] == 1
  )
    Level = 4;
  else if (Block[0] == 0 && Block[14] == 0 && Block[16] == 0 && Block[23] == 1 && Block[24] == 1)
    Level = 3;
  //****** ENTRY POINT A EXCEPTIONS ******
  else if (
    Block[0] == 1 &&
    Block[1] == 0 &&
    Block[5] == 0 &&
    Block[6] == 0 &&
    Block[9] == 0 &&
    Block[10] == 1
  )
    Level = 3;
  else if (
    Block[0] == 1 &&
    Block[1] == 0 &&
    Block[5] == 0 &&
    Block[6] == 0 &&
    Block[9] == 0 &&
    Block[10] == 0
  )
    Level = 2;
  else if (
    Block[0] == 1 &&
    Block[1] == 0 &&
    Block[7] == 1 &&
    Block[8] == 0 &&
    Block[9] == 0 &&
    Block[10] == 1
  )
    Level = 3;
  else if (
    Block[0] == 1 &&
    Block[1] == 0 &&
    Block[7] == 1 &&
    Block[8] == 0 &&
    Block[9] == 0 &&
    Block[10] == 0
  )
    Level = 2;
  //****** COMPOSITE SCORE ******
  else if (Composite >= 28) Level = 6;
  else if (Block[19] == 1 && Composite <= 27) Level = 5;
  else if (Block[18] == 1) Level = 4;
  else if (Block[20] == 1 && Composite <= 29) Level = 3;
  else if (Block[2] == 1 && Composite <= 16) Level = 2;
  else if (Block[3] == 0 || (Composite >= 7 && Composite <= 9)) Level = 0;
  else if (Block[4] == 1 && Composite <= 13) Level = 1;
  else Level = 0;

  return Level;
}
