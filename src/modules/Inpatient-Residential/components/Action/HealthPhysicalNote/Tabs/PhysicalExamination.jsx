import React, { Fragment } from 'react';
import { CommandBarButton, PrimaryButton, Stack } from '@fluentui/react';
import cn from 'classnames';
import CollapseVertical from 'components/Collapse/CollapseVertical';
import MyField from 'components/Form/Field';
import FieldComboBox from 'components/Form/FieldComboBox';
import FieldText from 'components/Form/FieldText';
import HorizontalRule from 'components/HorizontalRule';
import LoadingWrapper from 'components/Loading/LoadingWrapper';
import ModalConfirm from 'components/Modal/ModalConfirm';
import { MODAL_SIZE } from 'constants/modal';
import { TEXT_TEMPLATE } from 'constants/texts';
import { API_METHOD } from 'constants/urlRequest';
import { useClass, useModal, useRequest } from 'hooks';
import { get } from 'lodash';
import { toast } from 'react-toastify';
import { formatToFluentOptions } from 'utils';
import { t } from 'utils/string';
import AddTemplateModal from 'modules/InpatientNote/components/AddTemplateModal';
import FieldError from 'components/Form/FieldError';
import { HEALTH_PHYSICAL_NOTE_FIELDS_NAME } from 'constants/progressNote';

function PhysicalExamination({
  dataKey,
  tempKey,
  apiKey,
  title = t('Physical Examination'),
  params,
}) {
  const css = useClass(styles);
  const { showModal } = useModal();

  const apiTemplate = useRequest({
    url: apiKey.get,
    ...(params && { params, key: `${apiKey.get}-${title}` }),
  });

  const apiDetail = useRequest({ key: dataKey, enabled: false });

  const apiDeleteTemplate = useRequest({
    method: API_METHOD.DELETE,
    onSuccess: () => {
      apiTemplate.refetch();
      toast.success(TEXT_TEMPLATE.DELETE_SUCCESSFULLY());
    },
  });

  const apiCreateTemplate = useRequest({
    url: apiKey.get,
    method: API_METHOD.POST,
    onSuccess: () => {
      apiTemplate.refetch();
      toast.success(TEXT_TEMPLATE.ADD_SUCCESSFULLY());
    },
  });

  const apiUpdateTemplate = useRequest({
    method: API_METHOD.PUT,
    onSuccess: () => {
      apiTemplate.refetch();
      toast.success(TEXT_TEMPLATE.UPDATE_SUCCESSFULLY());
    },
  });

  const setDefaultMental = ({ values, setFieldValue }, result = []) => {
    const temp = get(values, dataKey, []);
    for (const mental of temp) {
      for (const option of mental.options) {
        option.examItems = option.examItems.map((i) => ({ ...i, detail: '', selected: false }));
        for (const item of option.examItems) {
          for (const ans of result) {
            if (ans.mentalStatusId + '' === `${item.mentalStatusId}${item.examinationTypeId}`) {
              item.detail = ans.detail;
              item.selected = true;
            }
          }
        }
      }
    }
    setFieldValue(dataKey, temp);
  };

  const createTemplate =
    (arr = []) =>
    (name) => {
      apiCreateTemplate.mutateAsync({ values: arr, templateName: name, type: params.templateType });
    };

  const ButtonList = ({ options, name }) => {
    return (
      <MyField isFast={false}>
        {({ form }) => (
          <Stack horizontal tokens={{ childrenGap: 16 }} wrap>
            {options.map((j) => {
              const itemIndex = get(form.values, `${name}.examItems`, []).findIndex(
                (i) => i.examinationTypeId === j.examinationTypeId,
              );
              return (
                <Stack key={j.examinationTypeId} tokens={{ childrenGap: 8 }}>
                  <div
                    className={cn(
                      css.cmClass.border,
                      css.cmClass.borderRadius8,
                      'p-8 pointer weight-600',
                      css.buttonWidth,
                      {
                        [css.blue]: j.abnormal === null,
                        [css.backgroundBlue]: j.abnormal === null && j.selected,
                        [css.green]: j.abnormal === 0,
                        [css.backgroundGreen]: j.abnormal === 0 && j.selected,
                        [css.red]: j.abnormal === 1,
                        [css.backgroundRed]: j.abnormal === 1 && j.selected,
                      },
                    )}
                    data-isbutton="button"
                    onClick={() => {
                      const temp = [...get(form.values, `${name}.examItems`, [])];
                      temp[itemIndex].selected = !temp[itemIndex].selected;
                      form.setFieldValue(`${name}.examItems`, temp);
                    }}
                    role="button"
                    tabIndex={0}
                  >
                    {j.typeDescription}
                  </div>
                  {j.selected && (
                    <FieldText
                      name={`${name}.examItems.${itemIndex}.detail`}
                      placeholder={j.typeDescription}
                    />
                  )}
                </Stack>
              );
            })}
          </Stack>
        )}
      </MyField>
    );
  };

  return (
    <div
      className={cn(
        css.cmClass.backgroundGrey100,
        css.cmClass.border,
        css.cmClass.borderColor,
        css.cmClass.borderRadius8,
        'p-16',
      )}
    >
      <section className="weight-600 text-16-24">
        {title} <span className="color-error">*</span>
        <FieldError name={HEALTH_PHYSICAL_NOTE_FIELDS_NAME.ROS.PHYSICAL} />
      </section>

      <Stack horizontal className="mt-16" tokens={{ childrenGap: 12 }}>
        <FieldComboBox
          isFast={false}
          options={formatToFluentOptions(apiTemplate?.data)}
          name={tempKey}
          formatValue={(data) => data?.key}
          onChange={async ({ data, form, field }) => {
            form.setFieldValue(field.name, data);
            const result = await apiDetail.request({
              url: apiKey.detail(data.key),
            });
            setDefaultMental(form, result?.data.values);
          }}
        />
        <MyField name={dataKey}>
          {({ field }) => (
            <PrimaryButton
              text={t('Create')}
              data-isbutton="button"
              onClick={() => {
                showModal({
                  size: MODAL_SIZE.SMALL,
                  content: (
                    <AddTemplateModal
                      onSubmit={createTemplate(field.value)}
                      loading={apiCreateTemplate.loading}
                    />
                  ),
                });
              }}
            />
          )}
        </MyField>
        <MyField name={tempKey} isFast={false}>
          {({ field, form }) =>
            field.value && (
              <LoadingWrapper loading={apiUpdateTemplate.loading}>
                <PrimaryButton
                  text={t('Update')}
                  data-isbutton="button"
                  onClick={() =>
                    apiUpdateTemplate.request({
                      url: apiKey.update(field.value.key),
                      payload: {
                        values: get(form.values, dataKey),
                        templateName: field.value.text,
                        type: params.templateType,
                      },
                    })
                  }
                />
              </LoadingWrapper>
            )
          }
        </MyField>
        <MyField name={tempKey} isFast={false}>
          {({ field, form }) =>
            field.value && (
              <PrimaryButton
                text={t('Delete')}
                data-isbutton="button"
                onClick={() =>
                  showModal({
                    content: (
                      <ModalConfirm
                        message={TEXT_TEMPLATE.DELETE_CONFIRMATION(t('this template'))}
                        onOk={async () => {
                          await apiDeleteTemplate.request({
                            url: apiKey.delete(field.value.key),
                          });
                          form.setFieldValue(tempKey, null);
                        }}
                      />
                    ),
                    size: MODAL_SIZE.X_SMALL,
                  })
                }
              />
            )
          }
        </MyField>
        <div className="flex-1" />
        <MyField name={dataKey}>
          {({ field: { name, value }, form: { setFieldValue } }) => (
            <React.Fragment>
              <CommandBarButton
                text={t('All Normal')}
                className={css.normalButton}
                data-isbutton="button"
                onClick={() => {
                  const temp = [...value];
                  for (const i of temp) {
                    for (const option of i.options) {
                      for (const item of option.examItems) {
                        if (item.abnormal === 0) {
                          item.selected = true;
                        }
                      }
                    }
                  }
                  setFieldValue(name, temp);
                }}
              />
              <CommandBarButton
                text={t('Reset All')}
                className={css.clearButton}
                data-isbutton="button"
                onClick={() => {
                  const temp = [...value];
                  for (const i of temp) {
                    for (const option of i.options) {
                      for (const item of option.examItems) {
                        item.selected = false;
                        item.detail = '';
                      }
                    }
                  }
                  setFieldValue(name, temp);
                }}
              />
            </React.Fragment>
          )}
        </MyField>
      </Stack>

      <HorizontalRule mt={16} mb={16} />

      <FieldText
        name={HEALTH_PHYSICAL_NOTE_FIELDS_NAME.ROS.NOTE}
        title={t('Shortcut/ Manual Entry')}
        fieldClassName="mb-16"
      />

      <MyField name={`${tempKey}Setting`} isFast={false}>
        {({ field, form }) =>
          field.value !== TEMPLATE_ACTION.ALL_NEGATIVE && (
            <LoadingWrapper loading={apiDetail.loading}>
              <Stack tokens={{ childrenGap: 16 }}>
                {get(form.values, dataKey, []).map((item, index) => (
                  <CollapseVertical
                    className={cn({ 'd-none': !item.options.length })}
                    open
                    hr
                    title={item.mentalStatusTypeValue}
                    key={item.mentalStatusTypeId}
                    rightSide={
                      <MyField name={`${dataKey}.${index}.options`}>
                        {({ field: { name, value }, form: { setFieldValue } }) => (
                          <Stack horizontal>
                            <CommandBarButton
                              text={t('Normal')}
                              className={css.normalButton}
                              data-isbutton="button"
                              onClick={() => {
                                const temp = [...value];
                                for (const i of temp) {
                                  for (const item of i.examItems) {
                                    if (item.abnormal === 0) {
                                      item.selected = true;
                                    }
                                  }
                                }
                                setFieldValue(name, temp);
                              }}
                            />
                            <CommandBarButton
                              text={t('Clear')}
                              className={css.clearButton}
                              data-isbutton="button"
                              onClick={() => {
                                const temp = [...value];
                                for (const i of temp) {
                                  for (const item of i.examItems) {
                                    item.selected = false;
                                    item.detail = '';
                                  }
                                }
                                setFieldValue(name, temp);
                              }}
                            />
                          </Stack>
                        )}
                      </MyField>
                    }
                  >
                    <Stack tokens={{ childrenGap: 16 }}>
                      {item.options.map((option, optionIndex) => (
                        <Fragment key={option.mentalStatusId}>
                          <Stack tokens={{ childrenGap: 16 }} horizontal>
                            <div className={cn('text-16-24 weight-600', css.titleWidth)}>
                              {option.mentalStatusDesc}
                            </div>
                            <Stack tokens={{ childrenGap: 24 }}>
                              {!!option.examItems.filter((i) => i.abnormal === null).length && (
                                <ButtonList
                                  options={option.examItems.filter((i) => i.abnormal === null)}
                                  name={`${dataKey}.${index}.options.${optionIndex}`}
                                />
                              )}
                              <ButtonList
                                options={option.examItems.filter((i) => i.abnormal === 0)}
                                name={`${dataKey}.${index}.options.${optionIndex}`}
                              />
                              <ButtonList
                                options={option.examItems.filter((i) => i.abnormal === 1)}
                                name={`${dataKey}.${index}.options.${optionIndex}`}
                              />
                            </Stack>
                          </Stack>
                          {optionIndex !== item.options.length - 1 && <HorizontalRule mt={16} />}
                        </Fragment>
                      ))}
                    </Stack>
                  </CollapseVertical>
                ))}
              </Stack>
            </LoadingWrapper>
          )
        }
      </MyField>
    </div>
  );
}

const TEMPLATE_ACTION = {
  DISPLAY_ALL: 'display_all',
  ALL_NEGATIVE: 'all_negative',
  ALL_NEGATIVE_EXCEPT: 'all_negative_except',
};

const styles = (theme) => ({
  clearButton: {
    color: theme.custom.red6,
    backgroundColor: theme.custom.grey100,
  },
  normalButton: {
    color: theme.custom.green6,
    backgroundColor: theme.custom.grey100,
  },
  titleWidth: {
    minWidth: 250,
    alignSelf: 'center',
  },
  buttonWidth: {
    minWidth: 180,
    textAlign: 'center',
  },
  blue: {
    borderColor: theme.custom.blue3,
  },
  backgroundBlue: {
    backgroundColor: `${theme.custom.blue3}50`,
  },
  green: {
    borderColor: theme.custom.green6,
  },
  backgroundGreen: {
    backgroundColor: `${theme.custom.green6}50`,
  },
  red: {
    borderColor: theme.custom.red6,
  },
  backgroundRed: {
    backgroundColor: `${theme.custom.red6}50`,
  },
});

export default PhysicalExamination;
