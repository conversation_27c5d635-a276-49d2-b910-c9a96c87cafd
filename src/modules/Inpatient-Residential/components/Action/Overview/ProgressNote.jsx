import React from 'react';
import DatePicker from 'components/DatePicker';
import { Stack } from '@fluentui/react';
import SelectorPrimary from 'components/Selector';
import { DEFAULT_PAGINATION_PARAMS } from 'constants/index';
import { SETTING_KEYS } from 'constants/settingKeys';
import { API_PROGRESS_NOTE } from 'constants/urlRequest';
import { useRequest, useSetting } from 'hooks';
import HistoryTable from 'modules/ProgressNote/ProgressNoteContent/History/HistoryTable';
import moment from 'moment';
import { formatToFluentOptions } from 'utils';
import { t } from 'utils/string';
import CollapseVertical from 'components/Collapse/CollapseVertical';
import useProgressNoteTypes from 'hooks/useProgressNoteTypes';

const ProgressNote = ({ patientId }) => {
  const [dateFormat] = useSetting(SETTING_KEYS.DATE_FORMAT);
  const { data } = useProgressNoteTypes();

  const apiHistory = useRequest({
    url: API_PROGRESS_NOTE.HISTORY,
    params: {
      ...DEFAULT_PAGINATION_PARAMS,
      formTypeIds: [],
      FromDateTime: moment().subtract(60, 'days').startOf('day').toISOString(),
      OrderBy: 'createDate',
    },
    requiredParams: { patientId },
    autoRefetch: true,
  });

  return (
    <CollapseVertical open title={t('Progress Note')}>
      <Stack horizontal tokens={{ childrenGap: 12 }} className="mb-16">
        <SelectorPrimary
          multiSelect
          showOptionAll
          label={t('Progress Note Type')}
          options={formatToFluentOptions(data)}
          formatData={(v) => v?.key}
          value={apiHistory.params?.formTypeIds || []}
          onChange={(formTypeIds) => apiHistory?.updateParams({ formTypeIds })}
        />
        <DatePicker
          label={t('Days Back')}
          placeholder={t('Days Back')}
          formatDate={(date) => moment(date).format(dateFormat)}
          value={apiHistory.params.FromDateTime && new Date(apiHistory.params.FromDateTime)}
          onSelectDate={(item) => apiHistory?.updateParams({ FromDateTime: item, Page: 0 })}
          disableAutoFocus={false}
          isDayBack
        />
      </Stack>
      <HistoryTable apiHistory={apiHistory} />
    </CollapseVertical>
  );
};

export default ProgressNote;
