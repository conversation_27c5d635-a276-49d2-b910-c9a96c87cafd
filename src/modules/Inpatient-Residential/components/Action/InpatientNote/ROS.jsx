import React from 'react';
import { <PERSON>Bar<PERSON>utton, PrimaryButton, Stack } from '@fluentui/react';
import cn from 'classnames';
import CalloutConfirmation from 'components/Callout/CalloutConfirmation';
import <PERSON><PERSON>ield from 'components/Form/Field';
import FieldComboBox from 'components/Form/FieldComboBox';
import HorizontalRule from 'components/HorizontalRule';
import LoadingWrapper from 'components/Loading/LoadingWrapper';
import { MODAL_SIZE } from 'constants/modal';
import { TEXT_TEMPLATE } from 'constants/texts';
import { API_METHOD } from 'constants/urlRequest';
import { useClass, useModal, useRequest } from 'hooks';
import { get } from 'lodash';
import TableEmotion from 'modules/ProgressNote/ProgressNoteContent/components/TableEmotion';
import { toast } from 'react-toastify';
import { formatToFluentOptions } from 'utils';
import { t } from 'utils/string';
import AddTemplateModal from './AddTemplateModal';
import ModalConfirm from 'components/Modal/ModalConfirm';
import FieldChoice from 'components/Form/FieldChoice';

function ROS({ data, dataKey, tempKey, apiKey, title = t('Physical Examination'), params }) {
  const css = useClass(styles);
  const { showModal } = useModal();

  const apiTemplate = useRequest({
    url: apiKey.get,
    ...(params && { params, key: `${apiKey.get}-${title}` }),
  });

  const apiDetail = useRequest({ key: dataKey, enabled: false });

  const apiDeleteTemplate = useRequest({
    method: API_METHOD.DELETE,
    onSuccess: () => {
      apiTemplate.refetch();
      toast.success(TEXT_TEMPLATE.DELETE_SUCCESSFULLY());
    },
  });

  const apiCreateTemplate = useRequest({
    url: apiKey.get,
    method: API_METHOD.POST,
    onSuccess: () => {
      apiTemplate.refetch();
      toast.success(TEXT_TEMPLATE.ADD_SUCCESSFULLY());
    },
  });

  const apiUpdateTemplate = useRequest({
    method: API_METHOD.PUT,
    onSuccess: () => {
      apiTemplate.refetch();
      toast.success(TEXT_TEMPLATE.UPDATE_SUCCESSFULLY());
    },
  });

  const setDefaultMental = ({ values, setFieldValue }, result = []) => {
    const temp = get(values, dataKey, []);
    for (const mental of temp) {
      for (const option of mental.options) {
        for (const ans of result) {
          if (ans.mentalStatusId === option.mentalStatusId) {
            option.detail = ans.detail;
            option.abnormal = ans.abnormal;
            option.selected = true;
            break;
          }
        }
      }
    }
    setFieldValue(dataKey, temp);
  };

  const setAllNegative = ({ values, setFieldValue }) => {
    const temp = get(values, dataKey, []);
    for (const mental of temp) {
      for (const option of mental.options) {
        option.abnormal = 0;
        option.selected = true;
      }
    }
    setFieldValue(dataKey, temp);
  };

  const createTemplate =
    (arr = []) =>
    (name) => {
      apiCreateTemplate.mutateAsync({ values: arr, templateName: name });
    };

  return (
    <div
      className={cn(
        css.cmClass.backgroundGrey100,
        css.cmClass.border,
        css.cmClass.borderColor,
        css.cmClass.borderRadius8,
        'p-16',
      )}
    >
      <Stack horizontal tokens={{ childrenGap: 16 }} horizontalAlign="space-between">
        <span className="weight-600 text-16-24">{title}</span>
        <FieldComboBox
          isFast={false}
          options={formatToFluentOptions(apiTemplate?.data)}
          name={tempKey}
          formatValue={(data) => data?.key}
          onChange={async ({ data, form, field }) => {
            form.setFieldValue(field.name, data);
            form.setFieldValue(`${tempKey}Setting`, TEMPLATE_ACTION.DISPLAY_ALL);
            const result = await apiDetail.request({
              url: apiKey.detail(data.key),
            });
            setDefaultMental(form, result?.data.values);
          }}
        />
        <Stack horizontal tokens={{ childrenGap: 16 }}>
          <MyField name={dataKey}>
            {({ field }) => (
              <PrimaryButton
                text={t('Create')}
                data-isbutton="button"
                onClick={() => {
                  showModal({
                    size: MODAL_SIZE.SMALL,
                    content: (
                      <AddTemplateModal
                        onSubmit={createTemplate(field.value)}
                        loading={apiCreateTemplate.loading}
                      />
                    ),
                  });
                }}
              />
            )}
          </MyField>
          <MyField name={tempKey} isFast={false}>
            {({ field, form }) =>
              field.value && (
                <LoadingWrapper loading={apiUpdateTemplate.loading}>
                  <PrimaryButton
                    text={t('Update')}
                    data-isbutton="button"
                    onClick={() =>
                      apiUpdateTemplate.request({
                        url: apiKey.update(field.value.key),
                        payload: {
                          values: get(form.values, dataKey),
                          templateName: field.value.text,
                        },
                      })
                    }
                  />
                </LoadingWrapper>
              )
            }
          </MyField>
          <MyField name={tempKey} isFast={false}>
            {({ field, form }) =>
              field.value && (
                <PrimaryButton
                  text={t('Delete')}
                  data-isbutton="button"
                  onClick={() =>
                    showModal({
                      content: (
                        <ModalConfirm
                          message={TEXT_TEMPLATE.DELETE_CONFIRMATION(t('this template'))}
                          onOk={async () => {
                            await apiDeleteTemplate.request({
                              url: apiKey.delete(field.value.key),
                            });
                            form.setFieldValue(tempKey, null);
                          }}
                        />
                      ),
                      size: MODAL_SIZE.X_SMALL,
                    })
                  }
                />
              )
            }
          </MyField>
        </Stack>
      </Stack>
      <HorizontalRule mt={16} mb={16} />

      <FieldChoice
        name={`${tempKey}Setting`}
        isFast={false}
        horizontal
        options={[
          { key: TEMPLATE_ACTION.ALL_NEGATIVE, text: 'All reviewed and are negative' },
          { key: TEMPLATE_ACTION.ALL_NEGATIVE_EXCEPT, text: 'All reviewed and negative, except' },
          { key: TEMPLATE_ACTION.DISPLAY_ALL, text: 'Display all Physical Examination' },
        ]}
        onChange={({ key }, name, { values, setFieldValue }) => {
          setFieldValue(name, key);
          if (key !== TEMPLATE_ACTION.DISPLAY_ALL) setAllNegative({ values, setFieldValue });
        }}
      />
      <HorizontalRule mt={16} mb={16} />

      <MyField name={`${tempKey}Setting`} isFast={false}>
        {({ field }) =>
          field.value !== TEMPLATE_ACTION.ALL_NEGATIVE && (
            <LoadingWrapper loading={apiDetail.loading}>
              <div className={cn(css.grid)}>
                {get(data, dataKey, []).map((item, index) => (
                  <div key={item.mentalStatusTypeId} className="template-table">
                    <Stack
                      horizontal
                      horizontalAlign="space-between"
                      className={cn(
                        css.headerClassName,
                        css.cmClass.backgroundColor,
                        css.cmClass.border,
                      )}
                    >
                      <Stack.Item grow className={css.title}>
                        {item.mentalStatusTypeValue}
                      </Stack.Item>
                      <MyField name={`${dataKey}.${index}.options`}>
                        {({ field: { name }, form: { setFieldValue } }) => (
                          <CalloutConfirmation
                            description={TEXT_TEMPLATE.CLEAR_CONFIRMATION('this section')}
                            onOk={() => setFieldValue(name, [])}
                          >
                            <CommandBarButton text="Clear" className={css.clearButton} />
                          </CalloutConfirmation>
                        )}
                      </MyField>
                    </Stack>
                    <TableEmotion fieldName={`${dataKey}.${index}`} item={item} />
                  </div>
                ))}
              </div>
            </LoadingWrapper>
          )
        }
      </MyField>
    </div>
  );
}

const TEMPLATE_ACTION = {
  DISPLAY_ALL: 'display_all',
  ALL_NEGATIVE: 'all_negative',
  ALL_NEGATIVE_EXCEPT: 'all_negative_except',
};

const styles = (theme) => ({
  grid: {
    borderRadius: 4,
    display: 'grid',
    gridTemplateColumns: 'repeat(3, 1fr)',
    gridAutoRows: 'auto',
    gap: 16,
    '.full': {
      gridColumn: '1/4',
    },
  },
  headerClassName: {
    padding: '12px 16px',
    borderBottom: '1px solid',
  },
  title: {
    fontWeight: 600,
    fontSize: '16px',
    lineHeight: '20px',
  },
  clearButton: {
    fontSize: '14px',
    lineHeight: '20px',
    color: theme.custom.red6,
  },
});

export default ROS;
