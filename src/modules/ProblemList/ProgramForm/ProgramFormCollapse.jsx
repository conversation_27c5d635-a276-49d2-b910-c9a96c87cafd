import React, { useContext } from 'react';
import CollapseVertical from 'components/Collapse/CollapseVertical';
import { t } from 'utils/string';
import LoadingWrapper from 'components/Loading/LoadingWrapper';
import MyField from 'components/Form/Field';
import { DefaultButton, PrimaryButton, Stack } from '@fluentui/react';
import CalloutConfirmation from 'components/Callout/CalloutConfirmation';
import Text from 'components/Text';
import ELEMENT_ID from 'constants/elementId';
import { ProblemListContext } from 'contexts/ProblemListContext';
import { Formik } from 'formik';
import ProgramFormContent from './ProgramFormContent';
import { API_METHOD, API_PROBLEM } from 'constants/urlRequest';
import { handleValidationBE } from 'utils/form';
import QUERY_KEY from 'constants/queryKey';
import { useModal, useNavigator, useRequest } from 'hooks';
import { toast } from 'react-toastify';
import { TEXT_TEMPLATE } from 'constants/texts';
import { fetchByQueryKey } from 'apis/queryClient';
import ModalConfirm from 'components/Modal/ModalConfirm';
import { MODAL_SIZE } from 'constants/modal';

const ProgramFormCollapse = () => {
  const { editProgramItem, setEditProgramItem } = useContext(ProblemListContext);
  const { searchParams } = useNavigator();
  const { showModal } = useModal();

  const saveProgram = useRequest({
    url: API_PROBLEM.SAVE_PROGRAM_INDICATOR,
    method: API_METHOD.POST,
  });

  const apiProgramHistory = useRequest({
    key: [QUERY_KEY.PROBLEM_PROGRAM_HISTORY, 'check-program-primary'],
    url: API_PROBLEM.GET_PROGRAM_INDICATOR,
    params: { page: 0, pageSize: 1000 },
    requiredParams: {
      PatientId: searchParams.patientId,
    },
    enabled: false,
  });

  const handleResetForm = (form) => {
    form.resetForm({ values: {} });
    setEditProgramItem({});
  };

  const handleRequestSave = async (values, form, primaryProgram) => {
    const isEditing = values?.programHistoryId;
    saveProgram.request({
      payload: { patientId: searchParams?.patientId, ...values, primaryProgram },
      options: {
        onError: (err) => handleValidationBE(form, err),
        onSuccess: () => {
          toast.success(
            isEditing
              ? TEXT_TEMPLATE.UPDATE_SUCCESSFULLY(t('Program'))
              : TEXT_TEMPLATE.ADD_SUCCESSFULLY(t('Program')),
          );
          handleResetForm(form);
          fetchByQueryKey(QUERY_KEY.PROBLEM_PROGRAM_HISTORY);
        },
      },
    });
  };

  const handleSave = async (values, form) => {
    if (!values?.primaryProgram) {
      handleRequestSave(values, form, false);
      return;
    }
    const listProgram = await apiProgramHistory.request();
    const primaryProgramItem = listProgram?.data?.items?.find((i) => i.primaryProgram);
    if (primaryProgramItem) {
      showModal({
        content: (
          <ModalConfirm
            title={t('Primary Program')}
            message={t(
              `${primaryProgramItem?.programIndicatorDesc} is already the Primary Program. Selecting 'Set as Primary' to replace it or Cancel to keep it.`,
            )}
            okText={t('Set as Primary')}
            onOk={() => {
              handleRequestSave(values, form, true);
            }}
            onCancel={(isFromCancelBtn) => {
              if (isFromCancelBtn) {
                handleRequestSave(values, form, false);
              }
            }}
          />
        ),
        size: MODAL_SIZE.X_SMALL,
      });
    } else {
      handleRequestSave(values, form, true);
    }
  };

  return (
    <CollapseVertical
      id={ELEMENT_ID.PROGRAM.INPUT}
      className="mb-16"
      open
      title={t('Program Input')}
    >
      <Formik onSubmit={handleSave} initialValues={editProgramItem} enableReinitialize>
        <LoadingWrapper loading={saveProgram?.loading || apiProgramHistory?.loading}>
          <ProgramFormContent />
          <MyField isFast={false}>
            {({ form }) => (
              <Stack horizontal tokens={{ childrenGap: 16 }} horizontalAlign="end">
                <CalloutConfirmation onOk={() => handleResetForm(form)}>
                  <DefaultButton>{t('Reset')}</DefaultButton>
                </CalloutConfirmation>
                <PrimaryButton
                  text={<Text>Submit</Text>}
                  data-isbutton="button"
                  onClick={form?.handleSubmit}
                />
              </Stack>
            )}
          </MyField>
        </LoadingWrapper>
      </Formik>
    </CollapseVertical>
  );
};

export default ProgramFormCollapse;
