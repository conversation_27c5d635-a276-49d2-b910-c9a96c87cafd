import React, { useCallback, useContext } from 'react';
import { SearchBox, Stack } from '@fluentui/react';
import CollapseVertical from 'components/Collapse/CollapseVertical';
import ModalConfirm from 'components/Modal/ModalConfirm';
import Table from 'components/Table';
import { MODAL_SIZE } from 'constants/modal';
import { API_METHOD } from 'constants/urlRequest';
import { useClass, useModal, useNavigator, useRequest } from 'hooks';
import { toast } from 'react-toastify';
import { debounceFunc } from 'utils/time';
import { DEFAULT_PAGINATION_PARAMS } from 'constants/index';
import { t } from 'utils/string';
import { TEXT_TEMPLATE } from 'constants/texts';
import { MODULE_NAME } from 'constants/routes';
import ELEMENT_ID from 'constants/elementId';
import { FormEditContext } from 'contexts/FormEditContext';
import QUERY_KEY from 'constants/queryKey';
import { getStatus } from 'constants/statusRender';

const FundingSourceHistory = () => {
  const { searchParams, navigate } = useNavigator();
  const css = useClass();
  const { showModal } = useModal();
  const { setEditItem } = useContext(FormEditContext).getEditFunctions(
    MODULE_NAME.FUNDING_SOURCE_INPUT,
  );

  const { data, loading, updateParams, params, refetch } = useRequest({
    key: QUERY_KEY.FUNDING_SOURCE_HISTORY,
    url: '',
    params: {
      ...DEFAULT_PAGINATION_PARAMS,
      OrderBy: 'createDate',
    },
    requiredParams: {
      PatientId: searchParams.patientId,
    },
    autoRefetch: true,
  });

  const apiDelete = useRequest({ method: API_METHOD.DELETE });

  const onSearch = useCallback(
    debounceFunc((searchValue) => {
      updateParams({ SearchTerm: searchValue, Page: 0 });
    }, 500),
    [],
  );

  return (
    <CollapseVertical
      className="mb-24"
      open
      title={t('Funding Source History')}
      id={ELEMENT_ID.FUNDING_SOURCE.HISTORY}
    >
      <Stack tokens={{ childrenGap: 16 }}>
        <SearchBox
          className={css.cmClass.maxWidth300}
          placeholder={t('Search')}
          iconProps={{ iconName: 'Zoom' }}
          onChange={(_, newValue) => onSearch(newValue)}
        />
        <Table
          columns={columns}
          items={data?.items || []}
          loading={loading}
          totalItems={data?.totalItems}
          metadata={params}
          onMetadataChange={updateParams}
          onEdit={() => {
            setEditItem(item);
            navigate({ hash: MODULE_NAME.FUNDING_SOURCE_INPUT });
          }}
          refetch={refetch}
          onDelete={(item) => {
            showModal({
              content: (
                <ModalConfirm
                  message={TEXT_TEMPLATE.DELETE_CONFIRMATION(t('this Funding Source'))}
                  onOk={async () => {
                    await apiDelete.request({
                      url: ``,
                      options: {
                        onSuccess: () => {
                          toast.success(TEXT_TEMPLATE.DELETE_SUCCESSFULLY(t('The Funding Source')));
                          refetch();
                        },
                      },
                    });
                  }}
                />
              ),
              size: MODAL_SIZE.X_SMALL,
            });
          }}
        />
      </Stack>
    </CollapseVertical>
  );
};

export default FundingSourceHistory;

const columns = [
  {
    name: 'Funding Source',
    fieldName: 'fundingSource',
    sortable: true,
  },
  {
    name: 'Authorization',
    fieldName: 'authorization',
    sortable: true,
  },
  {
    name: 'Authorization Entry',
    fieldName: 'authorizationEntry',
    sortable: true,
  },
  {
    name: 'Effective Date',
    fieldName: 'effectiveDate',
    sortable: true,
    renderItem: ({ effectiveDate }, _, c, render) => render({ date: effectiveDate }),
  },
  {
    name: 'End Date',
    fieldName: 'endDate',
    sortable: true,
    renderItem: ({ endDate }, _, c, render) => render({ date: endDate }),
  },
  {
    name: 'Status',
    fieldName: 'status',
    sortable: true,
    renderItem: ({ status }) => getStatus(status === 1 ? 'Approved' : 'Pending'),
  },
  {
    name: 'Comment',
    fieldName: 'comment',
    sortable: true,
  },
  { key: 'Action', name: 'Action', fieldName: 'action' },
];
