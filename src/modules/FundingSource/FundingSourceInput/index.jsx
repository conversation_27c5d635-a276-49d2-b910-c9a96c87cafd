import React, { useContext, useEffect } from 'react';
import { FormProvider } from 'react-hook-form';
import { toast } from 'react-toastify';
import useHookForm from 'components/HookForm/useHookForm';
import CollapseVertical from 'components/Collapse/CollapseVertical';
import { useNavigator, useRequest } from 'hooks';
import { API_METHOD } from 'constants/urlRequest';
import { handleValidationBE } from 'utils/form';
import { t } from 'utils/string';
import { TEXT_TEMPLATE } from 'constants/texts';
import { MODULE_NAME } from 'constants/routes';
import ELEMENT_ID from 'constants/elementId';
import { FormEditContext } from 'contexts/FormEditContext';
import FieldSelectorPrimary from 'components/HookForm/FieldSelectorPrimary';
import FieldDate from 'components/HookForm/FieldDate';
import FieldText from 'components/HookForm/FieldText';
import { DefaultButton, PrimaryButton, Stack } from '@fluentui/react';
import CalloutConfirmation from 'components/Callout/CalloutConfirmation';
import ResponsiveGrid from 'components/Layout/Responsive/Container';
import ResponsiveItem from 'components/Layout/Responsive/Item';
import LoadingWrapper from 'components/Loading/LoadingWrapper';

const FundingSourceInput = () => {
  const { searchParams, navigate } = useNavigator();

  const { editItem, setEditItem } = useContext(FormEditContext).getEditFunctions(
    MODULE_NAME.FUNDING_SOURCE_INPUT,
  );

  const defaultValues = {
    patientId: searchParams.patientId,
    fundingSource: '',
    authorization: '',
    authorizationEntry: '',
    effectiveDate: null,
    endDate: null,
    status: '',
    comment: '',
  };

  const methods = useHookForm({
    defaultValues: editItem || defaultValues,
    dependencies: { editItem },
  });

  const apiSave = useRequest({
    url: '/api/funding-source',
    method: API_METHOD.POST,
  });

  const apiUpdate = useRequest({
    url: '/api/funding-source',
    method: API_METHOD.PUT,
  });

  useEffect(() => {
    if (editItem) {
      methods.reset(editItem);
    }
  }, [editItem, methods]);

  const onReset = () => {
    methods.reset(editItem || defaultValues);
  };

  const onSubmit = (values) => {
    const payload = {
      ...values,
      patientId: searchParams.patientId,
    };

    const apiRequest = editItem ? apiUpdate : apiSave;
    const url = editItem ? `${editItem.id}` : '';

    apiRequest.request({
      url,
      payload,
      options: {
        onSuccess: () => {
          toast.success(TEXT_TEMPLATE.SAVE_SUCCESSFULLY(t('Funding Source')));
          setEditItem(null);
          navigate({ hash: MODULE_NAME.FUNDING_SOURCE_HISTORY });
        },
        onError: (err) => {
          handleValidationBE({ setError: methods.setError }, err);
        },
      },
    });
  };

  return (
    <FormProvider {...methods}>
      <CollapseVertical
        className="mb-24"
        open
        title={t('Funding Source Input')}
        id={ELEMENT_ID.FUNDING_SOURCE?.INPUT}
      >
        <LoadingWrapper loading={apiSave.loading}>
          <ResponsiveGrid>
            <ResponsiveItem md={4}>
              <FieldSelectorPrimary
                title={t('Funding Source')}
                name="fundingSource"
                options={[]}
                required
              />
            </ResponsiveItem>
            <ResponsiveItem md={4}>
              <FieldSelectorPrimary title={t('Authorization')} name="authorization" options={[]} />
            </ResponsiveItem>
            <ResponsiveItem md={4}>
              <FieldSelectorPrimary
                title={t('Authorization Entry')}
                name="authorizationEntry"
                options={[]}
              />
            </ResponsiveItem>

            <ResponsiveItem md={4}>
              <FieldDate title={t('Effective Date')} name="effectiveDate" />
            </ResponsiveItem>
            <ResponsiveItem md={4}>
              <FieldDate title={t('End Date')} name="endDate" />
            </ResponsiveItem>
            <ResponsiveItem md={4}>
              <FieldSelectorPrimary title={t('Status')} name="status" options={[]} />
            </ResponsiveItem>

            <ResponsiveItem md={12}>
              <FieldText title={t('Comment')} name="comment" rows={4} />
            </ResponsiveItem>

            <ResponsiveItem md={12}>
              <Stack horizontal tokens={{ childrenGap: 16 }}>
                <PrimaryButton text={t('Submit')} onClick={methods.handleSubmit(onSubmit)} />
                <CalloutConfirmation onOk={onReset}>
                  <DefaultButton text={t('Cancel')} />
                </CalloutConfirmation>
              </Stack>
            </ResponsiveItem>
          </ResponsiveGrid>
        </LoadingWrapper>
      </CollapseVertical>
    </FormProvider>
  );
};

export default FundingSourceInput;
