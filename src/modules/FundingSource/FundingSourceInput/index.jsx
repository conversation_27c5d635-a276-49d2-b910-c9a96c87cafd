import React, { useContext, useEffect } from 'react';
import { DefaultButton, PrimaryButton } from '@fluentui/react';
import { FormProvider } from 'react-hook-form';
import { toast } from 'react-toastify';
import useHookForm from 'components/HookForm/useHookForm';
import FieldSelectorPrimary from 'components/HookForm/FieldSelectorPrimary';
import FieldDate from 'components/HookForm/FieldDate';
import FieldTextArea from 'components/HookForm/FieldTextArea';
import CollapseVertical from 'components/Collapse/CollapseVertical';
import CalloutConfirmation from 'components/Callout/CalloutConfirmation';
import { useNavigator, useRequest } from 'hooks';
import { API_METHOD } from 'constants/urlRequest';
import { handleValidationBE } from 'utils/form';
import { t } from 'utils/string';
import { TEXT_TEMPLATE } from 'constants/texts';
import { MODULE_NAME } from 'constants/routes';
import useClass from 'hooks/useClass';
import ELEMENT_ID from 'constants/elementId';
import { FormEditContext } from 'contexts/FormEditContext';

const FundingSourceInput = () => {
  const css = useClass();
  const { searchParams, navigate } = useNavigator();

  const { editItem, setEditItem } = useContext(FormEditContext).getEditFunctions(
    MODULE_NAME.FUNDING_SOURCE_INPUT,
  );

  const defaultValues = {
    patientId: searchParams.patientId,
    fundingSource: '',
    authorization: '',
    authorizationEntry: '',
    effectiveDate: null,
    endDate: null,
    status: '',
    comment: '',
  };

  const methods = useHookForm({
    defaultValues: editItem || defaultValues,
    dependencies: { editItem },
  });

  const apiSave = useRequest({
    url: '',
    method: API_METHOD.POST,
  });

  const apiUpdate = useRequest({
    url: '',
    method: API_METHOD.PUT,
  });

  useEffect(() => {
    if (editItem) {
      methods.reset(editItem);
    }
  }, [editItem, methods]);

  const onReset = () => {
    methods.reset(editItem || defaultValues);
  };

  const onSubmit = (values) => {
    const payload = {
      ...values,
      patientId: searchParams.patientId,
    };

    const apiRequest = editItem ? apiUpdate : apiSave;
    const url = editItem ? `${editItem.id}` : '';

    apiRequest.request({
      url,
      payload,
      options: {
        onSuccess: () => {
          toast.success(TEXT_TEMPLATE.SAVE_SUCCESSFULLY(t('Funding Source')));
          setEditItem(null);
          navigate({ hash: MODULE_NAME.FUNDING_SOURCE_HISTORY });
        },
        onError: (err) => {
          handleValidationBE({ setError: methods.setError }, err);
        },
      },
    });
  };

  return (
    <CollapseVertical
      className="mb-24"
      open
      title={t('Funding Source Input')}
      id={ELEMENT_ID.FUNDING_SOURCE.INPUT}
    >
      <FormProvider {...methods}>
        <div className="p-16">
          <div className={css.cmClass.grid3Columns}>
            <FieldSelectorPrimary
              title={t('Funding Source')}
              name="fundingSource"
              options={[]}
              required
            />
            <FieldSelectorPrimary title={t('Authorization')} name="authorization" options={[]} />
            <FieldSelectorPrimary
              title={t('Authorization Entry')}
              name="authorizationEntry"
              options={[]}
            />
          </div>

          <div className={css.cmClass.grid3Columns}>
            <FieldDate title={t('Effective Date')} name="effectiveDate" placeholder="mm/dd/yyyy" />
            <FieldDate title={t('End Date')} name="endDate" placeholder="mm/dd/yyyy" />
            <FieldSelectorPrimary title={t('Status')} name="status" options={[]} />
          </div>

          <div className="mt-16">
            <FieldTextArea title={t('Comment')} name="comment" rows={4} />
          </div>

          <div className="mt-24 d-flex gap-8">
            <PrimaryButton
              text={t('Submit')}
              onClick={methods.handleSubmit(onSubmit)}
              disabled={apiSave.loading || apiUpdate.loading}
            />
            <CalloutConfirmation onOk={onReset}>
              <DefaultButton text={t('Cancel')} />
            </CalloutConfirmation>
          </div>
        </div>
      </FormProvider>
    </CollapseVertical>
  );
};

export default FundingSourceInput;
