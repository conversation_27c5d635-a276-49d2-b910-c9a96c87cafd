import React, { Fragment, useContext, useEffect } from 'react';
import { DefaultButton, PrimaryButton } from '@fluentui/react';
import { FormProvider } from 'react-hook-form';
import { toast } from 'react-toastify';
import useHookForm from 'components/HookForm/useHookForm';
import FieldSelectorPrimary from 'components/HookForm/FieldSelectorPrimary';
import FieldDate from 'components/HookForm/FieldDate';
import FieldTextArea from 'components/HookForm/FieldTextArea';
import CollapseVertical from 'components/Collapse/CollapseVertical';
import CalloutConfirmation from 'components/Callout/CalloutConfirmation';
import { useModal, useNavigator, useRequest } from 'hooks';
import { API_METHOD } from 'constants/urlRequest';
import { handleValidationBE } from 'utils/form';
import { t } from 'utils/string';
import { TEXT_TEMPLATE } from 'constants/texts';
import { MODULE_NAME } from 'constants/routes';
import { FormEditContext } from 'contexts/FormEditContext';
import useClass from 'hooks/useClass';
import ELEMENT_ID from 'constants/elementId';

const FundingSourceInput = () => {
  const css = useClass();
  const { searchParams, navigate } = useNavigator();
  const { hideModal } = useModal();

  const { editItem, setEditItem } = useContext(FormEditContext).getEditFunctions(
    MODULE_NAME.FUNDING_SOURCE_INPUT,
  );

  // Mock options - replace with actual API calls
  const fundingSourceOptions = [
    { key: 'medicaid', text: 'Medicaid' },
    { key: 'medicare', text: 'Medicare' },
    { key: 'private_insurance', text: 'Private Insurance' },
    { key: 'self_pay', text: 'Self Pay' },
    { key: 'grant', text: 'Grant' },
  ];

  const authorizationOptions = [
    { key: 'prior_auth', text: 'Prior Authorization' },
    { key: 'concurrent_review', text: 'Concurrent Review' },
    { key: 'retrospective_review', text: 'Retrospective Review' },
    { key: 'none', text: 'None Required' },
  ];

  const statusOptions = [
    { key: 'pending', text: 'Pending' },
    { key: 'approved', text: 'Approved' },
    { key: 'denied', text: 'Denied' },
    { key: 'expired', text: 'Expired' },
  ];

  const defaultValues = {
    patientId: searchParams.patientId,
    fundingSource: '',
    authorization: '',
    authorizationEntry: '',
    effectiveDate: null,
    endDate: null,
    status: '',
    comment: '',
  };

  const methods = useHookForm({
    defaultValues: editItem || defaultValues,
    dependencies: { editItem },
  });

  const apiSave = useRequest({
    url: '/api/funding-source', // TODO: Replace with actual API endpoint
    method: API_METHOD.POST,
  });

  const apiUpdate = useRequest({
    url: '/api/funding-source', // TODO: Replace with actual API endpoint
    method: API_METHOD.PUT,
  });

  useEffect(() => {
    if (editItem) {
      methods.reset(editItem);
    }
  }, [editItem, methods]);

  const onReset = () => {
    methods.reset(editItem || defaultValues);
  };

  const onSubmit = (values) => {
    const payload = {
      ...values,
      patientId: searchParams.patientId,
    };

    const apiRequest = editItem ? apiUpdate : apiSave;
    const url = editItem ? `/api/funding-source/${editItem.id}` : '/api/funding-source';

    apiRequest.request({
      url,
      payload,
      options: {
        onSuccess: () => {
          toast.success(TEXT_TEMPLATE.SAVE_SUCCESSFULLY(t('Funding Source')));
          setEditItem(null);
          navigate({ hash: MODULE_NAME.FUNDING_SOURCE_HISTORY });
        },
        onError: (err) => {
          handleValidationBE({ setError: methods.setError }, err);
        },
      },
    });
  };

  const onCancel = () => {
    setEditItem(null);
    navigate({ hash: MODULE_NAME.FUNDING_SOURCE_HISTORY });
  };

  return (
    <FormProvider {...methods}>
      <CollapseVertical
        className="mb-24"
        open
        title={t('Funding Source Input')}
        id={ELEMENT_ID.FUNDING_SOURCE?.INPUT} // TODO: Add to elementId constants
      >
        <div className="p-16">
          <div className={css.cmClass.grid3Columns}>
            <FieldSelectorPrimary
              title={t('Funding Source')}
              name="fundingSource"
              options={fundingSourceOptions}
              required
            />
            <FieldSelectorPrimary
              title={t('Authorization')}
              name="authorization"
              options={authorizationOptions}
            />
            <FieldSelectorPrimary
              title={t('Authorization Entry')}
              name="authorizationEntry"
              options={[]} // TODO: Add authorization entry options
            />
          </div>

          <div className={css.cmClass.grid3Columns}>
            <FieldDate title={t('Effective Date')} name="effectiveDate" placeholder="mm/dd/yyyy" />
            <FieldDate title={t('End Date')} name="endDate" placeholder="mm/dd/yyyy" />
            <FieldSelectorPrimary title={t('Status')} name="status" options={statusOptions} />
          </div>

          <div className="mt-16">
            <FieldTextArea title={t('Comment')} name="comment" rows={4} />
          </div>

          <div className="mt-24 d-flex gap-8">
            <PrimaryButton
              text={t('Submit')}
              onClick={methods.handleSubmit(onSubmit)}
              disabled={apiSave.loading || apiUpdate.loading}
            />
            <CalloutConfirmation onOk={onReset}>
              <DefaultButton text={t('Cancel')} />
            </CalloutConfirmation>
          </div>
        </div>
      </CollapseVertical>
    </FormProvider>
  );
};

export default FundingSourceInput;
