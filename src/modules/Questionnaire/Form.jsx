import React, { useMemo } from 'react';
import { API_METHOD, API_QUESTIONNAIRE } from 'constants/urlRequest';
import useRequest from 'hooks/useRequest';
import useNavigator from 'hooks/useNavigator';
import { t } from 'utils/string';
import { Formik } from 'formik';
import LoadingWrapper from 'components/Loading/LoadingWrapper';
import { DefaultButton, PrimaryButton, Stack } from '@fluentui/react';
import HorizontalRule from 'components/HorizontalRule';
import { formatSubmit, getAnswer } from 'modules/PRAPARE/PRAPAREForm/constant';
import MyField from 'components/Form/Field';
import CalloutConfirmation from 'components/Callout/CalloutConfirmation';
import { toast } from 'react-toastify';
import { TEXT_TEMPLATE } from 'constants/texts';
import cn from 'classnames';
import useClass from 'hooks/useClass';
import FieldDate from 'components/Form/FieldDate';

const today = new Date();

const Questionnaire = ({ formDesc = '', editItem, setEditItem }) => {
  const css = useClass();
  const { searchParams } = useNavigator();

  const { data = [], loading } = useRequest({
    url: API_QUESTIONNAIRE.GET_QUESTIONNAIRE,
    requiredParams: {
      formDesc,
    },
  });

  const questions = useMemo(() => {
    return data.map((ques) => ({
      ...ques,
      answerId: editItem?.answers?.find((i) => i.questionId === ques.questionId)?.answerId,
      answers: ques.answers.map((ans) => ({
        ...ans,
        ...(editItem?.answers?.find(
          (i) => i.answerId === ans.answerId && ques.questionId === i.questionId,
        ) || {}),
        selected: editItem?.answers?.find(
          (i) => i.answerId === ans.answerId && ques.questionId === i.questionId,
        ),
        answerSpecialType: ans.answerSpecialType || ques.answerType,
      })),
    }));
  }, [data, editItem]);

  const { request, loading: loadingSubmit } = useRequest({
    url: API_QUESTIONNAIRE.DEFAULT(),
    method: API_METHOD.POST,
  });

  const onSubmit = (values, form) => {
    request({
      payload: { ...values, answers: formatSubmit(values) },
      options: {
        onSuccess: () => {
          toast.success(TEXT_TEMPLATE.SAVE_SUCCESSFULLY(t('Questionnaire')));
          setEditItem();
          form.setValues({
            formDate: today,
            formName: formDesc,
            patientId: searchParams.patientId,
            questions,
          });
        },
      },
    });
  };

  return (
    <div
      className={cn(
        'mt-16',
        css.cmClass.border,
        css.cmClass.borderRadius4,
        css.cmClass.backgroundGrey100,
      )}
    >
      <Formik
        initialValues={{
          formDate: editItem?.formDate || today,
          batchId: editItem?.batchId,
          formName: formDesc,
          patientId: searchParams.patientId,
          questions: questions,
        }}
        onSubmit={onSubmit}
        enableReinitialize
      >
        <LoadingWrapper loading={loading || loadingSubmit}>
          <Stack tokens={{ padding: 16, childrenGap: 16 }}>
            <FieldDate
              name="formDate"
              title={t('Form Date')}
              fieldClassName={css.cmClass.maxWidth300}
            />
            {questions.map((ques, questionIndex) => (
              <div key={ques.questionId}>
                <span className="weight-600">
                  {ques.precedence}. {ques.questionText}
                </span>
                <Stack className="p-8" tokens={{ childrenGap: 4 }}>
                  {ques?.answers?.map((ans, answerIndex) => (
                    <React.Fragment key={ans.answerId}>
                      {getAnswer(ans, `questions.${questionIndex}`, answerIndex)}
                    </React.Fragment>
                  ))}
                </Stack>
              </div>
            ))}
            <HorizontalRule mt={16} />
            <MyField>
              {({ form }) => (
                <Stack horizontal tokens={{ childrenGap: 12 }}>
                  <PrimaryButton
                    text={t('Submit')}
                    data-isbutton="button"
                    onClick={form.submitForm}
                  />
                  <CalloutConfirmation
                    onOk={() => {
                      form.setValues({
                        formDate: today,
                        formName: formDesc,
                        patientId: searchParams.patientId,
                        questions,
                      });
                    }}
                  >
                    <DefaultButton text={t('Reset')} />
                  </CalloutConfirmation>
                </Stack>
              )}
            </MyField>
          </Stack>
        </LoadingWrapper>
      </Formik>
    </div>
  );
};

export default Questionnaire;
