import React, { useState } from 'react';
import useRequest from 'hooks/useRequest';
import { API_QUESTIONNAIRE } from 'constants/urlRequest';
import CollapseVertical from 'components/Collapse/CollapseVertical';
import { t } from 'utils/string';
import Table from 'components/Table';
import { DEFAULT_PAGINATION_PARAMS } from 'constants/index';

const ActiveQuestionnaires = ({ setFormDesc }) => {
  const [metadata, setMetadata] = useState(DEFAULT_PAGINATION_PARAMS);

  const { data, loading } = useRequest({
    url: API_QUESTIONNAIRE.DEFAULT(),
  });

  const onEdit = (item) => {
    setFormDesc(item.description);
  };

  return (
    <CollapseVertical title={t('Active Questionnaires')} open className="mb-24">
      <Table
        items={data?.slice(
          metadata?.Page * metadata?.PageSize,
          metadata?.Page * metadata?.PageSize + metadata?.PageSize,
        )}
        totalItems={data?.length || 0}
        onMetadataChange={(newValue) => setMetadata((oldValue) => ({ ...oldValue, ...newValue }))}
        metadata={metadata}
        loading={loading}
        columns={columns}
        onEdit={onEdit}
      />
    </CollapseVertical>
  );
};

export default ActiveQuestionnaires;

const columns = [
  { fieldName: 'key', name: t('ID'), minWidth: 100 },
  { fieldName: 'description', name: t('Form Name') },
  { key: 'Action', fieldName: 'Action', name: t('Action') },
];
