import React, { useState } from 'react';
import useRequest from 'hooks/useRequest';
import { API_METHOD, API_QUESTIONNAIRE } from 'constants/urlRequest';
import { t } from 'utils/string';
import Table from 'components/Table';
import { DEFAULT_PAGINATION_PARAMS } from 'constants/index';
import useNavigator from 'hooks/useNavigator';
import useModal from 'hooks/useModal';
import ModalConfirm from 'components/Modal/ModalConfirm';
import { TEXT_TEMPLATE } from 'constants/texts';
import { toast } from 'react-toastify';
import { MODAL_SIZE } from 'constants/modal';
import { onPrint } from 'utils/file';
import { parseFormatDateBE } from 'utils/time';
import useSetting from 'hooks/useSetting';
import { SETTING_KEYS } from 'constants/settingKeys';
import { getStatus } from 'constants/statusRender';

const QuestionnaireHistory = ({ formDesc, setEditItem, security }) => {
  const { searchParams } = useNavigator();
  const { showModal } = useModal();
  const [DATE_FORMAT] = useSetting(SETTING_KEYS.DATE_FORMAT);

  const { data, loading, params, updateParams, refetch } = useRequest({
    url: API_QUESTIONNAIRE.GET_HISTORY,
    requiredParams: { formDesc },
    params: { ...DEFAULT_PAGINATION_PARAMS, patientId: searchParams.patientId },
  });

  const { request: deleteRequest } = useRequest({
    url: API_QUESTIONNAIRE.DEFAULT(),
    method: API_METHOD.DELETE,
  });

  const handleDelete = (item) => {
    if (security?.userDelete === false) {
      toast.error(TEXT_TEMPLATE.NO_PERMISSION, { toastId: formDesc });
      return;
    }
    showModal({
      content: (
        <ModalConfirm
          message={TEXT_TEMPLATE.DELETE_CONFIRMATION(t('Questionnaire'))}
          onOk={async () => {
            await deleteRequest({ url: API_QUESTIONNAIRE.DEFAULT(item.batchId) });
            refetch();
            toast.success(TEXT_TEMPLATE.DELETE_SUCCESSFULLY(t('Questionnaire')));
          }}
        />
      ),
      size: MODAL_SIZE.X_SMALL,
    });
  };

  const handlePrint = (item) => {
    if (security?.userPrint === false) {
      toast.error(TEXT_TEMPLATE.NO_PERMISSION, { toastId: formDesc });
      return;
    }
    onPrint({
      url: API_QUESTIONNAIRE.PRINT_QUESTIONNAIRE,
      queryString: {
        batchId: item.batchId,
        dateFormatString: parseFormatDateBE(DATE_FORMAT),
      },
    });
  };

  const handleEdit = (item) => {
    if (security?.userEdit === false) {
      toast.error(TEXT_TEMPLATE.NO_PERMISSION, { toastId: formDesc });
      return;
    }
    setEditItem(item);
  };

  const getMenuProps = () => ({
    items: [
      { key: 'edit', text: t('Edit'), onClick: handleEdit },
      { key: 'delete', text: t('Delete'), onClick: handleDelete },
      { key: 'print', text: t('Print'), onClick: handlePrint },
    ],
  });

  return (
    <Table
      items={data?.items}
      totalItems={data?.totalItems || 0}
      onMetadataChange={updateParams}
      metadata={params}
      loading={loading}
      columns={columns}
      getMenuProps={getMenuProps}
      refetch={refetch}
    />
  );
};

export default QuestionnaireHistory;

const columns = [
  { fieldName: 'batchId', name: t('ID'), minWidth: 100 },
  {
    fieldName: 'status',
    name: t('Status'),
    renderItem: ({ status, deleted }) => getStatus(deleted ? 'Deleted' : status),
  },
  { fieldName: 'severity', name: t('Severity') },
  {
    fieldName: 'formDate',
    name: t('Form Date'),
    renderItem: (i, _, c, render) => render({ date: i.formDate }),
  },
  {
    fieldName: 'createDate',
    name: t('Created Date'),
    renderItem: (i, _, c, render) => render({ date: i.createDate, user: i.createUser }),
  },
  {
    fieldName: 'updateDate',
    name: t('Updated Date'),
    renderItem: (i, _, c, render) => render({ date: i.updateDate, user: i.updateUser }),
  },
  { key: 'Action', fieldName: 'Action', name: t('Action') },
];
