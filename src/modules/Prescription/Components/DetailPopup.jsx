import React from 'react';
import moment from 'moment';
import { SETTING_KEYS } from 'constants/settingKeys';
import { useClass, useNavigator, useRequest, useSetting } from 'hooks';
import { DefaultButton, Stack } from '@fluentui/react';
import HorizontalRule from 'components/HorizontalRule';
import { t } from 'utils/string';
import cn from 'classnames';
import { printHtml } from 'utils';
import LoadingWrapper from 'components/Loading/LoadingWrapper';
import { API_MEDICATION } from 'constants/urlRequest';
import { parseFormatDateBE } from 'utils/time';

const DetailPopup = ({ medication = {} }) => {
  const css = useClass(styles);
  const { searchParams } = useNavigator();
  const [DATE_FORMAT] = useSetting(SETTING_KEYS.DATE_FORMAT);

  const apiPrint = useRequest({
    url: API_MEDICATION.PRINT_MEDICATION,
    requiredParams: {
      dateFormatString: parseFormatDateBE(DATE_FORMAT),
      patientId: searchParams.patientId,
      prescriptionId: medication.prescriptionId,
    },
    enabled: false,
    autoRefetch: true,
  });

  const apiEducation = useRequest({
    url: API_MEDICATION.PATIENT_EDUCATION,
    requiredParams: {
      patientId: searchParams.patientId,
      drugId: medication.drugId,
    },
    enabled: false,
    autoRefetch: true,
  });

  const apiLexicomp = useRequest({
    url: API_MEDICATION.LEXICOMP_PAMPHLET,
    requiredParams: {
      drugId: medication.drugId,
    },
    enabled: false,
    autoRefetch: true,
  });

  const menuProps = {
    items: [
      {
        key: 'patientEducation',
        text: t('Patient Education'),
        onClick: async () => {
          const res = await apiEducation.request();
          return !res.isError && window.open(res?.data, '_blank');
        },
      },
      // #16199
      // {
      //   key: 'lexicompPamphlet',
      //   text: t('Lexicomp Pamphlet'),
      //   onClick: async () => {
      //     const res = await apiLexicomp.request();
      //     if (res.isError) return;

      //     const newWindow = window.open(null, '_blank');
      //     newWindow.document.write(res?.data);
      //   },
      // },
      {
        key: 'printCopy',
        text: t('Print Copy'),
        onClick: async () => {
          const res = await apiPrint.request();
          return !res.isError && printHtml(res?.data);
        },
      },
    ],
  };

  return (
    <div className={css.calloutWrapper}>
      <section className="color-gray-1100 weight-600">
        {medication.drugName} {medication.fullStrength}
      </section>
      <ul className={css.calloutList}>
        <li>
          <span>{t('Category:')}</span>
          <span>{medication.medicationCategoryName}</span>
        </li>
        <li>
          <span>{t('SIG:')}</span>
          <span className="description">{medication.directions}</span>
        </li>
        <li>
          <span>{t('Quantiy:')}</span>
          {medication.quantity}
        </li>
        <li>
          <span>{t('Days:')}</span>
          {medication.expectedDuration}
        </li>
        <li>
          <span>{t('Refills:')}</span>
          {medication.refills}
        </li>
        <li>
          <span>{t('Provider:')}</span>
          {medication.provider}
        </li>
        <li>
          <span>{t('Start date:')}</span>
          {moment(medication.startDate).format(DATE_FORMAT)}
        </li>
        <li>
          <span>{t('Renew by:')}</span>
          {moment(medication.renewDate).format(DATE_FORMAT)}
        </li>
        <li>
          <span>{t('Status:')}</span>
          <span className={cn({ 'color-error': medication.stopDate })}>
            {medication.stopDate ? 'Discontinue' : 'Active'}
          </span>
        </li>
        <li>
          <span>{t('Reason:')}</span>
          {medication.discontinueReason || '-'}
        </li>
      </ul>
      <HorizontalRule mt={16} mb={16} />
      <Stack horizontalAlign="end">
        <LoadingWrapper loading={apiPrint.loading || apiEducation.loading || apiLexicomp.loading}>
          <DefaultButton text={t('Action')} menuProps={menuProps} />
        </LoadingWrapper>
      </Stack>
    </div>
  );
};

export default DetailPopup;

const styles = () => ({
  calloutWrapper: {
    padding: '12px 16px',
  },
  calloutList: {
    paddingLeft: 20,
    display: 'flex',
    flexDirection: 'column',
    gap: 8,
    span: { display: 'inline-flex', width: 100 },
    '.description': {
      width: 200,
    },
  },
});
