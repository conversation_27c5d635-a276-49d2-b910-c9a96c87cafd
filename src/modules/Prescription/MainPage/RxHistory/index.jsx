import { Dropdown, SearchBox, Stack } from '@fluentui/react';
import GroupTableDetail from 'components/GroupDetailTable';
import { DEFAULT_PAGINATION_PARAMS } from 'constants/index';
import QUERY_KEY from 'constants/queryKey';
import { SETTING_KEYS } from 'constants/settingKeys';
import { API_MEDICATION } from 'constants/urlRequest';
import { useClass, useNavigator, useRequest, useSetting } from 'hooks';
import React, { useCallback } from 'react';
import { printHtml } from 'utils';
import { t } from 'utils/string';
import { debounceFunc, parseFormatDateBE } from 'utils/time';
import { RenderType } from '../components/ColumnsCustom';
import RenderDetail from '../components/DetailMedicationRow';
import CollapseVertical from 'components/Collapse/CollapseVertical';

const RxHistory = () => {
  const css = useClass(styles);
  const [formatDate] = useSetting(SETTING_KEYS.DATE_FORMAT);
  const { searchParams } = useNavigator();

  const { data, isFetching, updateParams, params } = useRequest({
    key: QUERY_KEY.RX_HISTORY,
    url: API_MEDICATION.RX_HISTORY,
    params: {
      ...DEFAULT_PAGINATION_PARAMS,
      PageSize: 10,
      isGroupByDate: false,
      status: '',
    },
    requiredParams: {
      patientId: searchParams.patientId,
    },
    autoRefetch: true,
  });

  const apiPrint = useRequest({
    url: API_MEDICATION.PRINT_MEDICATION,
    requiredParams: {
      dateFormatString: parseFormatDateBE(formatDate),
      patientId: searchParams.patientId,
    },
    enabled: false,
    autoRefetch: true,
  });

  const apiEducation = useRequest({
    url: API_MEDICATION.PATIENT_EDUCATION,
    requiredParams: {
      patientId: searchParams.patientId,
    },
    enabled: false,
    autoRefetch: true,
  });

  const apiLexicomp = useRequest({ url: API_MEDICATION.LEXICOMP_PAMPHLET, enabled: false });

  const onSearch = useCallback(
    debounceFunc((searchTerm) => {
      updateParams({ searchTerm, Page: 0 });
    }, 500),
    [],
  );

  const onFilter = (_, item) => {
    updateParams({ status: item.key });
  };

  const getMenu = () => ({
    items: [
      {
        key: 'patientEducation',
        text: t('Patient Education'),
        onClick: async ({ drugId }) => {
          const res = await apiEducation.request({ queryString: { drugId } });
          return !res.isError && window.open(res?.data, '_blank');
        },
      },
      //  #16199
      // {
      //   key: 'lexicompPamphlet',
      //   text: t('Lexicomp Pamphlet'),
      //   onClick: async ({ drugId }) => {
      //     const res = await apiLexicomp.request({ queryString: { drugId } });
      //     if (res.isError) return;

      //     const newWindow = window.open(null, '_blank');
      //     newWindow.document.write(res?.data);
      //   },
      // },
      {
        key: 'printCopy',
        text: t('Print Copy'),
        onClick: async ({ prescriptionId }) => {
          const res = await apiPrint.request({ queryString: { prescriptionId } });
          return !res.isError && printHtml(res?.data);
        },
      },
    ],
  });

  return (
    <CollapseVertical
      open
      className="mb-24"
      title={t('Prescription History') + ` (${data?.totalItems || 0})`}
    >
      <Stack horizontal tokens={{ childrenGap: 12 }} className="mb-16">
        <SearchBox
          iconProps={{ iconName: 'Zoom' }}
          onChange={(_, value) => onSearch(value)}
          placeholder={t('Search...')}
        />
        <Dropdown
          selectedKey={params.status}
          placeholder={t('Select an option')}
          options={[
            { key: '', text: 'All' },
            { key: 0, text: 'Active' },
            { key: 1, text: 'Expired' },
            { key: 2, text: 'Discontinued' },
          ]}
          onChange={onFilter}
          styles={{ root: { width: 120 } }}
        />
      </Stack>
      <GroupTableDetail
        onRenderDetail={(item) => <RenderDetail item={item} />}
        columns={columnCurrentRx}
        loading={isFetching || apiPrint.loading || apiEducation.loading || apiLexicomp.loading}
        pagination
        metadata={params}
        onMetadataChange={updateParams}
        totalItems={data?.totalItems}
        initalItems={data?.items?.[0]?.medications || []}
        getMenuProps={getMenu}
        className={css.commonTableWrapper}
      />
    </CollapseVertical>
  );
};

const columnCurrentRx = [
  {
    key: 'drugName',
    name: 'Medication',
    fieldName: 'drugName',
    sortable: true,
    minWidth: 250,
    maxWidth: 350,
    renderItem: ({ drugName, fullStrength }) => (
      <span className="cell_style">
        <span className="weight-600">{drugName}</span> <span>{fullStrength}</span>
      </span>
    ),
  },
  {
    key: 'directions',
    name: 'SIG',
    fieldName: 'directions',
    minWidth: Math.max(window.innerWidth - 1310, 50),
    maxWidth: window.innerWidth,
  },
  {
    key: 'quantity',
    name: 'Qty',
    fieldName: 'quantity',
    sortable: true,
    minWidth: 40,
    maxWidth: 40,
  },
  {
    key: 'expectedDuration',
    name: 'Days',
    fieldName: 'expectedDuration',
    sortable: true,
    minWidth: 40,
    maxWidth: 40,
  },
  {
    key: 'refills',
    name: 'Refills',
    fieldName: 'refills',
    sortable: true,
    minWidth: 40,
    maxWidth: 40,
  },
  {
    key: 'provider',
    name: 'Provider',
    fieldName: 'provider',
    minWidth: 100,
    maxWidth: 100,
  },
  {
    key: 'date',
    name: 'Start Date',
    fieldName: 'startDate',
    renderItem: ({ startDate }, _, c, render) => render({ date: startDate }),
    sortable: true,
    minWidth: 90,
    maxWidth: 90,
  },
  {
    key: 'status',
    name: 'Type',
    fieldName: 'status',
    minWidth: 110,
    maxWidth: 110,
    renderItem: ({ status }) => <RenderType type={status} />,
  },
  {
    key: 'details',
    name: 'Details',
    fieldName: 'details',
    minWidth: 180,
    maxWidth: 180,
    renderItem: ({ details }) => (
      <div className="cell_style" dangerouslySetInnerHTML={{ __html: details }} />
    ),
  },
  {
    key: 'Action',
    name: 'Action',
    fieldName: 'Action',
  },
];

const styles = {
  commonTableWrapper: {
    '.column_cell > div': {
      width: '100%',
    },
  },
};

export default RxHistory;
