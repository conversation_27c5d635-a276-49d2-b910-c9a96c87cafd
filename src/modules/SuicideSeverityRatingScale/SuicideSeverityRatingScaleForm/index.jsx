import React, { Fragment, useContext } from 'react';
import { Default<PERSON>utton, PrimaryButton, Stack } from '@fluentui/react';
import { fetchByQueryKey } from 'apis/queryClient';
import cn from 'classnames';
import CalloutConfirmation from 'components/Callout/CalloutConfirmation';
import <PERSON><PERSON>ield from 'components/Form/Field';
import FormUndo from 'components/FormUndo';
import LoadingWrapper from 'components/Loading/LoadingWrapper';
import QUERY_KEY from 'constants/queryKey';
import { API_METHOD, API_SUICIDE_SEVERITY_RATING_SCALE } from 'constants/urlRequest';
import { useClass, useNavigator, useRequest, useText, useUser } from 'hooks';
import { toast } from 'react-toastify';
import { handleValidationBE } from 'utils/form';
import FieldSelectorPrimary from 'components/Form/FieldSelectorPrimary';
import { FORM_ID } from 'constants/formId';
import ButtonWrapper from 'components/FormUndo/ButtonWrapper';
import { TEXT_TEMPLATE } from 'constants/texts';
import HorizontalRule from 'components/HorizontalRule';
import CollapseVertical from 'components/Collapse/CollapseVertical';
import { SSRSContext } from 'contexts/SSRSContext';
import { MODULE_NAME } from 'constants/routes';
import { formatSubmitWithSection, getAnswer } from 'modules/PRAPARE/PRAPAREForm/constant';
import FieldDate from 'components/Form/FieldDate';
const TODAY = new Date();

const SuicideSeverityRatingScaleForm = () => {
  const css = useClass(styles);
  const t = useText();
  const { navigate } = useNavigator();
  const { editItem, setIdItem } = useContext(SSRSContext);
  const { currentPatient } = useUser();

  const apiQuestions = useRequest({
    url: API_SUICIDE_SEVERITY_RATING_SCALE.GET_FORM,
    shouldCache: true,
  });

  const create = useRequest({
    url: API_SUICIDE_SEVERITY_RATING_SCALE.DEFAULT(),
    method: API_METHOD.POST,
    onSuccess: () => {
      fetchByQueryKey(QUERY_KEY.SUICIDE_SEVERITY_RATING_SCALE_HISTORY);
      toast.success(
        TEXT_TEMPLATE.ADD_SUCCESSFULLY(t('The Detailed Columbia - Suicide Severity Rating Scale')),
      );
    },
  });

  const apiGetData = useRequest({
    key: `suicide-detail-${editItem?.batchId}`,
    url: API_SUICIDE_SEVERITY_RATING_SCALE.DEFAULT(editItem?.batchId),
    enabled: !!editItem,
  });

  const onSubmit = (values, form) => {
    const payload = { ...values };
    let answers = [];
    for (const element of payload.fr) {
      answers = [...answers, ...formatSubmitWithSection(element)];
    }
    payload.answers = answers;
    delete payload.fr;

    create.mutateAsync(payload, {
      onSuccess: () => {
        setIdItem();
        navigate({ hash: MODULE_NAME.SUICIDE_SEVERITY_RATING_SCALE_HISTORY });
      },
      onError: (err) => handleValidationBE(form, err),
    });
  };

  const isValidDate = (str) => {
    // Regular expression to check if the string is in YYYY-MM-DD format
    const isoRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.\d{3}Z$/;
    if (!isoRegex.test(str)) {
      return false;
    }
    const date = new Date(str);
    return !isNaN(date.getTime());
  };

  const formatValue = () => {
    const temp = apiQuestions?.data?.length ? structuredClone(apiQuestions.data) : [];
    for (const fr of temp) {
      for (const section of fr.sections) {
        for (const question of section.questions) {
          for (const itemData of apiGetData?.data || []) {
            if (itemData.questionId === question.questionId) {
              question.answerId = question.answerId || itemData.answerId;
              question.answerText = itemData.answerText;
              question.value = question.value || itemData.value;
              if (isValidDate(itemData.value)) {
                question.answers[question.answers.length - 1].value = itemData.value;
              }
              for (const answer of question.answers) {
                if (itemData.answerId === answer.answerId) {
                  answer.selected = true;
                  answer.value = answer.value || itemData.value;
                }
              }
            }
          }
        }
      }
    }
    return temp;
  };

  const commonStyle = cn(
    css.cmClass.backgroundGrey100,
    css.cmClass.border,
    css.cmClass.borderColor,
    css.cmClass.borderRadius8,
    'p-16',
  );

  const numberToAlphabet = (num) => {
    let str = '';
    while (num > 0) {
      num--; // Adjust for 0-based index
      str = String.fromCharCode((num % 26) + 97) + str;
      num = Math.floor(num / 26);
    }
    return str;
  };

  return (
    <CollapseVertical
      title={t('Detailed Columbia - Suicide Severity Rating Scale')}
      open
      className="mb-24"
    >
      <FormUndo
        initialValues={
          editItem
            ? {
                batchId: editItem.batchId,
                formDate: new Date(editItem.formDate),
                patientId: currentPatient.patientId,
                status: editItem.status,
                fr: formatValue(),
              }
            : {
                formDate: TODAY,
                patientId: currentPatient.patientId,
                fr: structuredClone(apiQuestions.data),
              }
        }
        onSubmit={onSubmit}
        enableReinitialize
        formId={FORM_ID.SUICIDE_SEVERITY_RATING_SCALE}
      >
        <LoadingWrapper loading={create.loading || apiQuestions.loading || apiGetData.loading}>
          <Stack tokens={{ padding: 16, childrenGap: 16 }}>
            <FieldDate name="formDate" title={t('Entry Date')} fieldClassName={css.inputWidth} />
            {apiQuestions?.data?.map((i, frIndex) => (
              <Stack key={i.sectionId} className={commonStyle} tokens={{ childrenGap: 8 }}>
                {i.sectionName && (
                  <Fragment>
                    <span className="text-16-24 weight-600">{i.sectionName}</span>
                    <div dangerouslySetInnerHTML={{ __html: i.description }} />
                    <HorizontalRule mt={16} />
                  </Fragment>
                )}
                {i.sections?.map((sec, sectionIndex) => (
                  <Stack key={sec.sectionId}>
                    <div
                      className="weight-600"
                      dangerouslySetInnerHTML={{ __html: `${sec.order} - ${sec.sectionName}` }}
                    />
                    <div className={cn('mt-8', css.cmClass.grid3Columns)}>
                      {sec.questions?.map((ques, quesIndex) => (
                        <div key={ques.questionId}>
                          <Stack horizontal verticalAlign="center" tokens={{ childrenGap: 6 }}>
                            <span className={css.quesTitle}>{numberToAlphabet(quesIndex + 1)}</span>
                            <span className="text-16-24 weight-600">{ques.questionText}</span>
                          </Stack>
                          <Stack tokens={{ childrenGap: 2 }} className="mt-8 px-16">
                            {ques.answers?.map((ans, answerIndex) => (
                              <div key={ans.answerId}>
                                {getAnswer(
                                  ans,
                                  `fr.${frIndex}.sections.${sectionIndex}.questions.${quesIndex}`,
                                  answerIndex,
                                  true,
                                )}
                              </div>
                            ))}
                          </Stack>
                        </div>
                      ))}
                    </div>
                    {sec.sectionId !== i.sections[i.sections.length - 1].sectionId && (
                      <HorizontalRule mt={12} mb={8} />
                    )}
                  </Stack>
                ))}
              </Stack>
            ))}
            <FieldSelectorPrimary
              name="status"
              title={t('Status')}
              required
              showClear={false}
              fieldClassName={css.inputWidth}
              options={[
                { key: 0, text: 'Save as Incomplete' },
                { key: 1, text: 'Save Complete' },
              ]}
            />
            <ButtonWrapper alignEnd={false}>
              <MyField isFast={false}>
                {({ form }) => (
                  <Fragment>
                    <PrimaryButton
                      text={t('Submit')}
                      data-isbutton="button"
                      onClick={form.submitForm}
                    />
                    <CalloutConfirmation
                      onOk={() => form.setValues({ patientId: currentPatient.patientId })}
                    >
                      <DefaultButton text={t('Reset')} />
                    </CalloutConfirmation>
                  </Fragment>
                )}
              </MyField>
            </ButtonWrapper>
          </Stack>
        </LoadingWrapper>
      </FormUndo>
    </CollapseVertical>
  );
};

const styles = (theme) => ({
  inputWidth: {
    width: 300,
  },
  quesTitle: {
    width: 24,
    backgroundColor: theme.custom.grey600,
    borderRadius: '50%',
    color: '#fff',
    textAlign: 'center',
    fontWeight: 'bold',
    lineHeight: 24,
    display: 'inline-block',
  },
});

export default SuicideSeverityRatingScaleForm;
