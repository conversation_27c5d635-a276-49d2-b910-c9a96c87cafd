import React, { useState } from 'react';
import useRequest from 'hooks/useRequest';
import { API_METHOD, API_SUPPORT } from 'constants/urlRequest';
import CollapseVertical from 'components/Collapse/CollapseVertical';
import Table from 'components/Table';
import { t } from 'utils/string';
import useClass from 'hooks/useClass';
import cn from 'classnames';
import { FormProvider } from 'react-hook-form';
import useHookForm from 'components/HookForm/useHookForm';
import FieldText from 'components/HookForm/FieldText';
import FieldCheckBox from 'components/HookForm/FieldCheckbox';
import { DefaultButton, PrimaryButton, Stack } from '@fluentui/react';
import { toast } from 'react-toastify';
import { TEXT_TEMPLATE } from 'constants/texts';

const OrganizationData = () => {
  const [index, setIndex] = useState();

  const { data, loading } = useRequest({
    url: API_SUPPORT.PROGRESS_NOTES_ORG_FIELDS,
  });

  const { request: apiUpdate, loading: loadingUpdate } = useRequest({
    url: API_SUPPORT.PROGRESS_NOTES_ORG_FIELDS,
    method: API_METHOD.PUT,
  });

  const onSubmit = (data) => {
    apiUpdate({
      url: `${API_SUPPORT.PROGRESS_NOTES_ORG_FIELDS}/${data?.encounterOrganizationFieldId}`,
      payload: data,
      options: {
        onSuccess: () => {
          toast.success(TEXT_TEMPLATE.UPDATE_SUCCESSFULLY(t('Organization Field')));
          setIndex();
        },
      },
    });
  };

  return (
    <CollapseVertical className="mb-24" open title={t('Progress Note Organization Data')}>
      <Table
        columns={columns}
        items={data}
        loading={loading || loadingUpdate}
        pagination={false}
        onEdit={(i, index) => setIndex(index)}
      />
      {index >= 0 && <Form item={data[index]} setIndex={setIndex} onSubmit={onSubmit} />}
    </CollapseVertical>
  );
};

export default OrganizationData;

const columns = [
  { fieldName: 'encounterFieldKey', name: t('Field Key') },
  { fieldName: 'encounterFieldDisplayName', name: t('Display Name') },
  {
    fieldName: 'required',
    name: t('Required'),
    renderItem: ({ required }) => (required ? t('Yes') : t('No')),
  },
  {
    fieldName: 'UpdateDate',
    name: t('Update By'),
    renderItem: (i, _, c, render) => render({ date: i.updateDate, user: i.updateUser }),
  },
  {
    fieldName: 'CreateDate',
    name: t('Created By'),
    renderItem: (i, _, c, render) => render({ date: i.createDate, user: i.createUser }),
  },
  { key: 'Action', fieldName: 'Action', name: t('Action') },
];

const Form = ({ item, onSubmit, setIndex }) => {
  const css = useClass();

  const methods = useHookForm({
    defaultValues: item,
    dependencies: { encounterFieldKey: item?.encounterFieldKey },
  });

  return (
    <div
      className={cn(
        'mt-16 p-16',
        css.cmClass.backgroundGrey100,
        css.cmClass.border,
        css.cmClass.borderRadius4,
      )}
    >
      <FormProvider {...methods}>
        <form onSubmit={methods.handleSubmit(onSubmit)}>
          <div className={css.cmClass.grid4Columns}>
            <FieldText name="encounterFieldDisplayName" title={t('Display Name')} />
            <FieldCheckBox name="required" title={t('Required')} />
          </div>
          <Stack horizontal tokens={{ childrenGap: 12 }} className="mt-16">
            <PrimaryButton type="submit" text={t('Save')} />
            <DefaultButton type="button" text={t('Cancel')} onClick={() => setIndex()} />
          </Stack>
        </form>
      </FormProvider>
    </div>
  );
};
