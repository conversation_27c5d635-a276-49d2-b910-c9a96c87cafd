import React, { useState, useCallback } from 'react';
import {
  <PERSON><PERSON>utton,
  SearchBox,
  Stack,
  TextField,
  defaultDatePickerStrings,
} from '@fluentui/react';
import Table from 'components/Table';
import { SETTING_KEYS } from 'constants/settingKeys';
import { API_SUPPORT } from 'constants/urlRequest';
import { useClass, useRequest, useSetting } from 'hooks';
import moment from 'moment';
import DatePicker from 'components/DatePicker';
import { DEFAULT_PAGINATION_PARAMS } from 'constants/index';
import { t } from 'utils/string';
import { debounceFunc } from 'utils/time';
import CollapseVertical from 'components/Collapse/CollapseVertical';
import cn from 'classnames';
import HorizontalRule from 'components/HorizontalRule';
import OrganizationData from './OrganizationData';

function ProgressNotes() {
  const css = useClass(styles);
  const [DATE_FORMAT] = useSetting(SETTING_KEYS.DATE_FORMAT);

  const [search, setSearch] = useState({
    StartDate: moment().subtract(7, 'days').toDate(),
    EndDate: moment().toISOString(),
    PatientId: '',
  });

  const { data, loading, updateParams, params } = useRequest({
    url: API_SUPPORT.PROGRESS_NOTES,
    params: {
      ...DEFAULT_PAGINATION_PARAMS,
      StartDate: moment().subtract(7, 'days').toDate(),
      EndDate: moment().toISOString(),
    },
    enabled: false,
    autoRefetch: true,
  });

  const onFilterChange = (newFilter, name = 'StartDate') => {
    setSearch({ ...search, [name]: moment(newFilter).toISOString() });
  };

  const onSearch = useCallback(
    debounceFunc((SearchTerm) => {
      updateParams({ SearchTerm, Page: 0 });
    }, 500),
    [],
  );

  return (
    <React.Fragment>
      <CollapseVertical className="mb-24" open title={t('Progress Notes')}>
        <Stack tokens={{ childrenGap: 16 }}>
          <form
            onSubmit={(e) => {
              e.preventDefault();
              updateParams(search);
            }}
          >
            <div className={cn(css.cmClass.grid4Columns, css.alignEnd)}>
              <DatePicker
                formatDate={(date) => moment(date).format(DATE_FORMAT)}
                placeholder={t('Start Time filter')}
                strings={defaultDatePickerStrings}
                value={search?.StartDate && new Date(search.StartDate)}
                maxDate={new Date(search?.EndDate)}
                onSelectDate={onFilterChange}
                label={t('Start Date')}
                isRenderQuickButtons={false}
              />
              <DatePicker
                label={t('End Date')}
                formatDate={(date) => moment(date).format(DATE_FORMAT)}
                placeholder="End Time filter"
                strings={defaultDatePickerStrings}
                value={search?.EndDate && new Date(search.EndDate)}
                minDate={new Date(search?.StartDate)}
                maxDate={new Date()}
                onSelectDate={(date) => onFilterChange(date, 'EndDate')}
                isRenderQuickButtons={false}
              />
              <TextField
                label={t('Internal Client Id')}
                onChange={(_, value) => setSearch({ ...search, PatientId: value })}
              />
              <Stack.Item>
                <PrimaryButton text={t('Search')} type="submit" />
              </Stack.Item>
            </div>
          </form>
          <HorizontalRule mt={16} type="dotted" />
          <div className={css.cmClass.grid4Columns}>
            <SearchBox
              placeholder={t('Search')}
              iconProps={{ iconName: 'Zoom' }}
              onChange={(_, newValue) => onSearch(newValue)}
            />
          </div>
          <Table
            columns={columns}
            items={data?.items || []}
            loading={loading}
            totalItems={data?.totalItems}
            metadata={params}
            onMetadataChange={updateParams}
          />
        </Stack>
      </CollapseVertical>
      <OrganizationData />
    </React.Fragment>
  );
}

const styles = () => ({
  alignEnd: {
    alignItems: 'flex-end',
  },
});

const columns = [
  {
    name: 'Client',
    fieldName: 'patientName',
    sortable: true,
    renderItem: ({ patientName, bhmisId, patientId }) => (
      <Stack tokens={{ childrenGap: 4 }}>
        <span>{patientName}</span>
        <span>{`cli(${patientId})`}</span>
        <span>{bhmisId}</span>
      </Stack>
    ),
  },
  {
    name: 'Create Info',
    fieldName: 'createDate',
    sortable: true,
    minWidth: 300,
    maxWidth: 300,
    renderItem: ({ createDate, createUser }, _, c, render) => (
      <Stack tokens={{ childrenGap: 4 }}>
        <span>
          {t('Create User')}: {createUser}
        </span>
        <span>
          {t('Create DateTime')}: {render({ date: createDate, withTime: true })}
        </span>
      </Stack>
    ),
  },
  {
    name: 'Svc Start',
    fieldName: 'startTime',
    sortable: true,
    renderItem: ({ startTime }, _, c, render) => render({ date: startTime, withTime: true }),
  },
  {
    name: 'Svc End',
    fieldName: 'endTime',
    sortable: true,
    renderItem: ({ endTime }, _, c, render) => render({ date: endTime, withTime: true }),
  },
  {
    name: 'Enc Type',
    fieldName: 'formName',
    sortable: true,
    minWidth: 150,
    maxWidth: 150,
  },
  {
    name: 'Other Info',
    fieldName: 'serviceCode',
    sortable: true,
    renderItem: ({ amount, serviceCode, units }) => (
      <Stack tokens={{ childrenGap: 4 }}>
        <span>
          {t('Service')}: {serviceCode}
        </span>
        <span>
          {t('Unit(s)')}: {units}
        </span>
        <span>
          {t('Amount')}: {amount}
        </span>
      </Stack>
    ),
  },
];

export default ProgressNotes;
