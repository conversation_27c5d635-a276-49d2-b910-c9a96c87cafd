import React, { Fragment, useContext } from 'react';
import { DefaultButton, PrimaryButton, Stack } from '@fluentui/react';
import { fetchByQueryKey } from 'apis/queryClient';
import cn from 'classnames';
import CalloutConfirmation from 'components/Callout/CalloutConfirmation';
import MyField from 'components/Form/Field';
import FieldDate from 'components/Form/FieldDate';
import FormUndo from 'components/FormUndo';
import QUERY_KEY from 'constants/queryKey';
import { API_METHOD, API_PRAPARE } from 'constants/urlRequest';
import { useClass, useNavigator, useRequest, useText, useUser } from 'hooks';
import { toast } from 'react-toastify';
import { FORM_ID } from 'constants/formId';
import ButtonWrapper from 'components/FormUndo/ButtonWrapper';
import { TEXT_TEMPLATE } from 'constants/texts';
import HorizontalRule from 'components/HorizontalRule';
import CollapseVertical from 'components/Collapse/CollapseVertical';
import { FormEditContext } from 'contexts/FormEditContext';
import ActionBox from 'modules/Analytic/components/ActionBox';
import { handleValidationBE } from 'utils/form';
import LoadingWrapper from 'components/Loading/LoadingWrapper';
import { caculatorScore, formatSubmitWithSection, getAnswer } from './constant';
import { MODULE_NAME } from 'constants/routes';
const today = new Date();

const PRAPAREForm = () => {
  const css = useClass(styles);
  const t = useText();
  const { navigate } = useNavigator();
  const { editItem, setEditItem } = useContext(FormEditContext).getEditFunctions(
    MODULE_NAME.PRAPARE_INPUT,
  );
  const { currentPatient } = useUser();

  const apiQuestions = useRequest({ url: API_PRAPARE.GET_QUESTION, shouldCache: true });

  const create = useRequest({
    url: API_PRAPARE.DEFAULT(),
    method: API_METHOD.POST,
    onSuccess: () => {
      fetchByQueryKey(QUERY_KEY.PRAPARE_HISTORY);
      toast.success(TEXT_TEMPLATE.ADD_SUCCESSFULLY(t('The PRAPARE')));
    },
  });

  const apiGetData = useRequest({
    key: `PRAPARE-${editItem?.batchId}`,
    url: API_PRAPARE.DEFAULT(editItem?.batchId),
    method: API_METHOD.GET,
    enabled: !!editItem,
  });

  const onSubmit = (values, form) => {
    const payload = { ...values };
    payload.answers = formatSubmitWithSection(payload);
    delete payload.sections;
    create.request({
      payload,
      options: {
        onSuccess: () => {
          setEditItem();
          navigate({ hash: MODULE_NAME.PRAPARE_HISTORY });
        },
        onError: (err) => handleValidationBE(form, err),
      },
    });
  };

  const formatValue = () => {
    const temp = apiQuestions?.data?.length ? structuredClone(apiQuestions.data) : [];
    for (const section of temp) {
      for (const question of section.questions) {
        for (const itemData of apiGetData?.data || []) {
          if (itemData.questionId === question.questionId) {
            question.answerId = itemData.answerId;
            question.answerText = itemData.answerText;
            for (const answer of question.answers) {
              if (itemData.answerId === answer.answerId) {
                answer.selected = true;
                answer.value = [6, 13].includes(itemData.precedence)
                  ? +itemData.value
                  : itemData.value;
                if (['checkbox', 'text'].includes(answer.answerSpecialType)) question.answerId = '';
              }
            }
          }
        }
      }
    }
    return temp;
  };

  return (
    <CollapseVertical title={t('Questionnaire Panel')} open className="mb-24">
      <FormUndo
        initialValues={
          editItem
            ? {
                batchId: editItem.batchId,
                formDate: new Date(editItem.formDate),
                patientId: currentPatient.patientId,
                sections: formatValue(),
              }
            : {
                formDate: today,
                patientId: currentPatient.patientId,
                sections: apiQuestions.data || [],
              }
        }
        onSubmit={onSubmit}
        enableReinitialize
        formId={FORM_ID.PRAPARE}
      >
        <LoadingWrapper loading={create.loading || apiQuestions.loading || apiGetData.loading}>
          <Stack tokens={{ padding: 16, childrenGap: 16 }}>
            <FieldDate
              title={t('Entry Date')}
              name="formDate"
              required
              fieldClassName={css.inputWidth}
              maxDate={today}
              isRenderRightSide={false}
            />
            {apiQuestions?.data?.length && (
              <Stack tokens={{ childrenGap: 20 }}>
                {apiQuestions?.data?.map((section, sectionIndex) => (
                  <ActionBox
                    hasMoreButton={false}
                    key={section.sectionId}
                    title={section.sectionName}
                  >
                    <HorizontalRule mt={8} mb={8} />
                    <div className={css.cmClass.grid3Columns}>
                      {section.questions.map((ques, questionIndex) => (
                        <Stack key={ques.questionId} tokens={{ childrenGap: 8 }}>
                          <span className="weight-600">
                            {ques.precedence}. {ques.questionText}
                          </span>
                          <Stack
                            className={cn(
                              css.cmClass.border,
                              css.cmClass.backgroundBody,
                              'p-8 flex-1',
                              css.cmClass.borderRadius4,
                            )}
                            tokens={{ childrenGap: 6 }}
                          >
                            {ques.answers.map((ans, answerIndex) => (
                              <Fragment key={ans.answerId}>
                                {getAnswer(
                                  ans,
                                  `sections.${sectionIndex}.questions.${questionIndex}`,
                                  answerIndex,
                                )}
                              </Fragment>
                            ))}
                          </Stack>
                        </Stack>
                      ))}
                    </div>
                  </ActionBox>
                ))}
              </Stack>
            )}
            <HorizontalRule mt={16} />
            <MyField name="sections" isFast={false}>
              {({ field }) => (
                <span className="text-16-24 weight-600">
                  {t('Total Score:')} {caculatorScore(field.value || [])}
                </span>
              )}
            </MyField>
            <ButtonWrapper alignEnd={false}>
              <MyField>
                {({ form }) => (
                  <Stack horizontal tokens={{ childrenGap: 16 }}>
                    <PrimaryButton
                      text={t('Submit')}
                      data-isbutton="button"
                      onClick={form.submitForm}
                    />
                    <CalloutConfirmation
                      onOk={() => {
                        editItem && setEditItem();
                        form.setValues({
                          formDate: today,
                          patientId: currentPatient.patientId,
                          sections: structuredClone(apiQuestions.data),
                        });
                      }}
                    >
                      <DefaultButton text={t('Reset')} />
                    </CalloutConfirmation>
                  </Stack>
                )}
              </MyField>
            </ButtonWrapper>
          </Stack>
        </LoadingWrapper>
      </FormUndo>
    </CollapseVertical>
  );
};

const styles = () => ({
  inputWidth: {
    width: 300,
  },
});

export default PRAPAREForm;
