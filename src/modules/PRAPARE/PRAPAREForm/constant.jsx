import MyField from 'components/Form/Field';
import FieldCheckBox from 'components/Form/FieldCheckbox';
import FieldChoice from 'components/Form/FieldChoice';
import FieldDate from 'components/Form/FieldDate';
import FieldText from 'components/Form/FieldText';
import FieldSignButton from 'components/Form/FieldSignButton';
import { Fragment } from 'react';

const VALID = [
  {
    text: 'Are you Hispanic or Latino?',
    answers: ['Yes'],
  },
  {
    text: 'Which race(s) are you? Check all that apply',
    answers: [
      'Asian',
      'Native Hawaiian',
      'Pacific Islander',
      'Black/African American',
      'American Indian/Alaskan Native',
      'Other (please write)',
      'I choose not to answer this question',
    ],
  },
  {
    text: "At any point in the past 2 years, has season or migrant farm work been your or your family's main source of income?",
    answers: ['Yes'],
  },
  {
    text: 'Have you been discharged from the armed forces of the United States?',
    answers: ['Yes'],
  },
  {
    text: 'What language are you most comfortable speaking?',
    answers: ['Language other than English (please write)'],
  },
  {
    text: 'What is your housing situation today?',
    answers: [
      'I do not have housing (staying with others, in a hotel, in a shelter, living outside on the street, on a beach, in a car, or in a park)',
    ],
  },
  {
    text: 'Are you worried about losing your housing?',
    answers: ['Yes'],
  },
  {
    text: 'What is the highest level of school that you have finished?',
    answers: ['Less than high school degree'],
  },
  {
    text: 'What is your current work situation?',
    answers: ['Part', 'Unemployed'],
  },

  {
    text: 'What is your main insurance?',
    answers: ['None/uninsured'],
  },
  {
    text: 'In the past year, have you or your family members you live with been unable to get any of the following when it was really needed? Check all that apply.',
    answers: [
      'Food',
      'Clothing',
      'Utilities',
      'Child Care',
      'Medicine or Any Health Care (Medical, Dental, Mental Health, Vision)',
      'Phone',
      'Other (please write)',
    ],
  },
  {
    text: 'Has lack of transportation kept you from medical appointments, meetings, work, or from getting things needed for daily living? Check all that apply.',
    answers: [
      'Yes, it has kept me from medical appointment or from getting my medications',
      'Yes, it has kept me from non',
    ],
  },
  {
    text: 'How often do you see or talk to people that you care about and feel close to? (For example: talking to friends on the phone, visiting friends or family, going to church or club meetings)',
    answers: ['Less than once a week', '1 or 2 times a week'],
  },
  {
    text: "Stress is when someone feels tense, nervous, or can't sleep at night because their mind is troubled. How stressed are you?",
    answers: ['Quite a bit', 'Very Much'],
  },
  {
    text: 'In the past year, have you spent more than 2 nights in a row in a jail, detention center, or juvenile correctional facility?',
    answers: ['Yes'],
  },
  {
    text: 'Are you a refugee?',
    answers: ['Yes'],
  },
  {
    text: 'Do you feel physically and emotionally safe where you currently live?',
    answers: ['No'],
  },
  {
    text: 'In the past year, have you been afraid of your partner or ex-partner?',
    answers: ['Yes'],
  },
];

const QUES6 =
  'How many family members, including yourself, do you currently live with? (Numerical value only)';

const QUES13 =
  'During the past year, what was the total combined income for you and the family members you live with? This information will help us determine if you are eligible for any benefits. (Numerical value only)';

export const caculatorScore = (sections = []) => {
  let score = 0,
    ques6,
    ques13;
  for (const section of sections) {
    valid: for (const question of section.questions) {
      for (const itemData of VALID) {
        if (question.answerId) {
          if (
            question.questionText?.trim() === itemData.text &&
            itemData.answers.includes(question.answerText)
          ) {
            score++;
            continue valid;
          }
        } else {
          for (const answer of question.answers) {
            if (
              answer.selected &&
              question.questionText?.trim() === itemData.text &&
              itemData.answers.includes(answer.answerText)
            ) {
              score++;
              continue valid;
            }
            if (QUES6 === question.questionText?.trim()) {
              ques6 = answer.value;
              continue valid;
            }
            if (QUES13 === question.questionText?.trim()) {
              ques13 = answer.value;
              continue valid;
            }
          }
        }
      }
    }
  }
  if (ques6 && ques13 && ques13 / 2 <= 9440 + 5140 * ques6) {
    score++;
  }
  return score;
};

export const getAnswer = (answer, fieldName, answerIndex, isSuicide, isTextNumber = true) => {
  switch (answer.answerSpecialType) {
    case 'radiobutton':
      return (
        <MyField name={fieldName}>
          {({ field }) => (
            <FieldChoice
              name={`${fieldName}.answerId`}
              horizontal
              isFast={false}
              formatCheckedOption={(item) => item.key}
              options={[{ key: answer.answerId, text: answer.answerText }]}
              onChange={(option, name, form) => {
                form.setFieldValue(name, option.key);
                form.setFieldValue(`${fieldName}.answerText`, option.text);
                if (
                  ['I choose not to answer this question', 'No/Does not apply'].includes(
                    option.text,
                  )
                ) {
                  const answers = field.value?.answers ? [...field.value.answers] : [];
                  for (const answer of answers) {
                    answer.selected = false;
                    answer.value = '';
                  }
                  form.setFieldValue(`${fieldName}.answers`, answers);
                }
              }}
            />
          )}
        </MyField>
      );
    case 'selectedshowtextbox':
      return (
        <Fragment>
          <FieldCheckBox
            name={`${fieldName}.answers.${answerIndex}.selected`}
            label={answer.answerText}
            fieldClassName="px-8"
            isFast={false}
            onChange={({ setFieldValue, checked }) => {
              setFieldValue(`${fieldName}.answers.${answerIndex}.selected`, checked);
              setFieldValue(`${fieldName}.answerId`, null);
            }}
          />
          <MyField name={`${fieldName}.answers.${answerIndex}.selected`} isFast={false}>
            {({ field }) =>
              field.value && (
                <FieldText
                  name={`${fieldName}.answers.${answerIndex}.value`}
                  fieldClassName="px-8"
                  placeholder={answer.answerText}
                />
              )
            }
          </MyField>
        </Fragment>
      );
    case 'selectedradioshowtextbox':
      return (
        <Fragment>
          <FieldChoice
            name={`${fieldName}.answerId`}
            horizontal
            formatCheckedOption={(item) => item.key}
            options={[{ key: answer.answerId, text: answer.answerText }]}
            onChange={(option, name, form) => {
              form.setFieldValue(name, option.key);
              form.setFieldValue(`${fieldName}.answerText`, option.text);
            }}
          />
          <MyField name={`${fieldName}.answerId`}>
            {({ field }) =>
              field.value === answer.answerId && (
                <FieldText
                  name={`${fieldName}.value`}
                  fieldClassName="px-8"
                  placeholder={answer.answerText}
                />
              )
            }
          </MyField>
        </Fragment>
      );
    case 'checkbox':
      return (
        <FieldCheckBox
          name={`${fieldName}.answers.${answerIndex}.selected`}
          label={answer.answerText}
          fieldClassName="px-8"
          isFast={false}
          onChange={({ setFieldValue, checked }) => {
            setFieldValue(`${fieldName}.answers.${answerIndex}.selected`, checked);
            setFieldValue(`${fieldName}.answerId`, null);
          }}
        />
      );
    case 'text':
      return (
        <FieldText
          type={isTextNumber ? 'number' : 'text'}
          name={`${fieldName}.answers.${answerIndex}.value`}
          fieldClassName="px-8"
          isFast={false}
          placeholder={`${answer.answerText}${answer.answerValue ? `-${answer.answerValue}` : ''}`}
          onChange={({ data, field: { name }, form: { setFieldValue } }) => {
            setFieldValue(name, data);
            !isSuicide && setFieldValue(`${fieldName}.answerId`, null);
          }}
        />
      );
    case 'textarea':
      return (
        <FieldText
          name={`${fieldName}.answers.${answerIndex}.value`}
          fieldClassName="px-8"
          isFast={false}
          placeholder={answer.answerText}
        />
      );
    case 'Date':
      return (
        <FieldDate
          name={`${fieldName}.answers.${answerIndex}.value`}
          fieldClassName="px-8"
          isFast={false}
          onChange={({ setFieldValue }, date) => {
            setFieldValue(`${fieldName}.value`, date);
          }}
          isRenderRightSide={false}
        />
      );
    case 'signature':
      return (
        <FieldSignButton fieldClassName="px-8" name={`${fieldName}.answers.${answerIndex}.value`} />
      );
    default:
      break;
  }
};

export const formatSubmitWithSection = (payload) => {
  let answers = [];
  for (const section of payload.sections) {
    answers.push(...formatSubmit(section));
  }
  return answers;
};

export const formatSubmit = (section) => {
  let answers = [];
  for (const question of section.questions) {
    if (question.answerId) {
      answers.push({
        questionId: question.questionId,
        answerId: question.answerId,
        precedence: question.precedence,
        value: question.value || '1',
      });
    }
    for (const answer of question.answers || []) {
      if (['checkbox', 'selectedshowtextbox'].includes(answer.answerSpecialType)) {
        if (!answer.selected) continue;
        answers.push({
          questionId: question.questionId,
          ...answer,
          precedence: question.precedence,
          value: answer.value || '1',
        });
      } else if (['radiobutton', 'Date'].includes(answer.answerSpecialType)) {
        continue;
      } else if (answer.answerSpecialType === 'selectedradioshowtextbox') {
        if (!question.answerId || question.answerId !== answer.answerId) continue;
        answers = answers.filter(
          (i) => i.questionId !== question.questionId && i.answerId !== answer.answerId,
        );
        answers.push({
          questionId: question.questionId,
          ...answer,
          precedence: question.precedence,
          value: question.value || '1',
        });
      } else {
        if (!answer.value) continue;
        answers.push({
          questionId: question.questionId,
          ...answer,
          precedence: question.precedence,
        });
      }
    }
  }
  return answers;
};
