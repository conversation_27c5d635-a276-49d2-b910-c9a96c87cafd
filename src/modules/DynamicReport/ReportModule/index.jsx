import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { DefaultButton, PrimaryButton, SearchBox, Stack } from '@fluentui/react';
import CollapseVertical from 'components/Collapse/CollapseVertical';
import SelectorPrimary from 'components/Selector';
import ELEMENT_ID from 'constants/elementId';
import { isInclude, t } from 'utils/string';
import { useClass, useModal, useRequest } from 'hooks';
import cn from 'classnames';
import CalloutConfirmation from 'components/Callout/CalloutConfirmation';
import HorizontalRule from 'components/HorizontalRule';
import Parameters from './components/Parameters';
import { Formik } from 'formik';
import QUERY_KEY from 'constants/queryKey';
import { API_DYNAMIC_REPORT, API_METHOD, API_REPORT_MODULE } from 'constants/urlRequest';
import MyField from 'components/Form/Field';
import { toast } from 'react-toastify';
import { FORM_LOCAL_DATA } from 'constants';
import { isNil } from 'utils';
import LoadingWrapper from 'components/Loading/LoadingWrapper';
import { fetchByQueryKey } from 'apis/queryClient';
import ScheduleModal from './components/ScheduleModal';
import { MODAL_SIZE } from 'constants/modal';
import { debounceFunc } from 'utils/time';
import ListSelectCategory from './components/ListSelectCategory';
import ListSelectReport from './components/ListSelectReport';
import { setInitialValues } from './initialValues';
import ReportBeingGenerate from 'components/ReportBeingGenerate';
import formatParamValues from './formatParamValues';
import ResponsiveGrid from 'components/Layout/Responsive/Container';
import ResponsiveItem from 'components/Layout/Responsive/Item';
import TableBuildBatch from './components/TableBuildBatch';
import EHIExportHistory from './components/EHIExportHistory';
import { scrollIntoViewId } from 'utils/globalScroll';
import { MODULE_NAME } from 'constants/routes';
import useNavigator from 'hooks/useNavigator';

const REPORT_TYPE_KEY = {
  ALL: 'all',
  STANDARD: 'Standard Reports',
  DYNAMIC: 'Dynamic Reports',
  FAVORITE: 'Favorite Reports',
};
const REPORT_TYPE = [
  {
    key: REPORT_TYPE_KEY.ALL,
    text: t('All Reports'),
  },
  {
    key: REPORT_TYPE_KEY.STANDARD,
    text: t('Standard Reports'),
  },
  {
    key: REPORT_TYPE_KEY.DYNAMIC,
    text: t('Dynamic Reports'),
  },
  {
    key: REPORT_TYPE_KEY.FAVORITE,
    text: t('Favorite Reports'),
  },
];

const ReportModule = () => {
  const [reportTypeFilter, setReportTypeFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [category, setCategory] = useState();
  const [reportName, setReportName] = useState();
  const { showModal } = useModal();
  const css = useClass(styles);
  const reportId = reportName?.reportId;
  const reportType = reportName?.reportType;
  const reportNameKey = reportName?.reportName;
  const { navigate } = useNavigator();

  const { data: paramsStandardReport, loading: paramsStandardReportLoading } = useRequest({
    key: [QUERY_KEY.GET_PARAMS_REPORT_STANDARD, reportType, reportId],
    url: API_REPORT_MODULE.PARAMETERS,
    requiredParams: {
      reportModuleId: reportId,
    },
    enabled: reportType === 'ReportModule' && !!reportId,
  });

  const { data: paramsDynamicReport, loading: paramsDynamicReportLoading } = useRequest({
    key: [QUERY_KEY.GET_PARAMS_REPORT_DYNAMIC, reportType, reportId],
    url: API_DYNAMIC_REPORT.RETRIEVES_THE_SEARCH_PARAMETERS_OF_A_DYNAMIC_REPORT(reportId),
    enabled: reportType === 'DynamicReport' && !!reportId,
  });

  const { data: categoryOptions = [], loading: loadingCategory } = useRequest({
    key: QUERY_KEY.LIST_CATEGORY,
    url: API_REPORT_MODULE.REPORT_MODULES_CATEGORY,
  });

  const executeNormalReport = useRequest({
    url: API_REPORT_MODULE.RUN_ALL_TYPES_OF_REPORT_MODULE,
    method: API_METHOD.POST,
  });

  const executeDynamicReport = useRequest({
    method: API_METHOD.POST,
  });

  const buildBatchPrint = useRequest({
    url: API_REPORT_MODULE.BATCH_FORMS,
    method: API_METHOD.POST,
  });

  const saveBatchPrint = useRequest({
    url: API_REPORT_MODULE.SAVE_BATCH_PRINT_RECORDS,
    method: API_METHOD.POST,
  });

  const formatCategoryOptions = useMemo(
    () =>
      categoryOptions.reduce((acc, item) => {
        const cur = {
          ...item,
          reportModules: (item?.reportModules || []).filter((m) =>
            isInclude(m?.reportFullName, searchTerm),
          ),
          text: item?.reportCategoryName,
          key: item?.reportCategoryName,
        };
        if (cur.reportModules?.length) return [...acc, cur];
        return acc;
      }, []),
    [categoryOptions, searchTerm],
  );

  const reportNameOptions = useMemo(() => {
    const allModules =
      category || !searchTerm
        ? formatCategoryOptions.find((item) => item?.reportCategoryName === category)
            ?.reportModules || []
        : formatCategoryOptions.reduce((acc, cur) => [...acc, ...(cur?.reportModules || [])], []);

    return allModules
      .filter((item) => {
        if (reportTypeFilter === REPORT_TYPE_KEY.ALL) return true;
        if (reportTypeFilter === REPORT_TYPE_KEY.DYNAMIC)
          return item?.reportType === 'DynamicReport';
        if (reportTypeFilter === REPORT_TYPE_KEY.FAVORITE) return item?.favorite;
        if (reportTypeFilter === REPORT_TYPE_KEY.STANDARD)
          return item?.reportType === 'ReportModule';
        return true;
      })
      .map((item) => ({
        ...item,
        key: item?.reportId + item?.reportType,
        text: item?.reportFullName,
        isFavorite: item?.favorite,
        isDynamicReport: item?.reportType === 'DynamicReport',
      }));
  }, [formatCategoryOptions, category, reportTypeFilter, searchTerm]);

  const resetAll = (form) => {
    form?.resetForm();
    setCategory();
    setReportName();
  };

  const formatPayload = (values) => {
    let payload = { ...values };
    let validated = true;

    paramsStandardReport.forEach(({ name, singleSelect, passAllAsNull }) => {
      if (validateFields?.includes(name) && (isNil(payload?.[name]) || payload?.[name] === '')) {
        validated = false;
        return;
      }

      if (singleSelect && payload[name] === 'all') {
        payload[name] = '';
      }

      if (!singleSelect && (payload[name]?.join || payload?.[FORM_LOCAL_DATA]?.[name]?.join)) {
        if (payload?.[FORM_LOCAL_DATA]?.[name]?.length) {
          payload[name] = passAllAsNull
            ? name === 'Speciality'
              ? 'all'
              : null
            : payload?.[FORM_LOCAL_DATA]?.[name]?.join('|');
        } else {
          payload[name] = payload[name]?.join('|');
        }
      }

      payload[name] = formatParamValues(payload[name], {
        reportName: reportName?.reportName,
        paramName: name,
        singleSelect,
        passAllAsNull,
        localData: payload?.[FORM_LOCAL_DATA]?.[name],
      });
    });

    return { validated, payload };
  };

  const onHandleSubmit = (values, form) => {
    if (!reportName) return;
    // Build Batch Print
    if (reportNameKey === 'batchprint') {
      onBuildBatchPrint(values, form);
      return;
    }

    if (!values?.isSchedulingSubmit) {
      //FOR RUNNING
      if (reportName?.reportType === 'ReportModule') {
        const { payload, validated } = formatPayload(values);

        if (!validated) {
          toast.error(t('Please fill in all the required parameters'));
          return;
        }

        delete payload[FORM_LOCAL_DATA];
        executeNormalReport.request({
          payload: {
            ...payload,
            reportName: reportName?.reportName,
            reportFullName: reportName?.reportFullName,
            procedureName: reportName?.procedureName,
            procedureNameV2: reportName?.procedureNameV2,
          },
          options: {
            onSuccess: () => {
              navigate({ hash: MODULE_NAME.RECENT_REPORTS });
              resetAll();
              fetchByQueryKey(QUERY_KEY.RECENT_REPORTS_STATUS_COUNT);
            },
          },
        });
      } else {
        executeDynamicReport.request({
          url: API_DYNAMIC_REPORT.EXECUTE_DYNAMIC_REPORT_BACKGROUND(reportName?.reportId),
          payload: values?.params,
          options: {
            onSuccess: () => {
              navigate({ hash: MODULE_NAME.RECENT_REPORTS });
              resetAll();
              fetchByQueryKey(QUERY_KEY.RECENT_REPORTS_STATUS_COUNT);
            },
          },
        });
      }
    } else {
      //FOR SCHEDULING
      if (reportName?.reportType === 'ReportModule') {
        const { payload, validated } = formatPayload(values);

        if (!validated) {
          toast.error(t('Please fill in all the required parameters'));
          return;
        }

        showModal({
          content: (
            <ScheduleModal
              onAfterClose={() => form?.setFieldValue('isSchedulingSubmit', false)}
              params={payload}
              reportInfo={{
                ...payload,
                reportName: reportName?.reportName,
                reportFullName: reportName?.reportFullName,
                procedureName: reportName?.procedureName,
                procedureNameV2: reportName?.procedureNameV2,
              }}
            />
          ),
          size: MODAL_SIZE.MEDIUM,
        });
      } else {
        showModal({
          content: (
            <ScheduleModal
              onAfterClose={() => form?.setFieldValue('isSchedulingSubmit', false)}
              dynamicReportId={reportId}
              isForDynamicReport={true}
              params={values?.params}
              reportInfo={{ reportFullName: reportName?.reportFullName }}
            />
          ),
          size: MODAL_SIZE.MEDIUM,
        });
      }
    }
  };

  useEffect(() => {
    setReportName();
  }, [category]);

  const validateFields = reportName?.validator
    ?.split(';')
    ?.find((str) => str?.includes('validateRequired'))
    ?.replace('validateRequired: ', '')
    ?.split(',');

  const initialValues = useMemo(
    () =>
      reportType === 'DynamicReport'
        ? { params: paramsDynamicReport || [] }
        : setInitialValues(paramsStandardReport, reportName?.reportName),
    [paramsStandardReport, paramsDynamicReport, reportName?.reportName],
  );

  const onSearch = useCallback(
    debounceFunc((searchValue) => setSearchTerm(searchValue), 100),
    [],
  );

  const onRunAnother = () => {
    setSearchTerm('');
  };

  const onBuildBatchPrint = (values, form) => {
    const { validated } = formatPayload(values);

    if (!validated) {
      toast.error(t('Please fill in all the required parameters'));
      return;
    }

    form?.setFieldValue(`${FORM_LOCAL_DATA}.isShowEHIHistory`, false);

    const payloadRequest = {
      ...values,
      employeeIds: values?.Employee,
      formTypeIds: values?.FormTypes?.length
        ? values?.FormTypes
        : (values?.[FORM_LOCAL_DATA]?.FormTypes || []).map((item) => Number(item)),
    };
    delete payloadRequest[FORM_LOCAL_DATA];
    buildBatchPrint.request({
      payload: payloadRequest,
      options: {
        onSuccess: (data) => {
          form?.setFieldValue(`${FORM_LOCAL_DATA}.tableBuildData`, data);
          setTimeout(() => scrollIntoViewId(ELEMENT_ID.REPORT.TABLE_BUILD_BATCH_PRINT), 1000);
        },
      },
    });
  };

  const onSaveBatchPrint = (payload, form) => {
    saveBatchPrint.request({
      payload,
      options: {
        onSuccess: () => {
          form?.setFieldValue(`${FORM_LOCAL_DATA}.tableBuildData`, null);
          form?.setFieldValue(`${FORM_LOCAL_DATA}.isShowEHIHistory`, true);
          form?.setFieldValue(`${FORM_LOCAL_DATA}.ehiClientName`, form?.values?.ClientName);
          setTimeout(() => scrollIntoViewId(ELEMENT_ID.REPORT.EHI_EXPORT_HISTORY_REPORT), 1000);
        },
      },
    });
  };

  return (
    <CollapseVertical
      id={ELEMENT_ID.REPORT.REPORT_MODULE}
      className="mb-24"
      open
      title={t('Report Module')}
      rightSide={
          <Stack horizontal tokens={{ childrenGap: 12 }}>
            <SearchBox
              iconProps={{ iconName: 'Zoom' }}
              placeholder={t('Search...')}
              onChange={(_, value) => {
                if (category) setCategory();
                onSearch(value);
              }}
            />
            <SelectorPrimary
              textFieldProps={{
                prefix: t('Show: '),
              }}
              value={reportTypeFilter}
              styles={styleDropdown}
              onChange={(option) => setReportTypeFilter(option?.key)}
              options={REPORT_TYPE}
              showClear={false}
            />
          </Stack>
      }
    >
        <LoadingWrapper
          loading={
            executeNormalReport?.loading ||
            executeDynamicReport?.loading ||
            buildBatchPrint?.loading ||
            saveBatchPrint?.loading
          }
        >
          <Formik initialValues={initialValues} enableReinitialize onSubmit={onHandleSubmit}>
            <Stack tokens={{ childrenGap: 16 }}>
              <ResponsiveGrid>
                <ResponsiveItem md={4}>
                  <ListSelectCategory
                    loading={loadingCategory}
                    onChange={(option) => setCategory(option?.key)}
                    value={category}
                    options={formatCategoryOptions}
                  />
                </ResponsiveItem>
                <ResponsiveItem md={4}>
                  <ListSelectReport
                    onChange={(option) => setReportName(option)}
                    value={reportName?.key}
                    options={reportNameOptions}
                  />
                </ResponsiveItem>
                <ResponsiveItem md={4}>
                  <Stack tokens={{ childrenGap: 4 }}>
                    <Stack.Item className={css.desc}>{t('Description')}</Stack.Item>
                    <Stack
                      tokens={{ padding: 8 }}
                      className={cn(
                        css.cmClass.backgroundGrey100,
                        css.cmClass.border,
                        css.desContent,
                        css.desc,
                      )}
                    >
                      {reportName?.description}
                    </Stack>
                  </Stack>
                </ResponsiveItem>
              </ResponsiveGrid>
              <HorizontalRule mt={8} type="dashed" />
              <Parameters
                paramsStandardReport={paramsStandardReport}
                paramsDynamicReport={paramsDynamicReport}
                loading={paramsStandardReportLoading || paramsDynamicReportLoading}
                reportType={reportName?.reportType}
                validateFields={validateFields}
                reportName={reportName?.reportName}
              />
              <HorizontalRule mt={2} type="dashed" />
              {reportNameKey === 'batchprint' ? (
                <Stack horizontal>
                  <MyField isFast={false}>
                    {({ form }) => (
                      <PrimaryButton
                        data-isbutton="button"
                        disabled={!reportId}
                        onClick={form?.handleSubmit}
                      >
                        {t('Build Report')}
                      </PrimaryButton>
                    )}
                  </MyField>
                </Stack>
              ) : (
                <Stack horizontal tokens={{ childrenGap: 8 }}>
                  <MyField isFast={false}>
                    {({ form }) => (
                      <PrimaryButton
                        text={t('Run Report')}
                        data-isbutton="button"
                        disabled={!reportId}
                        onClick={form?.handleSubmit}
                      />
                    )}
                  </MyField>
                  <MyField isFast={false}>
                    {({ form }) => (
                      <PrimaryButton
                        text={t('Schedule')}
                        data-isbutton="button"
                        onClick={() => {
                          form?.setFieldValue('isSchedulingSubmit', true);
                          form?.handleSubmit();
                        }}
                        disabled={!reportId}
                      />
                    )}
                  </MyField>
                  <MyField isFast={false}>
                    {({ form }) => (
                      <CalloutConfirmation onOk={() => form?.resetForm()}>
                        <DefaultButton>{t('Reset Parameters')}</DefaultButton>
                      </CalloutConfirmation>
                    )}
                  </MyField>
                </Stack>
              )}
              <MyField name={`${FORM_LOCAL_DATA}.tableBuildData`}>
                {({ field: { value }, form }) =>
                  value && (
                    <TableBuildBatch
                      onSaveBatchPrint={(payload, patientData) =>
                        onSaveBatchPrint(payload, form, patientData)
                      }
                      items={value}
                    />
                  )
                }
              </MyField>
              <MyField name={`${FORM_LOCAL_DATA}.isShowEHIHistory`} isFast={false}>
                {({ field: { value }, form }) =>
                  value && (
                    <EHIExportHistory clientName={form?.values?.[FORM_LOCAL_DATA].ehiClientName} />
                  )
                }
              </MyField>
            </Stack>
          </Formik>
        </LoadingWrapper>
    </CollapseVertical>
  );
};

const styles = (theme) => ({
  desc: {
    color: theme?.darkMode ? '#FFFFFF' : theme?.custom?.grey700,
  },
  desContent: {
    height: 303,
    borderRadius: 4,
  },
  titleDone: {
    fontSize: 20,
    lineHeight: 30,
    textAlign: 'center',
  },
});

const styleDropdown = {
  root: {
    minWidth: 200,
  },
};

export default ReportModule;
