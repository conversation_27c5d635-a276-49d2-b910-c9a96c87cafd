import React, { useState } from 'react';
import { useClass, useRequest } from 'hooks';
import { API_LOCUS } from 'constants/urlRequest';
import { DefaultButton, PrimaryButton, Stack } from '@fluentui/react';
import SubCollapse from 'components/Collapse/SubCollapse';
import { t } from 'utils/string';
import LoadingWrapper from 'components/Loading/LoadingWrapper';
import HorizontalRule from 'components/HorizontalRule';
import FieldCheckBox from 'components/Form/FieldCheckbox';
import cn from 'classnames';
import MyField from 'components/Form/Field';
import { computeDimScoreAndStuffs, validate } from './constant';
import { toast } from 'react-toastify';

function Evaluating({ back, next, error, setError }) {
  const css = useClass(styles);
  const apiBaseForm = useRequest({ url: API_LOCUS.BASE_FORM, shouldCache: true });
  const [collapse, setCollapse] = useState(true);

  return (
    <LoadingWrapper loading={apiBaseForm.loading}>
      <Stack tokens={{ childrenGap: 16 }}>
        <Stack horizontal tokens={{ childrenGap: 16 }} horizontalAlign="end">
          <PrimaryButton
            text={t('Collapse All')}
            data-isbutton="button"
            onClick={() => setCollapse(false)}
          />
          <DefaultButton
            text={t('Expand All')}
            data-isbutton="button"
            onClick={() => setCollapse(true)}
          />
        </Stack>
        {apiBaseForm?.data?.map((i, sectionIndex) => (
          <SubCollapse
            key={i.sectionId}
            title={
              <span className={cn({ 'color-error': error === `section-${i.order}` })}>
                {i.sectionName}
              </span>
            }
            open={collapse}
            id={`section-${i.order}`}
            className={css.sectionBackground}
          >
            {i.description && (
              <div className={css.section} dangerouslySetInnerHTML={{ __html: i.description }} />
            )}
            {i.questions.map((ques, quesIndex) => (
              <Stack key={ques.questionId}>
                {(i.description || quesIndex > 0) && (
                  <HorizontalRule mt={i.description ? 16 : quesIndex ? 16 : 0} mb={16} />
                )}
                <div
                  className={cn(
                    'p-16',
                    css.cmClass.border,
                    css.cmClass.borderRadius8,
                    css.cmClass.backgroundGrey100,
                  )}
                >
                  <FieldCheckBox
                    vertical
                    name={['evaluating', sectionIndex, 'questions', quesIndex, 'answer']}
                    onChange={({ setFieldValue, checked, name, item, value, index }) => {
                      let temp = value ? [...value] : [];
                      temp[index] = checked ? item : undefined;
                      setFieldValue(name, temp);
                      setFieldValue(['evaluating', sectionIndex, 'order'], i.order);
                    }}
                    formatValue={(value, item, index) => value?.[index]?.key === item.key}
                    title={`${quesIndex + 1} - ${ques.questionText}`}
                    options={ques.answers.map((ans) => ({
                      key: ans.answerId,
                      text: ans.answerText,
                      isHtml: true,
                      point: +ans.answerValue,
                      value: ans.answerValue,
                      precedence: ques.precedence,
                      answerId: ans.answerId,
                      questionId: ques.questionId,
                    }))}
                  />
                </div>
              </Stack>
            ))}
          </SubCollapse>
        ))}
        <Stack horizontal tokens={{ childrenGap: 16 }}>
          <MyField isFast={false}>
            {({ form: { values, setFieldValue } }) => (
              <PrimaryButton
                text={t('Compute Scores')}
                data-isbutton="button"
                onClick={() => {
                  let errorSection = '';
                  if ((values?.evaluating || []).length < 7) {
                    toast.error(
                      'Each dimensions must have at least one response. Please make a selection.',
                    );
                    errorSection = validate(values?.evaluating || []);
                    setError(errorSection);
                  }

                  if (!errorSection) {
                    setFieldValue(
                      'recommendedDispositionLevel',
                      computeDimScoreAndStuffs(values?.evaluating),
                    );
                    next();
                  }
                }}
              />
            )}
          </MyField>
          <DefaultButton text={t('Back To Start')} data-isbutton="button" onClick={back} />
          <MyField>
            {({ form: { setValues } }) => (
              <DefaultButton
                text={t('Cancel Evaluation')}
                data-isbutton="button"
                onClick={() => {
                  back();
                  setValues({});
                }}
              />
            )}
          </MyField>
        </Stack>
      </Stack>
    </LoadingWrapper>
  );
}

const styles = (theme) => ({
  section: {
    p: { margin: '0 4px' },
  },
  sectionBackground: {
    backgroundColor: theme.darkMode ? theme.palette.backgroundColorShade3 : '#fff',
  },
});

export default Evaluating;
