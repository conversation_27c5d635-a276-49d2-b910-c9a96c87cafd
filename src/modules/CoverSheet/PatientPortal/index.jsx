import React from 'react';
import { <PERSON><PERSON><PERSON>on, Stack, TextField } from '@fluentui/react';
import CollapseVertical from 'components/Collapse/CollapseVertical';
import { t } from 'utils/string';
import FormUndo from 'components/FormUndo';
import { useClass, useModal, useNavigator, useRequest, useSetting } from 'hooks';
import FieldText from 'components/Form/FieldText';
import { API_COVER_SHEET, API_METHOD } from 'constants/urlRequest';
import MyField from 'components/Form/Field';
import LoadingWrapper from 'components/Loading/LoadingWrapper';
import { onPrint } from 'utils/file';
import { SETTING_KEYS } from 'constants/settingKeys';
import moment from 'moment';
import SubCollapse from 'components/Collapse/SubCollapse';
import AssociatedContactLogins from './AssociatedContactLogins';
import ModalConfirm from 'components/Modal/ModalConfirm';
import { MODAL_SIZE } from 'constants/modal';

function PatientPortal() {
  const css = useClass();
  const { searchParams } = useNavigator();
  const [DATE_FORMAT, TIME_FORMAT] = useSetting(SETTING_KEYS.DATE_FORMAT, SETTING_KEYS.TIME_FORMAT);
  const { showModal, hideModal } = useModal();

  const apiPortalInfo = useRequest({
    url: API_COVER_SHEET.PATIENT_PORTAL,
    requiredParams: {
      patientId: searchParams.patientId,
    },
  });

  const apiSetup = useRequest({
    url: API_COVER_SHEET.PATIENT_PORTAL,
    method: API_METHOD.POST,
    requiredParams: {
      patientId: searchParams.patientId,
    },
    onSuccess: apiPortalInfo.refetch,
  });

  const apiReset = useRequest({
    url: API_COVER_SHEET.PATIENT_PORTAL_RESET_PASS,
    method: API_METHOD.PUT,
  });

  return (
    <CollapseVertical title={t('Patient Portal')} open className="mb-24">
      <SubCollapse title={t('Patient Portal Input')} open className="mb-16">
        <FormUndo
          initialValues={
            apiPortalInfo?.data
              ? {
                  ...apiPortalInfo?.data,
                  portalLogin: apiPortalInfo?.data.portalLogin || 'No Login',
                }
              : {}
          }
          enableReinitialize
        >
          <LoadingWrapper loading={apiPortalInfo.loading || apiSetup.loading || apiReset.loading}>
            <div className={css.cmClass.grid4Columns}>
              <FieldText name="portalLogin" title={t('Portal UserName')} readOnly voice={false} />
              <MyField name="lastPortalLoginTime">
                {({ field }) => (
                  <TextField
                    value={
                      !!field.value
                        ? moment(field.value).format(`${DATE_FORMAT} ${TIME_FORMAT}`)
                        : t('Never Logged In')
                    }
                    readOnly
                    label={t('Last Login')}
                    className="read-only"
                  />
                )}
              </MyField>
              <div className="self-end">
                <MyField name="portalLogin" isFast={false}>
                  {({ field }) =>
                    field.value !== 'No Login' ? (
                      <Stack tokens={{ childrenGap: 16 }} horizontal>
                        <PrimaryButton
                          text={t('Print')}
                          data-isbutton="button"
                          onClick={() =>
                            onPrint({
                              url: API_COVER_SHEET.PATIENT_PORTAL_PRINT,
                              queryString: {
                                patientId: searchParams.patientId,
                              },
                            })
                          }
                        />
                        {/* #14091 */}
                        {/* <PrimaryButton
                          text={t('Reset Password')}
                          data-isbutton="button"
                          onClick={async () => {
                            const data = await apiReset.request({
                              queryString: { userLogin: field.value },
                            });
                            showModal({
                              content: (
                                <ModalConfirm
                                  title={t('Reset password successful!')}
                                  message={
                                    <span>
                                      The new portal user password is <strong>{data}</strong>
                                    </span>
                                  }
                                  onOk={hideModal}
                                  showCancelButton={false}
                                />
                              ),
                              size: MODAL_SIZE.X_SMALL,
                            });
                          }}
                        /> */}
                      </Stack>
                    ) : (
                      <PrimaryButton
                        text={t('Setup Portal Access')}
                        data-isbutton="button"
                        onClick={async () => {
                          const setup = await apiSetup.request();
                          showModal({
                            content: (
                              <ModalConfirm
                                title={t('Success')}
                                message={
                                  <span>
                                    The patient's username is <strong>{setup?.portalLogin}</strong>{' '}
                                    and password is <strong>{setup?.initialPassword}</strong>
                                  </span>
                                }
                                onOk={hideModal}
                                showCancelButton={false}
                              />
                            ),
                            size: MODAL_SIZE.X_SMALL,
                          });
                        }}
                      />
                    )
                  }
                </MyField>
              </div>
            </div>
          </LoadingWrapper>
        </FormUndo>
      </SubCollapse>
      {apiPortalInfo.data?.portalLogin && (
        <SubCollapse title={t('Associated Contact Logins')} open>
          <AssociatedContactLogins />
        </SubCollapse>
      )}
    </CollapseVertical>
  );
}

export default PatientPortal;
