import React, { useCallback } from 'react';
import { PrimaryButton, SearchBox, Stack } from '@fluentui/react';
import cn from 'classnames';
import SubCollapse from 'components/Collapse/SubCollapse';
import SelectorFilter from 'components/Selector/SelectorFilter';
import Table from 'components/Table';
import ELEMENT_ID from 'constants/elementId';
import { DEFAULT_PAGINATION_PARAMS } from 'constants/index';
import { SETTING_KEYS } from 'constants/settingKeys';
import { API_COVER_SHEET } from 'constants/urlRequest';
import { useClass, useNavigator, useRequest, useSetting } from 'hooks';
import { onPrint } from 'utils/file';
import { scrollIntoViewId } from 'utils/globalScroll';
import { formatPhone, t } from 'utils/string';
import { debounceFunc, parseFormatDateBE } from 'utils/time';
import { formatPhoneNumber } from 'react-phone-number-input';

const History = ({ setEditItem }) => {
  const css = useClass();
  const { searchParams } = useNavigator();
  const [DATE_FORMAT] = useSetting(SETTING_KEYS.DATE_FORMAT);

  const { data, loading, updateParams, params, refetch } = useRequest({
    key: `${API_COVER_SHEET.ADDRESS}-history`,
    url: API_COVER_SHEET.ADDRESS,
    params: { ...DEFAULT_PAGINATION_PARAMS, OnlyCurrent: true },
    requiredParams: {
      PatientId: searchParams.patientId,
    },
    autoRefetch: true,
  });

  const getMenu = (item) => ({
    items: [
      ...(item.expirationDate
        ? [
            {
              key: 'no-action',
              text: 'No Action',
              disabled: true,
            },
          ]
        : [
            {
              key: 'edit',
              text: 'Edit',
              iconProps: { iconName: 'Edit' },
              onClick: (item) => {
                setEditItem(item);
                scrollIntoViewId(ELEMENT_ID.COVER_SHEET.ADDRESS.FORM);
              },
            },
          ]),
    ],
    shouldFocusOnMount: false,
  });

  const onFilterStatus = useCallback((dropStatus) => {
    let OnlyCurrent = dropStatus.key;
    updateParams({ OnlyCurrent, Page: 0 });
  }, []);

  const onSearch = useCallback(
    debounceFunc((searchValue) => {
      updateParams({ SearchTerm: searchValue, Page: 0 });
    }, 500),
    [],
  );

  return (
    <SubCollapse title={t('Addresses')} hr open id={ELEMENT_ID.COVER_SHEET.ADDRESS.HISTORY}>
      <div className={cn('mb-16', css.cmClass.grid4Columns)}>
        <SearchBox
          placeholder={t('Search')}
          iconProps={{ iconName: 'Zoom' }}
          onChange={(_, newValue) => onSearch(newValue)}
        />
        <SelectorFilter
          placeholder={t('Status filter')}
          defaultSelectedKey={params.OnlyCurrent}
          options={[
            { key: false, text: t('All') },
            { key: true, text: t('Current') },
          ]}
          onChange={(_, item) => onFilterStatus(item)}
        />
        <div>
          <PrimaryButton
            text={t('Print List')}
            data-isbutton="button"
            onClick={() =>
              onPrint({
                url: API_COVER_SHEET.ADDRESS_PRINT,
                queryString: {
                  patientId: searchParams.patientId,
                  dateFormatString: parseFormatDateBE(DATE_FORMAT),
                },
              })
            }
          />
        </div>
      </div>
      <Table
        columns={columns}
        items={data?.items || []}
        loading={loading}
        getMenuProps={getMenu}
        totalItems={data?.totalItems}
        metadata={params}
        onMetadataChange={updateParams}
        refetch={refetch}
      />
    </SubCollapse>
  );
};

export default History;

const columns = [
  {
    name: 'Type',
    fieldName: 'addressTypeDesc',
    sortable: true,
    renderItem: ({ addressTypeDesc, expirationDate }) => (
      <span className="cell_style">
        {addressTypeDesc} {!expirationDate && t('(Last Known)')}
      </span>
    ),
  },
  {
    name: 'Street Address',
    fieldName: 'streetAddress1',
    sortable: true,
    renderItem: ({ streetAddress1, streetAddress2, city, state, postalCode5 }) => (
      <span className="cell_style">
        {streetAddress1} {streetAddress2} {city} {state} {postalCode5}
      </span>
    ),
  },
  {
    name: 'Contact Info',
    fieldName: 'workPhone',
    sortable: true,
    renderItem: ({ phone, cellPhone, workPhone, email }) => (
      <Stack tokens={{ childrenGap: 4 }}>
        <span>
          {t('Home')}: {formatPhone(phone)}
        </span>
        <span>
          {t('Cell')}: {formatPhoneNumber(cellPhone)}
        </span>
        <span>
          {t('Work')}: {formatPhone(workPhone)}
        </span>
        <span>
          {t('Email')}: {email}
        </span>
      </Stack>
    ),
  },
  {
    name: 'Contact Status',
    fieldName: 'contactStatusDesc',
    sortable: true,
    minWidth: 200,
    renderItem: ({ contactStatusDesc, textMessage }) => (
      <Stack tokens={{ childrenGap: 4 }}>
        <span className="cell_style">{contactStatusDesc}</span>
        {textMessage && <span>{t('Text message allowed')}</span>}
      </Stack>
    ),
  },
  {
    name: 'Address Status',
    fieldName: 'statusDesc',
    sortable: true,
  },
  {
    name: 'Comments',
    fieldName: 'comments',
    sortable: true,
  },
  {
    name: 'Updated',
    fieldName: 'updateDate',
    sortable: true,
    renderItem: ({ updateDate, updateSysId, expirationDate }, _, c, render) => (
      <span className="cell_style">
        {render({ date: updateDate, user: updateSysId })} {expirationDate && t('(Expired)')}
      </span>
    ),
  },
  {
    key: 'Action',
    name: 'Action',
    fieldName: 'action',
  },
];
