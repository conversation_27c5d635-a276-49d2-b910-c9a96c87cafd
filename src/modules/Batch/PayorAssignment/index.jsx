import React, { useMemo } from 'react';
import { Stack, StackItem } from '@fluentui/react';
import TabSidebar from 'components/TabSidebar';
import { t } from 'utils/string';
import Worklist from '../components/Worklist';
import { BATCH_SIDEBAR_SECTION_KEY } from 'constants/batch';
import PayorAssignmentHistoryContent from 'modules/PayorAssignment/PayorAssignmentHistory/PayorAssignmentHistoryContent';
import PayorAssignmentInformationCollapse from 'modules/PayorAssignment/PayorAssignmentInformation/PayorAssignmentInformationCollapse';
import { MODULE_NAME } from 'constants/routes';
import useSecurity from 'hooks/useSecurity';

const PayorAssignment = () => {
  const historySecurity = useSecurity(MODULE_NAME.PAYOR_ASSIGNMENT_HISTORY);
  const infoSecurity = useSecurity(MODULE_NAME.PAYOR_ASSIGNMENT_INFORMATION);

  const SIDEBAR_ITEMS = useMemo(
    () => [
      {
        key: BATCH_SIDEBAR_SECTION_KEY.HISTORY,
        text: t('Payor Assignment History'),
        iconName: 'Search',
        Content: historySecurity?.view ? <PayorAssignmentHistoryContent isRcm /> : <NoPermission />,
      },
      {
        key: BATCH_SIDEBAR_SECTION_KEY.WORK_LIST,
        text: t('Worklist'),
        iconName: 'AllApps',
        Content: <Worklist />,
      },
    ],
    [],
  );

  return (
    <Stack horizontal>
      <TabSidebar items={SIDEBAR_ITEMS} />
      <StackItem grow className="o-hidden">
        {infoSecurity?.view ? (
          <div className="p-16">
            <PayorAssignmentInformationCollapse />
          </div>
        ) : (
          <NoPermission />
        )}
      </StackItem>
    </Stack>
  );
};

export default PayorAssignment;

const NoPermission = () => (
  <div className="p-16">{t('You do not have permission to view this resource.')}</div>
);
