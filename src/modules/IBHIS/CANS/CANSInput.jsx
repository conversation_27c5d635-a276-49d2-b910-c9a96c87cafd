import React, { Fragment, useContext, useEffect, useMemo } from 'react';
import { t } from 'utils/string';
import { DefaultButton, PrimaryButton, Stack } from '@fluentui/react';
import { useClass, useRequest, useModal, useUser } from 'hooks';
import { Controller, FormProvider } from 'react-hook-form';
import FieldSelectorPrimary from 'components/HookForm/FieldSelectorPrimary';
import cn from 'classnames';
import HorizontalRule from 'components/HorizontalRule';
import FieldText from 'components/HookForm/FieldText';
import FieldDate from 'components/HookForm/FieldDate';
import ResponsiveGrid from 'components/Layout/Responsive/Container';
import ResponsiveItem from 'components/Layout/Responsive/Item';
import CollapseVertical from 'components/Collapse/CollapseVertical';
import { API_IBHIS, API_METHOD } from 'constants/urlRequest';
import { formatToFluentOptions } from 'utils';
import LoadingWrapper from 'components/Loading/LoadingWrapper';
import { fetchByQueryKey } from 'apis/queryClient';
import { toast } from 'react-toastify';
import { TEXT_TEMPLATE } from 'constants/texts';
import { handleValidationBE, _get } from 'utils/form';
import FieldChoice from 'components/HookForm/FieldChoice';
import QUERY_KEY from 'constants/queryKey';
import { CANS_FIELDS, CARE_GIVER, descriptions } from './constain';
import FieldLayout from 'components/HookForm/FieldLayout';
import CaregiverModal from './CaregiverModal';
import SubCollapse from 'components/Collapse/SubCollapse';
import sortBy from 'lodash/sortBy';
import { MODAL_SIZE } from 'constants/modal';
import { IBHISContext } from 'contexts/IBHISContext';
import FieldInfo from 'components/HookForm/FieldInfo';
import moment from 'moment';
import StatusInfo from '../UpdateAxiomClient/StatusInfoHookForm';
import CalloutConfirmation from 'components/Callout/CalloutConfirmation';
import useHookForm from 'components/HookForm/useHookForm';
import WatchValues from 'components/HookForm/WatchValues';
import WatchFormState from 'components/HookForm/WatchFormState';

const BoxDescription = ({ descriptions }) => {
  const css = useClass(styles);
  return (
    <div>
      <HorizontalRule />
      <div className={cn(css.cmClass.backgroundColor, css.descriptionBox, 'p-8')}>
        {descriptions?.map((i) => (
          <span key={i} className={cn(css.description, 'text-12-20')}>
            {i}
          </span>
        ))}
      </div>
    </div>
  );
};

const CANActionBox = ({ title = '', description, children }) => {
  const css = useClass(styles);
  return (
    <Stack className={cn(css.cmClass.border, css.actionBox)}>
      <strong className={cn(css.cmClass.backgroundGrey200, css.cmClass.title, 'p-8')}>
        {title?.toUpperCase()}
      </strong>
      <BoxDescription descriptions={description} />
      {children}
    </Stack>
  );
};

function CANSInput({ editItem, setEditItem }) {
  const css = useClass(styles);
  const { showModal } = useModal();
  const { ibhisPatientId } = useContext(IBHISContext);
  const { info } = useUser();

  const defaultInitialValues = useMemo(
    () => ({
      submissionId: null,
      assessmentDate: null,
      assessmentType: null,
      adminCloseReason: null,
      ibhisClientID: ibhisPatientId,
      providerNumber: null,
      hasCaregiver: null,
      assessingPractitionerNPI: null,
      contributorName1: null,
      contributorRelationship1: null,
      contributorName2: null,
      contributorRelationship2: null,
      contributorName3: null,
      contributorRelationship3: null,
      psychosis: null,
      impulsivityHyperactivity: null,
      depression: null,
      anxiety: null,
      oppositional: null,
      conduct: null,
      angerControl: null,
      substanceUse: null,
      adjustmentToTrauma: null,
      familyFunctioning: null,
      livingSituation: null,
      socialFunctioning: null,
      developmentalIntellectual: null,
      decisionMaking: null,
      schoolBehavior: null,
      schoolAchievement: null,
      schoolAttendance: null,
      medicalPhysical: null,
      sexualDevelopment: null,
      sleep: null,
      suicideRisk: null,
      nonSuicidalSelfInjuriousBehavior: null,
      otherSelfHarm: null,
      dangerToOthers: null,
      sexualAggression: null,
      delinquentBehavior: null,
      runaway: null,
      intentionalMisbehavior: null,
      familyStrengths: null,
      interpersonal: null,
      educationalSetting: null,
      talentsInterests: null,
      spiritualReligious: null,
      culturalIdentity: null,
      communityLife: null,
      naturalSupports: null,
      resiliency: null,
      language: null,
      traditionsAndRituals: null,
      culturalStress: null,
      cansCaregiverResourcesAndNeeds: [],
      sexualAbuse: null,
      physicalAbuse: null,
      emotionalAbuse: null,
      neglect: null,
      medicalTrauma: null,
      witnessToCommunitySchoolViolence: null,
      witnessToFamilyViolence: null,
      naturalOrManmadeDisaster: null,
      warTerrorismAffected: null,
      victimWitnessCriminalActivity: null,
      disruptionInCaregivingAttachmntLosses: null,
      parentalCriminalBehaviors: null,
      updateDate: null,
      updateUser: info?.username,
      env: null,
      effectiveDate: null,
      status: 'New',
      cansVersion: null,
      completedBy: null,
      comments: null,
      assessmentTypeDesc: null,
      adminCloseReasonDesc: null,
      providerNumberDesc: null,
      contributorRelationship1Desc: null,
      contributorRelationship2Desc: null,
      contributorRelationship3Desc: null,
      areasOfSufficiencyOrStrength: null,
      areasOfSufficiencyOrStrengthComments: null,
      areasOfPotentialNeed: null,
      agreedUponAreasToProvideSupport: null,
      areasOfSufficiencyOrStrengthGeneralComments: null,
    }),
    [ibhisPatientId, info?.username],
  );

  const methods = useHookForm({
    defaultValues: defaultInitialValues,
    dependencies: { ibhisPatientId, updateUser: info?.username },
  });

  const apiLookup = useRequest({ url: API_IBHIS.CANS_DICTIONARY, shouldCache: true });

  const CLOSE_FIELD = [
    'ibhisClientID',
    'assessmentDate',
    'assessmentType',
    'adminCloseReason',
    'providerNumber',
    'assessingPractitionerNPI',
    'status',
    'completedBy',
    'updateDate',
    'comments',
    'id',
    'submissionID',
  ];

  const apiSave = useRequest({
    url: API_IBHIS.CANS(),
    method: API_METHOD.POST,
  });

  const apiGetRecentAssessment = useRequest({
    url: API_IBHIS.CANS_PREVIOUS,
    enabled: false,
    autoRefetch: true,
  });

  const { data, loading } = useRequest({
    url: API_IBHIS.CANS_CAREGIVERS(),
    shouldCache: true,
  });

  const onSubmit = (values, isToIbhis) => {
    let payload = {
      ...values,
      isToIbhis,
      status: undefined,
      cansCaregiverResourcesAndNeeds: values.cansCaregiverResourcesAndNeeds?.filter(Boolean),
      assessmentDate: moment(values.assessmentDate).startOf('day'),
    };
    if (payload.assessmentType === '5') {
      payload = Object.keys(payload).reduce((acc, key) => {
        if (CLOSE_FIELD.includes(key)) {
          acc[key] = payload[key];
        }
        return acc;
      }, {});
    }

    apiSave.request({
      payload,
      options: {
        onSuccess: () => {
          fetchByQueryKey(QUERY_KEY.IBHIS_CANS);
          toast.success(
            editItem
              ? TEXT_TEMPLATE.UPDATE_SUCCESSFULLY('CANs')
              : TEXT_TEMPLATE.SAVE_SUCCESSFULLY('CANs'),
          );
          onReset();
        },
        onError: (err) => {
          handleValidationBE({ setError: methods.setError }, err);
        },
      },
    });
  };

  useEffect(() => {
    if (editItem) {
      methods.reset(editItem);
    }
  }, [editItem]);

  const onReset = () => {
    setEditItem();
    methods.reset(defaultInitialValues);
  };

  const handleSubmit = (isToIbhis) => {
    methods.clearErrors();
    methods.handleSubmit((values) => onSubmit(values, isToIbhis))();
  };

  return (
    <CollapseVertical open title={t('CANS Input')} className="mb-24" id="CANS-INPUT">
      <FormProvider {...methods}>
        <LoadingWrapper
          loading={
            apiLookup.loading || loading || apiSave.loading || apiGetRecentAssessment.loading
          }
        >
          <Stack tokens={{ childrenGap: 16 }}>
            {/* <Stack
              className={cn(
                css.cmClass.backgroundGrey100,
                css.cmClass.border,
                css.cmClass.borderRadius4,
              )}
              tokens={{ padding: 12 }}
            >
              <CreateNewArea />
            </Stack> */}
            <ResponsiveGrid>
              {/* <ResponsiveItem md={3}>
                <FieldDate name="effectiveDate" title={t('Select A Plan')} />
              </ResponsiveItem> */}
              <ResponsiveItem md={3} newLine>
                <FieldInfo
                  name="ibhisClientID"
                  title={t('IBHIS Client ID')}
                  fieldClassName="flex-1"
                />
              </ResponsiveItem>
              <ResponsiveItem md={3}>
                <FieldDate
                  required
                  name="assessmentDate"
                  title={t('Effective Date')}
                  maxDate={new Date()}
                  //base on documents
                  minDate={new Date('2018-07-01')}
                  readOnly={!!editItem}
                  helpText="Cannot be early than 2018-07-01 and cannot be a future date"
                />
              </ResponsiveItem>
              <ResponsiveItem md={3}>
                <FieldSelectorPrimary
                  name="assessmentType"
                  title={t('CANS Reason/Status')}
                  options={formatToFluentOptions(apiLookup?.data?.assessmentTypes)}
                  onChange={({ data }) => {
                    methods.setValue('assessmentType', data?.key);
                    methods.setValue('adminCloseReason', null);
                  }}
                  disabled={!!editItem}
                  required
                />
              </ResponsiveItem>
              <ResponsiveItem md={3}>
                <WatchValues name="assessmentType">
                  {(value) =>
                    value === '5' && (
                      <FieldSelectorPrimary
                        name="adminCloseReason"
                        title={t('If Administrative Close, Select Reason')}
                        options={formatToFluentOptions(apiLookup?.data?.adminCloseReasons)}
                        required
                      />
                    )
                  }
                </WatchValues>
              </ResponsiveItem>

              <ResponsiveItem md={3}>
                <FieldSelectorPrimary
                  required
                  name="providerNumber"
                  title={t('IBHIS Location Code')}
                  options={formatToFluentOptions(apiLookup?.data?.providerNumbers)}
                />
              </ResponsiveItem>

              <ResponsiveItem md={3}>
                <FieldText
                  required
                  name="assessingPractitionerNPI"
                  title={t('Assessing Practitioner NPI')}
                  maxLength={10}
                  type="text"
                  onChange={({ data }) => {
                    // Only allow numeric characters
                    const numericValue = data.replace(/[^0-9]/g, '');
                    methods.setValue('assessingPractitionerNPI', numericValue);
                  }}
                  helpText="Must contain exactly 10 numeric characters. Example: **********"
                />
              </ResponsiveItem>

              <WatchValues name="assessmentType">
                {(value) =>
                  value !== '5' && (
                    <Fragment>
                      <ResponsiveItem newLine>
                        <div
                          className={cn(
                            css.cmClass.backgroundGrey100,
                            css.cmClass.border,
                            css.cmClass.borderRadius4,
                            'p-16',
                          )}
                        >
                          <ResponsiveGrid>
                            <ResponsiveItem md={3} newLine>
                              <FieldText
                                name="contributorName1"
                                title={t('Contributor 1 Name')}
                                required
                              />
                            </ResponsiveItem>
                            <ResponsiveItem md={3}>
                              <FieldSelectorPrimary
                                placeholder={t('Select a Contributor Relation')}
                                name="contributorRelationship1"
                                title={t('Relation')}
                                options={formatToFluentOptions(
                                  apiLookup?.data?.contributorRelationships,
                                )}
                                required
                              />
                            </ResponsiveItem>

                            <ResponsiveItem md={3} newLine>
                              <FieldText name="contributorName2" title={t('Contributor 2 Name')} />
                            </ResponsiveItem>
                            <ResponsiveItem md={3}>
                              <FieldSelectorPrimary
                                placeholder={t('Select a Contributor Relation')}
                                name="contributorRelationship2"
                                title={t('Relation')}
                                options={formatToFluentOptions(
                                  apiLookup?.data?.contributorRelationships,
                                )}
                              />
                            </ResponsiveItem>

                            <ResponsiveItem md={3} newLine>
                              <FieldText name="contributorName3" title={t('Contributor 3 Name')} />
                            </ResponsiveItem>
                            <ResponsiveItem md={3}>
                              <FieldSelectorPrimary
                                placeholder={t('Select a Contributor Relation')}
                                name="contributorRelationship3"
                                title={t('Relation')}
                                options={formatToFluentOptions(
                                  apiLookup?.data?.contributorRelationships,
                                )}
                              />
                            </ResponsiveItem>
                          </ResponsiveGrid>
                        </div>
                      </ResponsiveItem>

                      <ResponsiveItem md={4} newLine>
                        <FieldChoice
                          name="hasCaregiver"
                          title={t('Has Caregivers')}
                          options={formatToFluentOptions(apiLookup?.data?.hasCaregivers)}
                          formatCheckedOption={(item) => item.key}
                          horizontal
                        />
                      </ResponsiveItem>

                      <WatchValues name="hasCaregiver">
                        {(value) =>
                          value === 'Y' && (
                            <Fragment>
                              {[
                                ...Array(4)
                                  .keys()
                                  .map((i) => (
                                    <ResponsiveItem
                                      md={3}
                                      newLine={i === 0 || undefined}
                                      key={`cansCaregiverResourcesAndNeeds-${i}`}
                                    >
                                      <FieldSelectorPrimary
                                        name={`cansCaregiverResourcesAndNeeds.${i}.id`}
                                        title={`Caregiver/Contact ${i ? i + 1 : ''}`}
                                        options={formatToFluentOptions(data, 'caregiverName', 'id')}
                                        onChange={({ data }) => {
                                          if (!data) {
                                            methods.setValue(
                                              ['cansCaregiverResourcesAndNeeds', i],
                                              null,
                                            );
                                            return;
                                          }
                                          methods.setValue(
                                            `cansCaregiverResourcesAndNeeds.${i}.id`,
                                            data?.key,
                                          );
                                        }}
                                      />
                                    </ResponsiveItem>
                                  )),
                              ]}
                              <ResponsiveItem md={4}>
                                <PrimaryButton
                                  text={t('Add/Edit Contact')}
                                  onClick={() =>
                                    showModal({
                                      content: (
                                        <CaregiverModal
                                          relationship={
                                            apiLookup?.data?.caregiverRelationships || []
                                          }
                                        />
                                      ),
                                      size: MODAL_SIZE.SMALL,
                                    })
                                  }
                                />
                              </ResponsiveItem>
                            </Fragment>
                          )
                        }
                      </WatchValues>

                      <ResponsiveItem md={12}>
                        <SubCollapse title={t('Core & Trauma')} open>
                          <div className={css.cmClass.grid2Columns}>
                            {CANS_FIELDS.map((boxItem) => (
                              <CANActionBox
                                title={boxItem.title}
                                key={boxItem.title}
                                hasMoreButton={false}
                                description={boxItem.descriptions}
                              >
                                <HorizontalRule />
                                <div
                                  className={cn(css.radioChoice, css.cmClass.backgroundGrey200, {
                                    [css.yesNoQuestion]: boxItem.lastItem,
                                  })}
                                >
                                  {boxItem.lastItem ? (
                                    <Fragment>
                                      <strong></strong>
                                      <strong>Yes</strong>
                                      <strong>No</strong>
                                    </Fragment>
                                  ) : (
                                    <Fragment>
                                      <strong></strong>
                                      <strong>0</strong>
                                      <strong>1</strong>
                                      <strong>2</strong>
                                      <strong>3</strong>
                                    </Fragment>
                                  )}
                                </div>
                                <HorizontalRule />
                                <div className={css.cmClass.backgroundColor}>
                                  {boxItem.fields.map((f, index) => (
                                    <div
                                      key={f.name}
                                      className={cn(css.radioChoice, {
                                        [css.yesNoQuestion]: boxItem.lastItem,
                                      })}
                                    >
                                      <WatchFormState name={f.name}>
                                        {(formState) => {
                                          const error = _get(formState?.errors, f.name)?.message;

                                          return (
                                            <span className={error && 'color-error'}>
                                              {f.title}
                                            </span>
                                          );
                                        }}
                                      </WatchFormState>

                                      {sortBy(apiLookup?.data?.[f.options] || [], 'key').map(
                                        (i) => (
                                          <div key={`${i.key}-${f.name}`}>
                                            <Controller
                                              name={f.name}
                                              render={({
                                                field: { onChange, onBlur, value, ref },
                                              }) => (
                                                <input
                                                  ref={ref}
                                                  onBlur={onBlur}
                                                  type="radio"
                                                  className={css.radio}
                                                  checked={i.key === value}
                                                  onChange={() => {
                                                    onChange(i.key);
                                                  }}
                                                />
                                              )}
                                            />
                                          </div>
                                        ),
                                      )}
                                      {index !== boxItem.fields.length - 1 && (
                                        <HorizontalRule className="full" />
                                      )}
                                    </div>
                                  ))}
                                </div>
                              </CANActionBox>
                            ))}
                          </div>
                        </SubCollapse>
                      </ResponsiveItem>
                      <WatchValues name="cansCaregiverResourcesAndNeeds">
                        {(value) =>
                          value && (
                            <ResponsiveItem md={12}>
                              <SubCollapse title={t('Caregiver(s)')} open>
                                <div className={css.cmClass.grid2Columns}>
                                  {[
                                    ...Array(4)
                                      .keys()
                                      .map((i) => {
                                        const caregiver = _get(value, `${i}.id`);
                                        if (!caregiver) return null;
                                        return (
                                          <CANActionBox
                                            title={`Caregiver ${i + 1} Resources and Needs`}
                                            key={`caregiver-${i}`}
                                            hasMoreButton={false}
                                            description={descriptions}
                                          >
                                            <HorizontalRule />

                                            <div
                                              className={cn(
                                                css.radioChoice,
                                                css.cmClass.backgroundGrey200,
                                                css.cmClass.backgroundColor,
                                              )}
                                            >
                                              <strong></strong>
                                              <strong>0</strong>
                                              <strong>1</strong>
                                              <strong>2</strong>
                                              <strong>3</strong>
                                            </div>
                                            <HorizontalRule />
                                            <div className={cn(css.cmClass.backgroundColor)}>
                                              {CARE_GIVER.map((f, index) => (
                                                <div
                                                  key={`${f.title}-${i}`}
                                                  className={css.radioChoice}
                                                >
                                                  <span>{f.title}</span>
                                                  {sortBy(
                                                    apiLookup?.data?.[f.options] || [],
                                                    'key',
                                                  ).map((option) => (
                                                    <WatchValues
                                                      name={f.name(i)}
                                                      key={`${option.key}-${f.name(i)}`}
                                                    >
                                                      {(value) => (
                                                        <FieldLayout name={f.name(i)}>
                                                          <input
                                                            type="radio"
                                                            className={css.radio}
                                                            checked={option.key === value}
                                                            onChange={() =>
                                                              methods.setValue(
                                                                f.name(i),
                                                                option.key,
                                                              )
                                                            }
                                                          />
                                                        </FieldLayout>
                                                      )}
                                                    </WatchValues>
                                                  ))}
                                                  {index !== CARE_GIVER.length - 1 && (
                                                    <HorizontalRule className="full" />
                                                  )}
                                                </div>
                                              ))}
                                            </div>
                                          </CANActionBox>
                                        );
                                      }),
                                  ]}
                                </div>
                              </SubCollapse>
                            </ResponsiveItem>
                          )
                        }
                      </WatchValues>
                    </Fragment>
                  )
                }
              </WatchValues>
            </ResponsiveGrid>

            <WatchValues name="assessmentType">
              {(value) =>
                value !== '5' && (
                  <SubCollapse title={t('Areas of sufficiency or strength')} open>
                    <ResponsiveGrid>
                      <ResponsiveItem md={6}>
                        <FieldText
                          helpText='E.g. Areas marked as "0," "1", or "2" within the Strengths Domain'
                          name="areasOfSufficiencyOrStrength"
                          title={t('1. Areas of sufficiency or strength')}
                        />
                      </ResponsiveItem>
                      <ResponsiveItem md={6}>
                        <FieldText
                          helpText="Include any positive outcomes where previous needs were met or improved upon"
                          name="areasOfSufficiencyOrStrengthComments"
                          title={t('Comments')}
                        />
                      </ResponsiveItem>
                      <ResponsiveItem md={6}>
                        <FieldText
                          helpText='E.g. areas marked as "2" or "3"'
                          name="areasOfPotentialNeed"
                          title={t('2. Areas of potential need')}
                        />
                      </ResponsiveItem>
                      <ResponsiveItem md={6}>
                        <FieldText
                          name="agreedUponAreasToProvideSupport"
                          title={t(
                            'Agreed upon areas to provide support/assistance through linkage & referral',
                          )}
                        />
                      </ResponsiveItem>
                      <ResponsiveItem md={12}>
                        <FieldText
                          helpText="Include history & current status of need, relevant information from significant supports, information from other documents/chart review, & any barriers to getting needs met"
                          name="areasOfSufficiencyOrStrengthGeneralComments"
                          title={t('Comments')}
                        />
                      </ResponsiveItem>
                    </ResponsiveGrid>
                  </SubCollapse>
                )
              }
            </WatchValues>
            <ResponsiveItem md={12} newLine>
              <StatusInfo withComments />
            </ResponsiveItem>
            <Stack horizontal tokens={{ childrenGap: 16 }}>
              <Fragment>
                <PrimaryButton
                  text={t('Submit To IBHIS')}
                  data-istoibhis="true"
                  onClick={() => handleSubmit(true)}
                />
                <PrimaryButton
                  className={css.cmBtn.secondaryButton}
                  text={t('Save as Incomplete')}
                  data-istoibhis="false"
                  onClick={() => handleSubmit(false)}
                />
                <CalloutConfirmation onOk={onReset}>
                  <DefaultButton text={t('Reset')} />
                </CalloutConfirmation>
              </Fragment>
            </Stack>
          </Stack>
        </LoadingWrapper>
      </FormProvider>
    </CollapseVertical>
  );
}

const styles = (theme) => {
  return {
    titleHeight: {
      'div:first-child': {
        minHeight: 24,
      },
      '.ms-Button': {
        minWidth: 0,
      },
    },
    blue: [{ color: '#1890FF' }],
    radioChoice: {
      display: 'grid',
      alignItems: 'center',
      gridTemplateColumns: '5fr repeat(4, 1fr)',
      gap: 4,
      minHeight: 32,
      paddingTop: 4,
      span: {
        marginLeft: 16,
      },
      strong: {
        marginLeft: 8,
      },
      '.full': {
        gridColumn: '1/6',
      },
      // backgroundColor: theme.custom.grey200,
      '&:nth-child(2n)': {
        backgroundColor: theme.darkMode
          ? theme.palette.backgroundColorShade2
          : theme.custom.grey100,
      },
      '&:hover': {
        backgroundColor: theme.darkMode
          ? theme.palette.backgroundColorShade3
          : theme.custom.grey200,
      },
    },
    yesNoQuestion: {
      alignItems: 'center',
      gridTemplateColumns: '4fr 1fr 1fr !important',
      span: {
        marginLeft: 16,
      },
      strong: {
        marginLeft: 2,
      },
      '.full': {
        gridColumn: '1/4',
      },
    },

    radio: {
      height: 24,
      accentColor: theme.palette.primaryColor,
      transform: 'scale(1.3)',
    },
    actionBox: {
      height: 'fit-content',
    },
    descriptionBox: {
      display: 'grid',
      gridTemplateColumns: '1fr 1fr',
      gap: 4,
    },
  };
};

export default CANSInput;
