import React, { memo, useContext, useEffect, useState } from 'react';
import CollapseVertical from 'components/Collapse/CollapseVertical';
import { t } from 'utils/string';
import useNavigator from 'hooks/useNavigator';
import useRequest from 'hooks/useRequest';
import QUERY_KEY from 'constants/queryKey';
import Table from 'components/Table';
import { Link, PrimaryButton, Stack } from '@fluentui/react';
import { MODULE_NAME } from 'constants/routes';
import { IBHISContext } from 'contexts/IBHISContext';
import { API_IBHIS } from 'constants/urlRequest';
import { REPORTS } from '../Reports/constant';
import Episodes from '../Episodes';
import { scrollIntoViewId } from 'utils/globalScroll';
import useUser from 'hooks/useUser';
import AdvancedSearch from './AdvancedSearch';
import { SectionsContext } from 'contexts/SectionsContext';

function SearchClientRecord() {
  const { navigate } = useNavigator();
  const { currentPatient } = useUser();
  const [showSearch, setShowSearch] = useState(false);
  const { hideSection } = useContext(SectionsContext);

  const { ibhisPatientId, setIbhisPatientId, setSelectedReport, setShowPregnancy } =
    useContext(IBHISContext);

  const getMenu = (item) => ({
    items: [
      {
        key: 'episodes',
        text: 'Episodes',
        onClick: () => {
          setShowPregnancy(item.gender === 'F');
          setIbhisPatientId(item.ibhisPatientId);
          setTimeout(() => scrollIntoViewId(MODULE_NAME.IBHIS_EPISODES), 100);
        },
      },
      {
        key: 'client-data',
        text: 'Client Data',
        onClick: () => {
          setShowPregnancy(item.gender === 'F');
          setIbhisPatientId(item.ibhisPatientId);
          navigate({ hash: MODULE_NAME.IBHIS_CLIENT_DATA });
        },
      },
      {
        key: 'legacy-service-history',
        text: 'Legacy Service History',
        onClick: () => {
          setIbhisPatientId(item.ibhisPatientId);
          setSelectedReport(REPORTS.CLIENT_LEGACY_SERVICE_HISTORY);
          navigate({ hash: MODULE_NAME.IBHIS_REPORT });
        },
      },
      {
        key: 'dcfs-service-history',
        text: 'DCFS Service History',
        onClick: () => {
          setIbhisPatientId(item.ibhisPatientId);
          navigate({ hash: MODULE_NAME.IBHIS_REPORT });
          setSelectedReport(REPORTS.CLIENT_DCFS_SERVICE_HISTORY);
        },
      },
      {
        key: 'public-guardian-service-history',
        text: 'Public Guardian Service History',
        onClick: () => {
          setIbhisPatientId(item.ibhisPatientId);
          navigate({ hash: MODULE_NAME.IBHIS_REPORT });
          setSelectedReport(REPORTS.PUBLIC_GUARDIAN_SERVICE_HISTORY);
        },
      },
      {
        key: 'psc',
        text: 'EPSDT - PSC',
        onClick: () => {
          setIbhisPatientId(item.ibhisPatientId);
          navigate({ hash: MODULE_NAME.IBHIS_PSC });
        },
      },
      {
        key: 'locus',
        text: 'LOCUS',
        onClick: () => {
          setIbhisPatientId(item.ibhisPatientId);
          navigate({ hash: MODULE_NAME.IBHIS_LOCUS });
        },
      },
    ],
    shouldFocusOnMount: false,
  });

  const { data, loading, updateParams } = useRequest({
    key: QUERY_KEY.IBHIS_CLIENT_RECORD,
    url: API_IBHIS.SEARCH_CLIENT,
    enabled: false,
    autoRefetch: true,
    saveHistory: false,
    showAdditionalData: true,
    onSuccess: (data) => {
      setShowPregnancy(data?.[0]?.gender === 'F');
    },
  });

  useEffect(() => {
    setIbhisPatientId(currentPatient?.ibhisClientId);
    hideSection(MODULE_NAME.IBHIS_UPDATE);
    setShowSearch(false);
    if (currentPatient?.ibhisClientId) {
      updateParams({
        searchType: 'ByIbhisPatientId',
        IbhisPatientId: currentPatient?.ibhisClientId,
      });
    }
  }, [currentPatient?.ibhisClientId]);

  return (
    <React.Fragment>
      <CollapseVertical open title={t('IBHIS Client Record')} className="mb-24">
        {!ibhisPatientId ? (
          <Stack tokens={{ childrenGap: 8 }}>
            <section>
              {t('No IBHIS ID was found for ')}
              <span className="weight-600">{currentPatient.fullName}</span>
              {t(
                ". To establish an episode for this patient, you'll need to add them to IBHIS first.",
              )}
            </section>
            <Stack horizontal tokens={{ childrenGap: 12 }}>
              <PrimaryButton
                text={t('Add Client to IBHIS')}
                data-isbutton="button"
                onClick={() => {
                  setIbhisPatientId();
                  setShowSearch(false);
                  navigate({ hash: MODULE_NAME.IBHIS_CLIENT_DATA });
                }}
              />
            </Stack>
            <section>
              {t('Not seeing what you expect? Use ')}
              <Link underline onClick={() => setShowSearch(true)}>
                {t('Client Records Advanced Search')}
              </Link>
            </section>
          </Stack>
        ) : (
          <Table
            columns={columns}
            loading={loading}
            getMenuProps={getMenu}
            items={data}
            pagination={false}
          />
        )}
      </CollapseVertical>
      {showSearch && <AdvancedSearch />}
      {!!ibhisPatientId && <Episodes />}
    </React.Fragment>
  );
}

export default memo(SearchClientRecord);

const columns = [
  { name: 'ID', fieldName: 'ibhisPatientId' },
  {
    name: 'Client Name',
    fieldName: 'patientFirstName',
    sortable: true,
    renderItem: (i) =>
      `${i?.patientFirstName || ''} ${i?.patientMiddleInitial || ''} ${i?.patientLastName || ''}`,
  },
  { name: 'Alias', fieldName: 'alias', sortable: true },
  {
    name: 'Gender',
    fieldName: 'gender',
    sortable: true,
    renderItem: ({ gender }) => (gender === 'F' ? 'Female' : 'Male'),
  },
  {
    name: 'DOB',
    fieldName: 'dateOfBirth',
    sortable: true,
    renderItem: ({ dateOfBirth }, _, c, render) => render({ date: dateOfBirth }),
  },
  {
    name: 'Address Info',
    fieldName: 'streetAddress1',
    sortable: true,
    renderItem: (i) =>
      `${i?.streetAddress1 || i?.streetAddress2 || ''} ${i?.city || ''} ${i?.state || ''}`,
  },
  {
    name: 'Last Sync',
    sortable: true,
    fieldName: 'updateDateTime',
    renderItem: ({ updateDateTime, updateUser }, _, c, render) =>
      render({ date: updateDateTime, user: updateUser, withTime: true }),
  },
  { key: 'Action', name: 'Action', fieldName: 'Action' },
];
