import React, { memo, useContext, useEffect, useMemo, useState } from 'react';
import CollapseVertical from 'components/Collapse/CollapseVertical';
import { t } from 'utils/string';
import useModal from 'hooks/useModal';
import useNavigator from 'hooks/useNavigator';
import useRequest from 'hooks/useRequest';
import { DEFAULT_PAGINATION_PARAMS } from 'constants';
import QUERY_KEY from 'constants/queryKey';
import Table from 'components/Table';
import { PrimaryButton, Stack } from '@fluentui/react';
import { Formik } from 'formik';
import FieldText from 'components/Form/FieldText';
import HorizontalRule from 'components/HorizontalRule';
import { MODULE_NAME } from 'constants/routes';
import { IBHISContext } from 'contexts/IBHISContext';
import { API_IBHIS } from 'constants/urlRequest';
import { formatToFluentOptions } from 'utils';
import FieldSelectorPrimary from 'components/Form/FieldSelectorPrimary';
import MyField from 'components/Form/Field';
import FieldDate from 'components/Form/FieldDate';
import ResponsiveGrid from 'components/Layout/Responsive/Container';
import ResponsiveItem from 'components/Layout/Responsive/Item';
import queryClient from 'apis/queryClient';
import { REPORTS } from '../Reports/constant';
import { MODAL_SIZE } from 'constants/modal';
import LoadingWrapper from 'components/Loading/LoadingWrapper';
import { scrollIntoViewId } from 'utils/globalScroll';
import useUser from 'hooks/useUser';
import LinkAxiomClient from '../LinkAxiomClient';
import orderBy from 'lodash/orderBy';

function AdvancedSearch({ report }) {
  const { navigate, searchParams } = useNavigator();
  const { currentPatient } = useUser();

  const { showModal } = useModal();
  const { setIbhisPatientId, setSelectedReport, setShowPregnancy } = useContext(IBHISContext);
  const [metadata, setMetadata] = useState(DEFAULT_PAGINATION_PARAMS);

  const { data: searchType } = useRequest({ url: API_IBHIS.SEARCH_TYPE, shouldCache: true });
  const apiLookup = useRequest({ url: API_IBHIS.DEMOGRAPHIC_LOOKUP, shouldCache: true });

  const getMenu = (item) => ({
    items: [
      {
        key: 'episodes',
        text: 'Episodes',
        onClick: () => {
          setShowPregnancy(item.gender === 'F');
          setIbhisPatientId(item.ibhisPatientId);
          setTimeout(() => scrollIntoViewId(MODULE_NAME.IBHIS_EPISODES), 100);
        },
      },
      {
        key: 'client-data',
        text: 'Client Data',
        onClick: () => {
          setIbhisPatientId(item.ibhisPatientId);
          navigate({ hash: MODULE_NAME.IBHIS_CLIENT_DATA });
        },
      },
      {
        key: 'legacy-service-history',
        text: 'Legacy Service History',
        onClick: () => {
          setIbhisPatientId(item.ibhisPatientId);
          setSelectedReport(REPORTS.CLIENT_LEGACY_SERVICE_HISTORY);
          navigate({ hash: MODULE_NAME.IBHIS_REPORT });
        },
      },
      {
        key: 'dcfs-service-history',
        text: 'DCFS Service History',
        onClick: () => {
          setIbhisPatientId(item.ibhisPatientId);
          navigate({ hash: MODULE_NAME.IBHIS_REPORT });
          setSelectedReport(REPORTS.CLIENT_DCFS_SERVICE_HISTORY);
        },
      },
      {
        key: 'public-guardian-service-history',
        text: 'Public Guardian Service History',
        onClick: () => {
          setIbhisPatientId(item.ibhisPatientId);
          navigate({ hash: MODULE_NAME.IBHIS_REPORT });
          setSelectedReport(REPORTS.PUBLIC_GUARDIAN_SERVICE_HISTORY);
        },
      },
      {
        key: 'psc',
        text: 'EPSDT - PSC',
        onClick: () => {
          setIbhisPatientId(item.ibhisPatientId);
          navigate({ hash: MODULE_NAME.IBHIS_PSC });
        },
      },
    ],
    shouldFocusOnMount: false,
  });

  const { data, loading, updateParams } = useRequest({
    key: QUERY_KEY.IBHIS_CLIENT_RECORD_HISTORY,
    url: API_IBHIS.SEARCH_CLIENT,
    enabled: false,
    autoRefetch: true,
    saveHistory: false,
    showAdditionalData: true,
    onError: () => {
      queryClient.setQueryData([QUERY_KEY.IBHIS_CLIENT_RECORD_HISTORY], []);
    },
  });

  const apiGetIbhisClient = useRequest({
    key: [API_IBHIS.PATIENT_LINK_LOOkUP, searchParams.patientId],
    url: API_IBHIS.PATIENT_LINK_LOOkUP,
    requiredParams: { patientId: searchParams.patientId },
    autoRefetch: true,
  });

  useEffect(() => {
    if (apiGetIbhisClient?.data?.ibhisPatientId) {
      setIbhisPatientId(apiGetIbhisClient?.data?.ibhisPatientId);
      updateParams({
        searchType: 'ByIbhisPatientId',
        IbhisPatientId: apiGetIbhisClient?.data?.ibhisPatientId,
        isFromIbhis: false,
      });
    } else {
      setIbhisPatientId();
      updateParams({
        searchType: 'ByNameAkaGender',
        patientFirstName: currentPatient?.firstName,
        patientLastName: currentPatient?.lastName,
        gender: currentPatient?.gender,
        isFromIbhis: false,
      });
    }
  }, [apiGetIbhisClient?.data?.ibhisPatientId]);

  const initialValues = useMemo(
    () =>
      apiGetIbhisClient?.data?.ibhisPatientId
        ? {
            searchType: 'ByIbhisPatientId',
            IbhisPatientId: apiGetIbhisClient?.data?.ibhisPatientId,
          }
        : {
            searchType: 'ByNameAkaGender',
            patientFirstName: currentPatient?.firstName,
            patientLastName: currentPatient?.lastName,
            gender: currentPatient?.gender,
          },
    [apiGetIbhisClient?.data?.ibhisPatientId],
  );

  const onSubmit = (values) => {
    updateParams(values, true);
    setMetadata((prev) => ({ ...prev, Page: 0 }));
  };

  return (
    <CollapseVertical
      open
      title={t('IBHIS Client Record')}
      id={report && 'searchClientRecord'}
      className="mb-24"
      contentClassName="p-0"
    >
      <Stack tokens={{ childrenGap: 16, padding: 16 }}>
        <Stack horizontal tokens={{ childrenGap: 12 }}></Stack>
        <Formik initialValues={initialValues} enableReinitialize onSubmit={() => {}}>
          {({ values }) => (
            <ResponsiveGrid>
              <ResponsiveItem md={2}>
                <FieldSelectorPrimary
                  isFast={false}
                  title={t('Search Type')}
                  name="searchType"
                  fieldClassName="flex-1"
                  showClear={false}
                  options={formatToFluentOptions(searchType)}
                />
              </ResponsiveItem>

              <MyField name="searchType" isFast={false}>
                {({ field }) => {
                  if (field?.value === 'ByIbhisPatientId')
                    return (
                      <ResponsiveItem md={2}>
                        <FieldText
                          title={t('IBHIS Client Id')}
                          name="IbhisPatientId"
                          multiline={false}
                          required
                          fieldClassName="flex-1"
                        />
                      </ResponsiveItem>
                    );
                  if (field?.value === 'ByCinNumber')
                    return (
                      <ResponsiveItem md={2}>
                        <FieldText
                          title={t('CIN Number')}
                          fieldClassName="flex-1"
                          name="CinNumber"
                          required
                          multiline={false}
                        />
                      </ResponsiveItem>
                    );

                  const common = (
                    <React.Fragment>
                      <ResponsiveItem md={2}>
                        <FieldText
                          title={t('Client First Name')}
                          name="patientFirstName"
                          multiline={false}
                          required
                          fieldClassName="flex-1"
                        />
                      </ResponsiveItem>
                      <ResponsiveItem md={2}>
                        <FieldText
                          title={t('Client Last Name')}
                          name="patientLastName"
                          multiline={false}
                          required
                          fieldClassName="flex-1"
                        />
                      </ResponsiveItem>
                      <ResponsiveItem md={2}>
                        <FieldText
                          title={t('A.K.A')}
                          name="alias"
                          multiline={false}
                          fieldClassName="flex-1"
                        />
                      </ResponsiveItem>
                      <ResponsiveItem md={2}>
                        <LoadingWrapper loading={apiLookup.loading}>
                          <FieldSelectorPrimary
                            isFast={false}
                            title={t('Gender')}
                            name="gender"
                            required
                            fieldClassName="flex-1"
                            options={formatToFluentOptions(
                              apiLookup?.data?.gender,
                              'description',
                              'code',
                            )}
                          />
                        </LoadingWrapper>
                      </ResponsiveItem>
                    </React.Fragment>
                  );
                  if (field?.value === 'ByNameAkaGender') return common;
                  if (field?.value === 'ByNameAkaGenderDob')
                    return (
                      <React.Fragment>
                        {common}
                        <ResponsiveItem md={2}>
                          <FieldDate
                            title={t('DOB')}
                            name="dateOfBirth"
                            required
                            isRenderRightSide={false}
                            fieldClassName="flex-1"
                          />
                        </ResponsiveItem>
                      </React.Fragment>
                    );
                  if (field?.value === 'ByNameAkaGenderSsn')
                    return (
                      <React.Fragment>
                        {common}
                        <ResponsiveItem md={2}>
                          <FieldText title={t('SSN')} name="SSN" required fieldClassName="flex-1" />
                        </ResponsiveItem>
                      </React.Fragment>
                    );
                }}
              </MyField>
              <ResponsiveItem md={6} style={{ alignContent: 'end' }}>
                <Stack horizontal tokens={{ childrenGap: 12 }}>
                  <PrimaryButton
                    text={t('Search IBHIS')}
                    disabled={!values.searchType}
                    iconProps={{ iconName: 'Zoom' }}
                    onClick={() => {
                      onSubmit({
                        ...values,
                        isFromIbhis: true,
                      });
                    }}
                  />
                  <PrimaryButton
                    text={t('Link Axiom Client to IBHIS')}
                    disabled={!data?.length}
                    onClick={() =>
                      showModal({
                        content: <LinkAxiomClient data={data} />,
                        size: MODAL_SIZE.MEDIUM,
                      })
                    }
                  />
                </Stack>
              </ResponsiveItem>
            </ResponsiveGrid>
          )}
        </Formik>
        <HorizontalRule type="dashed" mt={16} />
        <Table
          columns={columns}
          loading={loading}
          getMenuProps={report ? undefined : getMenu}
          items={orderBy(data || [], metadata.OrderBy, metadata.OrderDirection.toLowerCase()).slice(
            metadata?.Page * metadata?.PageSize,
            metadata?.Page * metadata?.PageSize + metadata?.PageSize,
          )}
          totalItems={data?.length || 0}
          onMetadataChange={(newValue) => setMetadata((oldValue) => ({ ...oldValue, ...newValue }))}
          metadata={metadata}
        />
      </Stack>
    </CollapseVertical>
  );
}

export default memo(AdvancedSearch);

const columns = [
  { name: 'ID', fieldName: 'ibhisPatientId' },
  {
    name: 'Client Name',
    fieldName: 'patientFirstName',
    sortable: true,
    renderItem: (i) =>
      `${i?.patientFirstName || ''} ${i?.patientMiddleInitial || ''} ${i?.patientLastName || ''}`,
  },
  { name: 'Alias', fieldName: 'alias', sortable: true },
  {
    name: 'Gender',
    fieldName: 'gender',
    sortable: true,
    renderItem: ({ gender }) => (gender === 'F' ? 'Female' : 'Male'),
  },
  {
    name: 'DOB',
    fieldName: 'dateOfBirth',
    sortable: true,
    renderItem: ({ dateOfBirth }, _, c, render) => render({ date: dateOfBirth }),
  },
  {
    name: 'Address Info',
    fieldName: 'streetAddress1',
    sortable: true,
    renderItem: (i) =>
      `${i?.streetAddress1 || i?.streetAddress2 || ''} ${i?.city || ''} ${i?.state || ''}`,
  },
  { name: 'SSN', fieldName: 'socialSecurityNumber', sortable: true },
  { name: 'Score', fieldName: 'score', sortable: true },
  {
    name: 'Last Sync',
    sortable: true,
    fieldName: 'updateDateTime',
    renderItem: ({ updateDateTime, updateUser }, _, c, render) =>
      render({ date: updateDateTime, user: updateUser, withTime: true }),
  },
  { key: 'Action', name: 'Action', fieldName: 'Action' },
];
