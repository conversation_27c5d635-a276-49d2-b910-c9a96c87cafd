import React, { useContext, useMemo } from 'react';
import { t } from 'utils/string';
import <PERSON><PERSON>ield from 'components/Form/Field';
import { PrimaryButton, Stack } from '@fluentui/react';
import useClass from 'hooks/useClass';
import useRequest from 'hooks/useRequest';
import { Formik } from 'formik';
import { API_IBHIS, API_METHOD } from 'constants/urlRequest';
import FieldDate from 'components/Form/FieldDate';
import QUERY_KEY from 'constants/queryKey';
import { handleValidationBE } from 'utils/form';
import { toast } from 'react-toastify';
import { TEXT_TEMPLATE } from 'constants/texts';
import FieldText from 'components/Form/FieldText';
import FieldSelectorPrimary from 'components/Form/FieldSelectorPrimary';
import { formatToFluentOptions } from 'utils';
import { IBHISContext } from 'contexts/IBHISContext';
import LoadingWrapper from 'components/Loading/LoadingWrapper';
import ClientInfo from '../ClientInfo';
import ActionBox from 'modules/Analytic/components/ActionBox';
import FieldCheckBox from 'components/Form/FieldCheckbox';
import { FORM_LOCAL_DATA } from 'constants/index';
import StatusInfo from '../StatusInfo';

function FinancialEligibility() {
  const css = useClass();
  const { ibhisPatientId, episode } = useContext(IBHISContext);

  const { data, loading, updateParams } = useRequest({
    key: [QUERY_KEY.IBHIS_FINANCIAL_ELIGIBILITY, ibhisPatientId],
    url: API_IBHIS.FINANCIAL_ELIGIBILITY,
    requiredParams: { ibhisPatientId, episodeId: episode?.episodeID },
    autoRefetch: true,
    shouldCache: true,
  });

  const initialValues = useMemo(() => {
    const laCountyGuarantor = data?.clientFinEligibility?.find(
      (i) => i?.guarantor?.guarantorName === 'LA County',
    );
    const mediCalGuarantor = data?.clientFinEligibility?.find(
      (i) => i?.guarantor?.guarantorName === 'Medi-Cal',
    );
    const updateDateTime = laCountyGuarantor?.updateDateTime || mediCalGuarantor?.updateDateTime;
    const status = laCountyGuarantor?.status || mediCalGuarantor?.status;
    const updateUser = laCountyGuarantor?.updateUser || mediCalGuarantor?.updateUser;

    return { laCountyGuarantor, mediCalGuarantor, updateDateTime, status, updateUser };
  }, [data?.clientFinEligibility]);

  const { data: gender } = useRequest({
    key: [API_IBHIS.DICTIONARY, 'Gender'],
    url: API_IBHIS.DICTIONARY,
    params: { lookupType: 'Gender' },
  });

  const apiSave = useRequest({
    url: API_IBHIS.FINANCIAL_ELIGIBILITY,
    method: API_METHOD.POST,
  });

  const onSubmit = (form, isToIbhis) => {
    const { AddNewMediCal, UpdateExistingMediCal, UpdateNonMediCal } =
      form?.values?.[FORM_LOCAL_DATA];
    if (!AddNewMediCal && !UpdateExistingMediCal && !UpdateNonMediCal) {
      toast.error(t('Please select at least one option'));
      return;
    }
    const payload = {
      ...(form?.values || {}),
      isToIbhis,
      status: undefined,
      createClientFinEligibility: AddNewMediCal
        ? 'AddNewMediCal'
        : UpdateExistingMediCal
        ? 'UpdateExistingMediCal'
        : 'UpdateNonMediCal',
    };
    delete payload[FORM_LOCAL_DATA];

    apiSave.request({
      payload,
      options: {
        onSuccess: () => {
          updateParams({ isFromIbhis: false });
          toast.success(TEXT_TEMPLATE.SAVE_SUCCESSFULLY(t('Financial Eligibility Information')));
        },
        onError: (err) => handleValidationBE(form, err),
      },
    });
  };

  return (
    <Formik
      initialValues={{
        ...episode,
        ...initialValues,
        episodeId: episode?.episodeID,
        ibhisPatientId,
        [FORM_LOCAL_DATA]: {
          UpdateNonMediCal: true,
          UpdateExistingMediCal: !!initialValues?.mediCalGuarantor,
        },
      }}
      enableReinitialize
      onSubmit={() => {}}
    >
      <LoadingWrapper loading={loading || apiSave.loading}>
        <Stack tokens={{ childrenGap: 16 }}>
          <ClientInfo
            lastSync={data?.clientFinEligibility?.[0]?.updateDateTime}
            refetch={() => updateParams({ isFromIbhis: true })}
          />
          <ActionBox title={t('Financial Guarantor Information')} hasMoreButton={false}>
            <section>{t('Which guarantor(s) apply to this client?')}</section>
            <Stack horizontal tokens={{ childrenGap: 24 }}>
              {!initialValues?.mediCalGuarantor ? (
                <FieldCheckBox
                  name={[FORM_LOCAL_DATA, 'AddNewMediCal']}
                  label={t('Medi-Cal')}
                  onChange={({ setFieldValue, checked, name }) => {
                    setFieldValue(name, checked);
                    if (checked) setFieldValue([FORM_LOCAL_DATA, 'UpdateExistingMediCal'], false);
                  }}
                />
              ) : (
                <FieldCheckBox
                  name={[FORM_LOCAL_DATA, 'UpdateExistingMediCal']}
                  label={t('Medi-Cal')}
                  onChange={({ setFieldValue, checked, name }) => {
                    setFieldValue(name, checked);
                    if (checked) setFieldValue([FORM_LOCAL_DATA, 'AddNewMediCal'], false);
                  }}
                />
              )}
              <FieldCheckBox
                name={[FORM_LOCAL_DATA, 'UpdateNonMediCal']}
                label={t('LA County Financial Guarantor')}
              />
            </Stack>
          </ActionBox>
          <MyField name={FORM_LOCAL_DATA}>
            {({ field }) =>
              (field?.value?.['AddNewMediCal'] || field?.value?.['UpdateExistingMediCal']) && (
                <ActionBox title={t('MediCal Subscriber Information')} hasMoreButton={false}>
                  <div className={css.cmClass.grid4Columns}>
                    <FieldText
                      name="mediCalGuarantor.subscriberFirstName"
                      title={t('Subscriber First Name')}
                    />
                    <FieldText
                      name="mediCalGuarantor.subscriberLastName"
                      title={t('Subscriber Last Name')}
                    />
                    <FieldText
                      name="mediCalGuarantor.subscriberAddress"
                      title={t('Subscriber Address')}
                    />
                    <FieldText
                      name="mediCalGuarantor.subscriberAddress2"
                      title={t('Subscriber Address 2')}
                    />
                    <FieldText name="mediCalGuarantor.subscriberZip" title={t('Subscriber Zip')} />
                    <FieldDate
                      name="mediCalGuarantor.subscriberDateOfBirth"
                      title={t('Subscriber DOB')}
                    />
                    <FieldSelectorPrimary
                      isFast={false}
                      name="mediCalGuarantor.subscriberGender"
                      title={t('Subscriber Gender')}
                      options={formatToFluentOptions(gender, 'description', 'code')}
                    />
                    <FieldText
                      name="mediCalGuarantor.subscriberSocialSecurityNumber"
                      title={t('Subscriber SSN')}
                    />
                    <FieldText
                      name="mediCalGuarantor.subscriberClientIndexNumber"
                      title={t('Subscriber Client Index Number')}
                    />
                    <MyField name={[FORM_LOCAL_DATA, 'UpdateExistingMediCal']}>
                      {({ field }) => (
                        <FieldDate
                          name="mediCalGuarantor.coverageEffectiveDate"
                          title={t('Coverage Effective Date')}
                          disabled={field?.value && initialValues?.mediCalGuarantor}
                        />
                      )}
                    </MyField>
                  </div>
                </ActionBox>
              )
            }
          </MyField>
          <MyField name={FORM_LOCAL_DATA}>
            {({ field }) =>
              field?.value?.['UpdateNonMediCal'] && (
                <ActionBox title={t('LA County Subscriber Information')} hasMoreButton={false}>
                  <div className={css.cmClass.grid4Columns}>
                    <FieldText
                      name="laCountyGuarantor.subscriberFirstName"
                      title={t('Subscriber First Name')}
                    />
                    <FieldText
                      name="laCountyGuarantor.subscriberLastName"
                      title={t('Subscriber Last Name')}
                    />
                    <FieldText
                      name="laCountyGuarantor.subscriberAddress"
                      title={t('Subscriber Address')}
                    />
                    <FieldText
                      name="laCountyGuarantor.subscriberAddress2"
                      title={t('Subscriber Address 2')}
                    />
                    <FieldText name="laCountyGuarantor.subscriberZip" title={t('Subscriber Zip')} />
                    <FieldDate
                      name="laCountyGuarantor.subscriberDateOfBirth"
                      title={t('Subscriber DOB')}
                    />
                    <FieldSelectorPrimary
                      isFast={false}
                      name="laCountyGuarantor.subscriberGender"
                      title={t('Subscriber Gender')}
                      options={formatToFluentOptions(gender, 'description', 'code')}
                    />
                    <FieldText
                      name="laCountyGuarantor.subscriberSocialSecurityNumber"
                      title={t('Subscriber SSN')}
                    />
                  </div>
                </ActionBox>
              )
            }
          </MyField>
          <StatusInfo />
        </Stack>
        <MyField>
          {({ form }) => (
            <Stack horizontal tokens={{ childrenGap: 16 }} className="mt-16">
              <PrimaryButton text={t('Submit to IBHIS')} onClick={() => onSubmit(form, true)} />
              <PrimaryButton
                className={css.cmBtn.secondaryButton}
                text={t('Save as Incomplete')}
                onClick={() => onSubmit(form, false)}
              />
            </Stack>
          )}
        </MyField>
      </LoadingWrapper>
    </Formik>
  );
}

export default FinancialEligibility;

const TYPES = [
  { key: 'AddNewMediCal', text: 'Add New MediCal' },
  { key: 'UpdateExistingMediCal', text: 'Update MediCal' },
  { key: 'UpdateNonMediCal', text: 'Update Non-MediCal' },
];
