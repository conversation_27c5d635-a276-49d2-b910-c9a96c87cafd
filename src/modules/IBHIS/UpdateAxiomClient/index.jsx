import React, { useContext } from 'react';
import { FontIcon, mergeStyleSets, Stack } from '@fluentui/react';
import cn from 'classnames';
import CollapseVertical from 'components/Collapse/CollapseVertical';
import { useClass, useRequest } from 'hooks';
import { t } from 'utils/string';
import UpdateAxiomClientTab from './UpdateAxiomClientTab';
import { IBHISContext } from 'contexts/IBHISContext';
import { API_IBHIS } from 'constants/urlRequest';
import { MODULE_NAME } from 'constants/routes';

function UpdateAxiomClient() {
  const css = useClass();
  const { ibhisPatientId, setEpisode } = useContext(IBHISContext);

  useRequest({
    key: [API_IBHIS.EPISODES(ibhisPatientId), true],
    url: API_IBHIS.EPISODES(ibhisPatientId),
    params: { activeOnly: true },
    requiredParams: { ibhisPatientId },
    autoRefetch: true,
    onSuccess: (data) => {
      if (data?.length) setEpisode(data[0]);
    },
  });

  return (
    <CollapseVertical
      open
      title={t('Update Episode')}
      className="mb-24"
      id={MODULE_NAME.IBHIS_UPDATE}
    >
      <Stack
        horizontal
        tokens={{ childrenGap: 16, padding: '8px 16px' }}
        verticalAlign="center"
        className={cn(css.cmClass.backgroundGrey200, 'mb-8', css.cmClass.borderRadius8)}
      >
        <FontIcon iconName="Info" className={style.blue} />
        <span>
          {t(
            'This page allows you to update the existing client information in Axiom with the latest data from IBHIS.',
          )}
          <br />
          {t(
            'This ensures that your client records are accurate and up-to-date, reflecting any changes made in the IBHIS system.',
          )}
        </span>
      </Stack>
      <UpdateAxiomClientTab />
    </CollapseVertical>
  );
}

const style = mergeStyleSets({
  blue: [{ color: '#1890FF' }],
});

export default UpdateAxiomClient;
