import React from 'react';
import { Stack } from '@fluentui/react';
import { t } from 'utils/string';
import cn from 'classnames';
import useClass from 'hooks/useClass';
import FieldInfo from 'components/Form/FieldInfo';
import moment from 'moment';
import useSetting from 'hooks/useSetting';
import { SETTING_KEYS } from 'constants/settingKeys';
import { getStatus } from 'constants/statusRender';
import FieldText from 'components/Form/FieldText';

function StatusInfo({ withComments = false }) {
  const css = useClass();
  const [dateFormat, timeFormat] = useSetting(SETTING_KEYS.DATE_FORMAT, SETTING_KEYS.TIME_FORMAT);

  return (
    <Stack
      className={cn(css.cmClass.backgroundGrey100, css.cmClass.border, css.cmClass.borderRadius4)}
      tokens={{ padding: 12, childrenGap: 36 }}
      horizontal
    >
      {/* #16223 */}
      {/* <FieldInfo name="status" title={t('Status')} formatValue={(v) => getStatus(v)} /> */}
      <FieldInfo
        name="updateDateTime"
        title={t('Last Update')}
        formatValue={(v) => (v ? moment(v).format(`${dateFormat} ${timeFormat}`) : '')}
      />
      {/* <FieldInfo name="updateUser" title={t('Updated By')} /> */}
      {withComments && <FieldText name="comments" title={t('Comments')} />}
    </Stack>
  );
}

export default StatusInfo;
