import React from 'react';
import { Stack } from '@fluentui/react';
import { t } from 'utils/string';
import { useClass } from 'hooks';
import cn from 'classnames';
import ResponsiveGrid from 'components/Layout/Responsive/Container';
import ResponsiveItem from 'components/Layout/Responsive/Item';
import FieldDate from 'components/HookForm/FieldDate';
import FieldSelectorPrimary from 'components/HookForm/FieldSelectorPrimary';
import FieldChoice from 'components/HookForm/FieldChoice';
import FieldText from 'components/HookForm/FieldText';
import FieldTime from 'components/HookForm/FieldTime';
import {
  CLOSURE_REASON_OPTIONS,
  DISPOSTION_TYPE_OPTIONS,
  EARLIER_APPT_OFFERED_TYPE_OPTIONS,
} from '../constant';
import { formatToFluentOptions } from 'utils';
import { _get } from 'utils/form';
import WatchValues from 'components/HookForm/WatchValues';
import { useFormContext } from 'react-hook-form';

const AppointmentInformation = ({ lookupData }) => {
  const css = useClass();
  const { setValue } = useFormContext();
  return (
    <Stack tokens={{ childrenGap: 16 }}>
      <Stack
        className={cn(css.cmClass.backgroundGrey200, css.cmClass.borderRadius8)}
        tokens={{ padding: '12px 16px', childrenGap: 16 }}
      >
        <div className={'weight-600'}>{t('Appointment Information')}</div>

        <ResponsiveGrid>
          <ResponsiveItem md={3}>
            <FieldSelectorPrimary
              title={t('Disposition')}
              name="disposition.dispositionType"
              options={DISPOSTION_TYPE_OPTIONS}
              required
              onChange={({ form, data }) => {
                setValue('disposition.dispositionType', data?.key);
                if (data?.key !== 'NoApptAtThisSite') {
                  setValue('disposition.dispositionChoice', null);
                } else {
                  setValue('apptPractitionerNPI', null);
                  setValue('apptProgramOfService', null);
                  setValue('apptDate', null);
                  setValue('apptTime', null);
                  setValue('closureReason', null);
                  setValue('earlierApptOfferedType', null);
                  setValue('firstOfferedApptDate', null);
                  setValue('secondOfferedApptDate', null);
                  setValue('thirdOfferedApptDate', null);
                }
              }}
            />
          </ResponsiveItem>

          <WatchValues name="disposition.dispositionType">
            {(value) =>
              value === 'NoApptAtThisSite' && (
                <ResponsiveItem md={3}>
                  <FieldSelectorPrimary
                    title={t('Disposition Choice')}
                    required
                    name="disposition.dispositionChoice"
                    options={formatToFluentOptions(lookupData?.nonAssessmentDispositionTypes)}
                  />
                </ResponsiveItem>
              )
            }
          </WatchValues>
          <ResponsiveItem md={3}>
            <FieldText title={t('Disposition Details')} name="disposition.dispositionDetails" />
          </ResponsiveItem>
          <WatchValues name="disposition.dispositionType">
            {(value) =>
              (value === 'AssessmentAppointmentGivenThisSite' ||
                value === 'UntimelyApptThisSiteReferDeclined') && (
                <ResponsiveItem md={3}>
                  <FieldSelectorPrimary
                    title={t('Closure Reason')}
                    name="closureReason"
                    required
                    options={CLOSURE_REASON_OPTIONS}
                    onChange={({ data }) => {
                      setValue('closureReason', data?.key);
                      if (data?.key !== 'BeneficiaryDidNotAcceptAnyOfferedAssessmentDates') {
                        setValue('apptPractitionerNPI', null);
                        setValue('apptProgramOfService', null);
                        setValue('apptDate', null);
                        setValue('apptTime', null);
                      }
                    }}
                  />
                </ResponsiveItem>
              )
            }
          </WatchValues>
        </ResponsiveGrid>
        <ResponsiveGrid>
          <WatchValues name="closureReason">
            {(value) =>
              (value ===
                'BeneficiaryAcceptedOfferedAssessmentDateButDidNotAttendInitialAssessmentAppointment' ||
                value === 'BeneficiaryAcceptedOfferedAssessmentDateAndAttendedAppointment') && (
                <React.Fragment>
                  <ResponsiveItem md={3}>
                    <FieldText
                      type="number"
                      title={t('Appointment Practitioner NPI')}
                      name="apptPractitionerNPI"
                      required
                    />
                  </ResponsiveItem>
                  <ResponsiveItem md={3}>
                    <FieldSelectorPrimary
                      title={t('Program of Service')}
                      name="apptProgramOfService"
                      required
                      options={formatToFluentOptions(lookupData?.medApptProgramOfServices)}
                    />
                  </ResponsiveItem>
                  <ResponsiveItem md={3}>
                    <FieldDate title={t('Appointment Date')} name="apptDate" required />
                  </ResponsiveItem>
                  <ResponsiveItem md={3}>
                    <FieldTime title={t('Appointment Time')} name="apptTime" required />
                  </ResponsiveItem>
                </React.Fragment>
              )
            }
          </WatchValues>
        </ResponsiveGrid>
        <ResponsiveGrid>
          <WatchValues name="disposition.dispositionType">
            {(value) =>
              (value === 'AssessmentAppointmentGivenThisSite' ||
                value === 'UntimelyApptThisSiteReferDeclined') && (
                <ResponsiveItem md={12}>
                  <FieldChoice
                    horizontal
                    options={EARLIER_APPT_OFFERED_TYPE_OPTIONS}
                    formatCheckedOption={(item) => item.key}
                    title={t('Was An Earlier Appointment Offered?')}
                    name="earlierApptOfferedType"
                    required
                    onChange={(option) => {
                      setValue('earlierApptOfferedType', option?.key);
                      if (option?.key !== 'Yes') {
                        setValue('firstOfferedApptDate', null);
                        setValue('secondOfferedApptDate', null);
                        setValue('thirdOfferedApptDate', null);
                      }
                    }}
                  />
                </ResponsiveItem>
              )
            }
          </WatchValues>
        </ResponsiveGrid>
        <ResponsiveGrid>
          <WatchValues name="earlierApptOfferedType">
            {(value) =>
              value === 'Yes' && (
                <ResponsiveItem md={3}>
                  <FieldDate
                    required
                    title={t('Date of First Offered Appointment')}
                    name="firstOfferedApptDate"
                    onChange={(data) => {
                      if (!data) {
                        setValue('secondOfferedApptDate', null);
                        setValue('thirdOfferedApptDate', null);
                      }
                    }}
                  />
                </ResponsiveItem>
              )
            }
          </WatchValues>
          <WatchValues name="thirdOfferedApptDate">
            {(value) =>
              !!value && (
                <ResponsiveItem md={3}>
                  <FieldDate
                    title={t('Date of Second Offered Appointment')}
                    name="secondOfferedApptDate"
                    required
                  />
                </ResponsiveItem>
              )
            }
          </WatchValues>
          <WatchValues name="earlierApptOfferedType">
            {(value) =>
              value === 'Yes' && (
                <ResponsiveItem md={3}>
                  <FieldDate
                    title={t('Date of Third Offered Appointment')}
                    name="thirdOfferedApptDate"
                    onChange={(data) => {
                      if (!data) {
                        setValue('secondOfferedApptDate', null);
                      }
                    }}
                  />
                </ResponsiveItem>
              )
            }
          </WatchValues>
        </ResponsiveGrid>
      </Stack>
    </Stack>
  );
};

export default AppointmentInformation;
