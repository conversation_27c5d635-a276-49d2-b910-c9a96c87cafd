import React, { useContext, useEffect, useMemo } from 'react';
import useClass from 'hooks/useClass';
import useRequest from 'hooks/useRequest';
import useUser from 'hooks/useUser';
import useNavigator from 'hooks/useNavigator';
import { t } from 'utils/string';
import { PrimaryButton, Stack } from '@fluentui/react';
import SubCollapse from 'components/Collapse/SubCollapse';
import FieldDate from 'components/HookForm/FieldDate';
import FieldText from 'components/HookForm/FieldText';
import FieldCheckBox from 'components/HookForm/FieldCheckbox';
import { API_EMPLOYEE_IDENTIFIER, API_IBHIS, API_METHOD, API_PATIENT } from 'constants/urlRequest';
import { formatToFluentOptions, removeUndefinedFromObject } from 'utils';
import FieldSelectorPrimary from 'components/HookForm/FieldSelectorPrimary';
import { toast } from 'react-toastify';
import { handleValidationBE } from 'utils/form';
import QUERY_KEY from 'constants/queryKey';
import LoadingWrapper from 'components/Loading/LoadingWrapper';
import { IBHISContext } from 'contexts/IBHISContext';
import { MODULE_NAME } from 'constants/routes';
import CollapseVertical from 'components/Collapse/CollapseVertical';
import FieldInfo from 'components/HookForm/FieldInfo';
import FieldTime from 'components/HookForm/FieldTime';
import { Col, Container, Row } from 'components/Layout/Grid';
import queryClient from 'apis/queryClient';
import { TEXT_TEMPLATE } from 'constants/texts';
import useHookForm from 'components/HookForm/useHookForm';
import WatchValues from 'components/HookForm/WatchValues';
import { FormProvider } from 'react-hook-form';

const today = new Date();

function ClientData() {
  const css = useClass();
  const { currentPatient, info } = useUser();
  const { data: dataIdentifier } = useRequest({
    url: API_EMPLOYEE_IDENTIFIER.GET(info.ehrUserId),
    shouldCache: true,
  });

  const employeeNPI = useMemo(
    () =>
      dataIdentifier?.items?.filter((i) => i?.description === 'NPI' && !i?.endDate)?.[0]?.idValue,
    [dataIdentifier],
  );
  const defaultInitialValues = useMemo(() => {
    return {
      patientId: currentPatient.patientId,
      isMedicalClient: false,
      admissionDate: today,
      admittingStaffNPI: employeeNPI,
    };
  }, [currentPatient, employeeNPI]);

  const methods = useHookForm({
    defaultValues: defaultInitialValues,
    dependencies: {
      patientId: currentPatient.patientId,
      admittingStaffNPI: employeeNPI,
    },
  });

  const { ibhisPatientId, setIbhisPatientId } = useContext(IBHISContext);
  const { navigate } = useNavigator();

  const apiEpisode = useRequest({
    key: ['ibhis-episodes', 'active'],
    url: API_IBHIS.EPISODES(ibhisPatientId),
    requiredParams: { ibhisPatientId, activeOnly: true },
    autoRefetch: true,
    saveHistory: false,
  });

  const {
    data: patientData,
    loading: patientLoading,
    refetch: patientRefetch,
  } = useRequest({
    key: `${QUERY_KEY.DEMOGRAPHIC}-${ibhisPatientId}`,
    url: API_IBHIS.DEMOGRAPHIC,
    requiredParams: { ibhisPatientId, isFromIbhis: true },
    shouldCache: true,
    saveHistory: false,
  });

  const { data: lookup } = useRequest({ url: API_IBHIS.ADMIT_LOOKUP, shouldCache: true });

  const apiLookup = useRequest({ url: API_IBHIS.CSI_LOOKUP, shouldCache: true });

  const {
    data: csiData,
    loading: csiLoading,
    updateParams: csiUpdateParams,
  } = useRequest({
    key: `${QUERY_KEY.IBHIS_CSI}-${ibhisPatientId}`,
    url: API_IBHIS.CSI,
    requiredParams: { ibhisPatientId },
    autoRefetch: true,
    shouldCache: true,
  });

  const { data: languages } = useRequest({
    key: [API_IBHIS.DICTIONARY, 'Language'],
    url: API_IBHIS.DICTIONARY,
    params: { lookupType: 'Language' },
    shouldCache: true,
  });

  const apiSave = useRequest({
    url: API_IBHIS.DEMOGRAPHIC,
    method: API_METHOD.PUT,
  });

  const { request, loading } = useRequest({
    url: API_IBHIS.ADMIT_CLIENT,
    method: API_METHOD.POST,
    showAdditionalData: true,
  });

  const onSubmit = (values) => {
    const payload = {
      ...values,
      isNonMedicalClient: !values?.isMedicalClient,
      patientId: ibhisPatientId ? undefined : values?.patientId,
    };

    if (payload.ibhisPatientId) {
      if (!apiEpisode?.data?.length) {
        toast.error(t('Please create a new episode before editing client data to IBHIS'));
        return;
      }

      apiSave.request({
        payload: removeUndefinedFromObject(values, true),
        options: {
          onSuccess: () => {
            patientRefetch();
            csiUpdateParams({ isFromIbhis: true });
            toast.success(TEXT_TEMPLATE.SAVE_SUCCESSFULLY('Client Data'));
          },
          onError: (err) => handleValidationBE({ setError: methods.setError }, err),
        },
      });
    } else {
      request({
        payload: removeUndefinedFromObject(payload, true),
        options: {
          onSuccess: async ({ ibhisClientId }) => {
            toast.success(t('Admitted client successfully'));
            setIbhisPatientId(ibhisClientId);
            queryClient.invalidateQueries([API_PATIENT.DEFAULT, currentPatient.patientId]);
            navigate({ hash: MODULE_NAME.IBHIS_SEARCH });
          },
          onError: (err) => handleValidationBE({ setError: methods.setError }, err),
        },
      });
    }
  };
  useEffect(() => {
    if (patientData && ibhisPatientId && csiData && apiEpisode?.data?.length) {
      methods.reset({
        ibhisPatientId,
        patientId: currentPatient.patientId,
        episodeID: apiEpisode?.data?.[0]?.episodeID,
        ...csiData,
        ...patientData.client,
        ...patientData.clientSmokingAssessment,
        subjectId: patientData.subjectId,
        status: undefined,
        clientsHomePhone: patientData.clientLivingArrangements.clientsHomePhone?.replaceAll(
          ' ',
          '',
        ),
        streetAddress1: patientData.clientLivingArrangements.streetAddress1,
        zipCode: patientData.clientLivingArrangements.zipCode,
      });
    } else {
      methods.reset(defaultInitialValues);
    }
  }, [patientData, ibhisPatientId, csiData, apiEpisode?.data]);

  return (
    <FormProvider {...methods}>
      <CollapseVertical open title={t('Client Data')} className="mb-24">
        <LoadingWrapper
          loading={
            loading || apiSave?.loading || patientLoading || csiLoading || apiEpisode?.loading
          }
        >
          <Stack tokens={{ childrenGap: 16 }}>
            {!patientData && (
              <React.Fragment>
                <SubCollapse title={t('Admission Details')} open>
                  <div className={css.cmClass.grid4Columns}>
                    <FieldDate name="admissionDate" title={t('Admission Date')} required />
                    <FieldTime name="admissionTime" title={t('Admission Time')} required />
                    <FieldSelectorPrimary
                      name="typeOfAdmission"
                      title={t('Type Of Admission')}
                      required
                      options={formatToFluentOptions(lookup?.admissionTypeOfAdmission)}
                    />
                    <FieldSelectorPrimary
                      name="sourceOfAdmission"
                      title={t('Source Of Admission')}
                      options={formatToFluentOptions(lookup?.sourceOfAdmissionType)}
                    />
                    <FieldSelectorPrimary
                      name="programOfAdmission"
                      title={t('Program Of Admission')}
                      options={formatToFluentOptions(lookup?.programOfAdmissions)}
                    />
                    <FieldText
                      name="admittingStaffNPI"
                      title={t('Admitting Staff NPI')}
                      fieldClassName="flex-1"
                      required
                    />
                    <FieldCheckBox name="isMedicalClient" title={t('Medi-Cal Client')} />
                  </div>
                </SubCollapse>
                <WatchValues name="isMedicalClient">
                  {(value) =>
                    !!value && (
                      <SubCollapse title={t('Financial Eligibility')} open>
                        <div className={css.cmClass.grid4Columns}>
                          <FieldDate
                            name="coverageEffectiveDate"
                            title={t('Coverage Effective Date')}
                          />
                          <FieldText
                            name="subscriberClientIndexNumber"
                            title={t('Subscriber Client Index Number')}
                          />
                          <FieldText name="subscriberAddress" title={t('Subscriber Address')} />
                          <FieldText
                            name="subscriberAddress2"
                            title={t('Subscriber Additional Address')}
                          />
                          <FieldText name="subscriberZip" title={t('Subscriber Zip Code')} />
                          <FieldSelectorPrimary
                            name="subscriberGender"
                            title={t('Subscriber Gender')}
                            options={formatToFluentOptions(lookup?.subscriberGenderType)}
                          />
                        </div>
                      </SubCollapse>
                    )
                  }
                </WatchValues>
              </React.Fragment>
            )}
            <SubCollapse title={t('Client Demographics')} open>
              <div className={css.cmClass.grid4Columns}>
                {!!patientData && <FieldInfo name="ibhisPatientId" title={t('IBHIS Patient ID')} />}
                <FieldSelectorPrimary
                  name="clientPrefix"
                  title={t('Prefix')}
                  options={formatToFluentOptions(lookup?.clientPrefix)}
                />
                <FieldText name="clientFirstName" title={t('First Name')} required />
                <FieldText name="clientLastName" title={t('Last Name')} required />
                <FieldText name="clientMiddleInitial" title={t('Middle Initial')} />
                <FieldSelectorPrimary
                  name="clientSuffix"
                  title={t('Suffix')}
                  options={formatToFluentOptions(lookup?.clientSuffix)}
                />
                <FieldSelectorPrimary
                  name="gender"
                  title={t('Gender')}
                  options={formatToFluentOptions(lookup?.subscriberGenderType)}
                  required
                />
                <FieldSelectorPrimary
                  name="genderIdentity"
                  title={t('Gender Identity')}
                  options={formatToFluentOptions(lookup?.genderIdentityType)}
                />
                <FieldSelectorPrimary
                  name="sexualOrientation"
                  title={t('Sexual Orientation')}
                  options={formatToFluentOptions(lookup?.sexualOrientationType)}
                />
                <FieldDate name="dateOfBirth" title={t('DOB')} isRenderRightSide={false} required />
                <FieldText name="socialSecurityNumber" title={t('SSN')} required />
                <FieldText name="clientsHomePhone" title={t('Home Phone')} />
                <FieldSelectorPrimary
                  name="ethnicity"
                  title={t('Ethnicity')}
                  options={formatToFluentOptions(lookup?.ethnicity)}
                  required
                />
                <FieldSelectorPrimary
                  name="clientOtherRace"
                  title={t('Race')}
                  options={formatToFluentOptions(lookup?.races)}
                  multiSelect
                  formatData={(i) => i.key}
                  required
                />
                <FieldSelectorPrimary
                  name="maritalStatus"
                  title={t('Marital Status')}
                  options={formatToFluentOptions(lookup?.maritalStatus)}
                  required
                />
                <FieldSelectorPrimary
                  name="primaryLanguage"
                  title={t('Primary Language')}
                  options={formatToFluentOptions(languages, 'description', 'code')}
                  required
                />
                <FieldSelectorPrimary
                  name="education"
                  title={t('Education')}
                  options={formatToFluentOptions(lookup?.education)}
                  required
                />
                <FieldSelectorPrimary
                  name="employmentStatus"
                  title={t('Employment Status')}
                  options={formatToFluentOptions(lookup?.employmentStatus)}
                  required
                />
                <FieldSelectorPrimary
                  name="smokingAssessment"
                  title={t('Smoking Assessment')}
                  options={formatToFluentOptions(lookup?.smokingAssessment)}
                />
                {/* <MyField name="smokingAssessment">
                {({ field }) =>
                  !!field?.value && (
                    <FieldDate name="smokingAssessmentDate" title={t('Smoking Assessment Date')} />
                  )
                }
              </MyField> */}
              </div>
              <Container className="mt-16">
                <Row>
                  <Col md={3}>
                    <FieldText name="streetAddress1" title={t('Address')} required />
                  </Col>
                  <Col md={3}>
                    <FieldText
                      name="zipCode"
                      title={t('Zip Code (9-Digit)')}
                      required
                      helpText={t('Must be in the format XXXXX-XXXX. Example: 12345-6789')}
                    />
                  </Col>
                  {!ibhisPatientId && (
                    <Col md={6}>
                      <FieldSelectorPrimary
                        name="livingArrangements"
                        title={t('Living Arrangements')}
                        options={formatToFluentOptions(lookup?.livingArrangement)}
                        fieldClassName="flex-1"
                        required
                      />
                    </Col>
                  )}
                </Row>
              </Container>
            </SubCollapse>
            {!!patientData && (
              <SubCollapse title={t('CSI')} open>
                <div className={css.cmClass.grid3Columns}>
                  <FieldText title={t('Birth First Name')} name="birthFirstName" required />
                  <FieldText title={t('Birth Last Name')} name="birthLastName" />
                  <FieldText title={t('Birth Middle Name')} name="birthMiddleName" />
                  <FieldText title={t('Mothers First Name')} name="mothersFirstName" />
                  <FieldSelectorPrimary
                    title={t('Place Of Birth County')}
                    name="placeOfBirthCounty"
                    options={formatToFluentOptions(apiLookup?.data?.placeOfBirthCounty)}
                  />
                  <FieldSelectorPrimary
                    title={t('Place Of Birth State')}
                    name="placeOfBirthState"
                    required
                    options={formatToFluentOptions(apiLookup?.data?.placeOfBirthState)}
                  />
                  <FieldSelectorPrimary
                    title={t('Place Of Birth Country')}
                    name="placeOfBirthCountry"
                    required
                    options={formatToFluentOptions(apiLookup?.data?.placeOfBirthCountry)}
                  />
                  <FieldSelectorPrimary
                    title={t('CSI Ethnicity')}
                    name="csiEthnicity"
                    options={formatToFluentOptions(apiLookup?.data?.csiEthnicity)}
                    required
                  />
                  <FieldSelectorPrimary
                    title={t('CSI Race')}
                    name="csiRaces"
                    multiSelect
                    options={formatToFluentOptions(apiLookup?.data?.csiRace)}
                    formatData={(i) => i.key}
                    required
                  />
                  <FieldSelectorPrimary
                    title={t('Preferred Language')}
                    name="preferredLanguage"
                    options={formatToFluentOptions(apiLookup?.data?.preferredLanguage)}
                    required
                  />
                  <FieldSelectorPrimary
                    title={t('Fiscally Responsible County For Client')}
                    name="fiscallyResponsibleCountyForClient"
                    options={formatToFluentOptions(
                      apiLookup?.data?.fiscallyResponsibleCountyForClient,
                    )}
                    required
                  />
                  <FieldSelectorPrimary
                    title={t('Admission Necessity Code')}
                    name="admissionNecessityCode"
                    options={formatToFluentOptions(apiLookup?.data?.admissionNecessityCode)}
                  />
                  <FieldSelectorPrimary
                    title={t('Conservatorship Court Status')}
                    name="conservatorShipCourtStatus"
                    options={formatToFluentOptions(apiLookup?.data?.conservatorshipCourtStatus)}
                  />
                  <FieldSelectorPrimary
                    title={t('Special Population')}
                    name="specialPopulation"
                    options={formatToFluentOptions(apiLookup?.data?.specialPopulation)}
                  />
                  <FieldSelectorPrimary
                    title={t('Legal Class')}
                    name="legalClass"
                    options={formatToFluentOptions(apiLookup?.data?.legalClass)}
                  />
                  <FieldSelectorPrimary
                    title={t('County School')}
                    name="countySchool"
                    options={formatToFluentOptions(apiLookup?.data?.countySchool)}
                  />
                  <FieldText
                    title={t('Number Of Dependents Less Than 18YO')}
                    name="numberOfDependentsLessThan18YO"
                    type="number"
                  />
                  <FieldText
                    title={t('Number Of Dependents Over 18YO')}
                    name="numberOfDependentsOver18YO"
                    type="number"
                  />
                </div>
              </SubCollapse>
            )}

            <Stack horizontal tokens={{ childrenGap: 12 }}>
              <PrimaryButton text={t('Submit to IBHIS')} onClick={methods.handleSubmit(onSubmit)} />
              {/* <DefaultButton
                    text={t('Sync to Axiom')}
                    onClick={() => {
                      showModal({
                        content: (
                          <SyncDemographicModal
                            ibhisPatientId={ibhisPatientId}
                            patientId={currentPatient.patientId}
                          />
                        ),
                        size: MODAL_SIZE.LARGE,
                      });
                    }}
                  /> */}
            </Stack>
          </Stack>
        </LoadingWrapper>
      </CollapseVertical>
    </FormProvider>
  );
}

export default ClientData;
