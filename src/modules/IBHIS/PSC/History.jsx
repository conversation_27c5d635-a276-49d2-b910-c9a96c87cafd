import React, { useContext, useEffect, useState } from 'react';
import { t } from 'utils/string';
import { PrimaryButton, SearchBox, Stack } from '@fluentui/react';
import useRequest from 'hooks/useRequest';
import useModal from 'hooks/useModal';
import useClass from 'hooks/useClass';
import useSetting from 'hooks/useSetting';
import CollapseVertical from 'components/Collapse/CollapseVertical';
import Table from 'components/Table';
import { API_IBHIS, API_METHOD } from 'constants/urlRequest';
import { IBHISContext } from 'contexts/IBHISContext';
import ModalConfirm from 'components/Modal/ModalConfirm';
import { TEXT_TEMPLATE } from 'constants/texts';
import { toast } from 'react-toastify';
import { MODAL_SIZE } from 'constants/modal';
import { scrollIntoViewId } from 'utils/globalScroll';
import ELEMENT_ID from 'constants/elementId';
import queryClient from 'apis/queryClient';
import { HistoryEmpty } from '../CANS/CANSHistory';
import { getStatus } from 'constants/statusRender';
import ModalViewItem from 'components/Modal/ModalView/ModalViewItem';
import { parseFormatDateBE } from 'utils/time';
import { SETTING_KEYS } from 'constants/settingKeys';

function PSCHistory({ setPSCSubmission }) {
  const { showModal } = useModal();
  const css = useClass();
  const { ibhisPatientId } = useContext(IBHISContext);
  const [searchValue, setSearchValue] = useState();
  const [DATE_FORMAT] = useSetting(SETTING_KEYS.DATE_FORMAT);

  const { data, loading, updateParams, refetch } = useRequest({
    key: API_IBHIS.PSC_HISTORY,
    url: API_IBHIS.PSC_HISTORY,
    params: { ibhisPatientId },
    enabled: !!ibhisPatientId,
    autoRefetch: true,
  });

  useEffect(() => {
    if (ibhisPatientId) {
      updateParams({ ibhisPatientId });
    } else {
      queryClient.setQueryData([API_IBHIS.PSC_HISTORY], []);
    }
  }, [ibhisPatientId]);

  const onSearch = () => {
    updateParams({ ibhisPatientId: searchValue });
  };

  const { request: requestDelete, loading: deleting } = useRequest({
    url: API_IBHIS.PSC(''),
    method: API_METHOD.DELETE,
  });

  const getMenu = (item) => ({
    items: [
      {
        key: 'edit',
        text: 'Edit',
        iconProps: { iconName: 'EditNote' },
        onClick: (item) => {
          scrollIntoViewId(ELEMENT_ID.IBHIS.PSC_INPUT, 'start');
          setPSCSubmission(item);
        },
      },
      {
        key: 'view',
        text: 'View',
        iconProps: { iconName: 'ViewList' },
        onClick: () =>
          showModal({
            content: (
              <ModalViewItem
                url={API_IBHIS.PSC_PRINT(item.ibhisEpsdtPscId)}
                title={t('PSC')}
                itemKey={`PSC-${item.ibhisEpsdtPscId}`}
              />
            ),
          }),
      },
      {
        key: 'print',
        text: 'Print',
        iconProps: { iconName: 'Print' },
        onClick: async () => {
          onPrint({
            url: API_IBHIS.PSC_PRINT(item.ibhisEpsdtPscId),
            queryString: {
              dateFormatString: parseFormatDateBE(DATE_FORMAT),
            },
          });
        },
      },

      {
        key: 'delete',
        text: 'Delete',
        iconProps: { iconName: 'Delete' },
        onClick: (item) => {
          showModal({
            content: (
              <ModalConfirm
                message={t(TEXT_TEMPLATE.DELETE_CONFIRMATION('PSC'))}
                onOk={() =>
                  requestDelete({
                    url: API_IBHIS.PSC(item?.ibhisEpsdtPscId),
                    options: {
                      onSuccess: () => {
                        toast.success(t(TEXT_TEMPLATE.DELETE_SUCCESSFULLY('PSC')));
                        refetch();
                      },
                    },
                  })
                }
              />
            ),
            size: MODAL_SIZE.X_SMALL,
          });
        },
      },
    ],
    shouldFocusOnMount: false,
  });

  if (!ibhisPatientId) return <HistoryEmpty form="PSC" />;

  return (
    <CollapseVertical open title={t('PSC History')} className="mb-24">
      <Stack tokens={{ childrenGap: 16 }}>
        <Stack horizontal tokens={{ childrenGap: 16 }}>
          <SearchBox
            iconProps={{ iconName: 'Zoom' }}
            placeholder={t('Ibhis Patient Id')}
            onChange={(_, value) => setSearchValue(value)}
            value={searchValue}
            onSearch={onSearch}
            className={css.cmClass.minWidth300}
          />
          <PrimaryButton text={t('Search')} onClick={onSearch} iconProps={{ iconName: 'Zoom' }} />
          <PrimaryButton
            text={t('New PSC Assessment')}
            iconProps={{ iconName: 'Add' }}
            onClick={() => scrollIntoViewId(ELEMENT_ID.IBHIS.PSC_INPUT, 'start')}
            disabled={!ibhisPatientId}
          />
        </Stack>
        <Table
          loading={loading || deleting}
          columns={_columns}
          items={data}
          pagination={false}
          getMenuProps={getMenu}
          refetch={refetch}
        />
      </Stack>
    </CollapseVertical>
  );
}

export default PSCHistory;

const _columns = [
  { name: 'Id', fieldName: 'ibhisEpsdtPscId' },
  {
    name: 'Assessment Date',
    fieldName: 'assessmentDate',
    sortable: true,
    renderItem: (i, _, c, render) => render({ date: i?.pscAdministrativeData?.assessment?.date }),
  },
  {
    name: 'Assessment Type',
    fieldName: 'assessmentType',
    sortable: true,
    renderItem: (i) => (
      <AssessmentType assessmentType={i?.pscAdministrativeData?.assessment?.type} />
    ),
  },
  { name: 'Status', fieldName: 'status', sortable: true, renderItem: (i) => getStatus(i.status) },
  {
    name: 'Last Update',
    fieldName: 'updateUser',
    sortable: true,
    renderItem: (i, _, c, render) =>
      render({ date: i?.updateDateTime, withTime: true, user: i?.updateUser }),
  },
  { name: 'Comments', fieldName: 'comments', sortable: true },
  { name: 'Submission Id', fieldName: 'submissionId' },
  { key: 'Action', name: 'Action', fieldName: 'Action' },
];

const AssessmentType = ({ assessmentType }) => {
  const { data: lookups } = useRequest({ url: API_IBHIS.PSC_LOOKUP, shouldCache: true });

  return lookups?.assessmentTypes?.find((item) => item.key === assessmentType)?.description;
};
