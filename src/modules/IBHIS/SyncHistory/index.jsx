import React, { use<PERSON>allback, useContext, useEffect } from 'react';
import CollapseVertical from 'components/Collapse/CollapseVertical';
import { t } from 'utils/string';
import useClass from 'hooks/useClass';
import useRequest from 'hooks/useRequest';
import useSetting from 'hooks/useSetting';
import useModal from 'hooks/useModal';
import { DEFAULT_PAGINATION_PARAMS, SECURITY_TYPE } from 'constants/index';
import { DatePicker, Link, SearchBox, Stack } from '@fluentui/react';
import { debounceFunc, getDateRange } from 'utils/time';
import { API_IBHIS } from 'constants/urlRequest';
import { getStatus } from 'constants/statusRender';
import { SETTING_KEYS } from 'constants/settingKeys';
import moment from 'moment';
import { MODAL_SIZE } from 'constants/modal';
import ModalLayout from 'components/GlobalModal/ModalLayout';
import beautify from 'xml-beautifier';
import Table from 'components/Table';
import HorizontalRule from 'components/HorizontalRule';
import withSecurity from 'HOCs/withSecurity';
import { SECURITY_FUNCTIONS } from 'constants/modules';
import { IBHISContext } from 'contexts/IBHISContext';
import useDidUpdateEffect from 'hooks/useDidUpdateEffect';
import queryClient from 'apis/queryClient';

function SyncHistory() {
  const css = useClass();
  const [dateFormat] = useSetting(SETTING_KEYS.DATE_FORMAT);
  const { showModal } = useModal();
  const { ibhisPatientId } = useContext(IBHISContext);

  const { data, loading, updateParams, params, refetch } = useRequest({
    key: API_IBHIS.MESSAGE,
    url: API_IBHIS.MESSAGE,
    params: {
      ...DEFAULT_PAGINATION_PARAMS,
      searchTerm: ibhisPatientId,
      OrderBy: 'requestMessageDateTime',
      messageFromDateTime: moment().startOf('month').toISOString(),
      messageToDateTime: moment().endOf('month').toISOString(),
    },
    enabled: !!ibhisPatientId,
    autoRefetch: true,
  });

  useDidUpdateEffect(() => {
    if (ibhisPatientId) {
      updateParams({ searchTerm: ibhisPatientId, Page: 0 });
    } else {
      queryClient.setQueryData([API_IBHIS.MESSAGE], []);
    }
  }, [ibhisPatientId]);

  const getMenu = (item) => ({
    items: [
      { key: 'delay', text: 'Delay Message', disabled: true, onClick: () => {} },
      { key: 'delete', text: 'Mark as Delete', disabled: true, onClick: () => {} },
      { key: 'resubmission', text: 'Mark for Resubmission', disabled: true, onClick: () => {} },
      { key: 'pend', text: 'Pend for review', disabled: true, onClick: () => {} },
    ],
    shouldFocusOnMount: false,
  });

  const onViewBodyDetail = (id, title) => {
    showModal({
      content: <ModalDetail messageId={id} title={title} />,
      size: MODAL_SIZE.SMALL,
    });
  };

  const onSearch = useCallback(
    debounceFunc((searchTerm) => updateParams({ searchTerm, Page: 0 }), 500),
    [],
  );

  const SearchClient = withSecurity(
    SearchBox,
    SECURITY_TYPE.VIEW,
    SECURITY_FUNCTIONS.IBHIS_SYNC_HISTORY_ADMIN,
  );

  return (
    <React.Fragment>
      <CollapseVertical open title={t('IBHIS Sync History')} className="mb-24">
        <Stack tokens={{ childrenGap: 16 }}>
          <div className={css.cmClass.grid4Columns}>
            <SearchClient
              className="w-100"
              placeholder={t('Search IBHIS Client ID ')}
              iconProps={{ iconName: 'Zoom' }}
              styles={{ root: { alignSelf: 'flex-end' } }}
              onChange={(_, newValue) => onSearch(newValue)}
              defaultValue={ibhisPatientId}
            />
            <DatePicker
              label={t('From Date')}
              formatDate={(date) => moment(date).format(dateFormat)}
              placeholder={dateFormat}
              value={params?.messageFromDateTime ? new Date(params.messageFromDateTime) : ''}
              onSelectDate={(date) =>
                updateParams({
                  messageFromDateTime: date ? getDateRange(date)[0] : undefined,
                  Page: 0,
                })
              }
              maxDate={new Date(params.messageToDateTime)}
              styles={datePickerStyle}
            />
            <DatePicker
              label={t('To Date')}
              formatDate={(date) => moment(date).format(dateFormat)}
              placeholder={dateFormat}
              value={params?.messageToDateTime ? new Date(params.messageToDateTime) : ''}
              onSelectDate={(date) =>
                updateParams({
                  messageToDateTime: date ? getDateRange(date)[1] : undefined,
                  Page: 0,
                })
              }
              minDate={new Date(params.messageFromDateTime)}
              styles={datePickerStyle}
            />
          </div>
          <Table
            columns={columns(onViewBodyDetail)}
            items={data?.items || []}
            loading={loading}
            getMenuProps={getMenu}
            totalItems={data?.totalItems}
            metadata={params}
            onMetadataChange={updateParams}
            refetch={refetch}
          />
        </Stack>
      </CollapseVertical>
      {/* #15906 temporary disable */}
      {/* <SyncAxiomHistory /> */}
    </React.Fragment>
  );
}

export default SyncHistory;

const datePickerStyle = { statusMessage: { margin: 0 } };

const columns = (onViewBodyDetail) => [
  {
    name: 'Timestamp',
    fieldName: 'requestMessageDateTime',
    sortable: true,
    renderItem: (i, _, c, render) => render({ date: i?.requestMessageDateTime, withTime: true }),
  },
  { name: 'Operation', fieldName: 'operationTypeKey', sortable: true },
  { name: 'Receiver IBHIS ID', fieldName: 'ibhisClientId', sortable: true },
  { name: 'Request User', fieldName: 'requestUser', sortable: true },
  // {
  //   name: 'Sent Date',
  //   fieldName: 'requestSentDateTime',
  //   sortable: true,
  //   renderItem: (i, _, c, render) => render({ date: i?.requestSentDateTime, withTime: true }),
  // },
  {
    name: 'Status',
    fieldName: 'requestMessageStatus',
    sortable: true,
    renderItem: (i) => getStatus(i?.requestMessageStatus === 'SUCCESS' ? 'Sent' : 'Failed'),
  },
  {
    name: 'Request Body',
    fieldName: 'requestMessageId',
    renderItem: (i) =>
      !!i?.requestMessageId && (
        <Link onClick={() => onViewBodyDetail(i?.requestMessageId, 'Request Body')}>
          {t('View Detail')}
        </Link>
      ),
  },
  {
    name: 'Response Body',
    fieldName: 'responseMessageId',
    renderItem: (i) =>
      !!i?.responseMessageId && (
        <Link onClick={() => onViewBodyDetail(i?.responseMessageId, 'Response Body')}>
          {t('View Detail')}
        </Link>
      ),
  },
  // { key: 'Action', name: 'Action', fieldName: 'Action' },
];

const ModalDetail = ({ messageId, title }) => {
  const [dateFormat, timeFormat] = useSetting(SETTING_KEYS.DATE_FORMAT, SETTING_KEYS.TIME_FORMAT);

  const { data, loading } = useRequest({
    key: [API_IBHIS.MESSAGE_CONTENT, messageId],
    url: API_IBHIS.MESSAGE_CONTENT,
    requiredParams: { messageId },
    shouldCache: true,
  });

  return (
    <ModalLayout title={t(title)} loading={loading}>
      <div className="p-16">
        {!!data?.messageContent && <pre>{beautify(data?.messageContent)}</pre>}
        <HorizontalRule mt={24} mb={16} />
        <section>{t('Actions:')}</section>
        <ul>
          {data?.actions?.map((i) => (
            <li key={i?.createDateTime}>
              {i?.action} - {moment(i?.createDateTime).format(`${dateFormat} ${timeFormat}`)}
              {t(' by ')}
              {i?.createUser}
            </li>
          ))}
        </ul>
      </div>
    </ModalLayout>
  );
};
