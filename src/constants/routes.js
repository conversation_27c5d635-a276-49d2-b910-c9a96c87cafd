import React from 'react';
import { t } from 'utils/string';

export const MODULE_NAME = {
  APPOINTMENT_HISTORY: 'AppointmentHistory',
  APPOINTMENT_BY_SITE: 'AppointmentBySite',
  TIME_BLOCKED_HISTORY: 'TimeBlockedHistory',
  AIMS: 'AIMS',

  //Dynamic form
  DF_RECENT_FORMS: 'DynamicFormRecentForms',
  DF_HISTORY: 'DynamicFormHistory',
  DF_VIEWER: 'DynamicFormViewer',

  ASAM_HISTORY: 'ASAMHistory',
  ASAM_INPUT: 'ASAMInput',
  ASSESSMENT_HISTORY: 'AssessmentHistory',
  ASSESSMENT_INPUT: 'AssessmentInput',

  CRISIS_HISTORY: 'CrisisHistory',
  CRISIS_CHILD_HISTORY: 'CrisisChildHistory',
  CRISIS_INPUT: 'CrisisInput',
  CRISIS_PREVENTION_HISTORY: 'CrisisPreventionHistory',
  CRISIS_PREVENTION_INPUT: 'CrisisPreventionInput',

  SNCD_HISTORY: 'SNCDHistory',
  SNCD_INPUT: 'SNCDInput',

  SUPPORT_PLAN_HISTORY: 'SupportPlanHistory',
  SUPPORT_PLAN_INPUT: 'SupportPlanInput',

  DASHBOARD_COMPLIANCE: 'DashboardCompliance',
  DASHBOARD_ENROLLMENT: 'DashboardEnrollment',

  DYNAMIC_SERVICE_PLAN_HISTORY: 'DynamicServicePlanHistory',
  DYNAMIC_SERVICE_PLAN_VIEWER: 'DynamicServicePlanViewer',
  // DYNAMIC_SERVICE_PLAN_AUTO_SAVE: 'DynamicServicePlanAutoSave',

  PROGRESS_NOTE_PSYCH: 'ProgressNotePsych',
  PROGRESS_NOTE_PCP: 'ProgressNotePCP',
  PROGRESS_NOTE_CM: 'ProgressNoteCM',
  PROGRESS_NOTE_AUTO_SAVE: 'ProgressNoteAutoSave',
  PROGRESS_NOTE_HISTORY: 'ProgressNoteHistory',

  DYNAMIC_PROGRESS_NOTE_SELECTION: 'DynamicProgressNoteSelection',
  DYNAMIC_PROGRESS_NOTE_HISTORY: 'DynamicProgressNoteHistory',
  DYNAMIC_PROGRESS_NOTE_VIEWER: 'DynamicProgressNoteViewer',
  DYNAMIC_PROGRESS_NOTE_VIEWER_HISTORY: 'DynamicProgressNoteViewerHistory',
  DYNAMIC_PROGRESS_NOTE_VIEWER_AUTO_SAVE: 'DynamicProgressNoteViewerAutoSave',
  CM3_CONTACT: 'CM3Contacts',
  CM3_APPOINTMENT: 'CM3Appointments',
  CM3_PRESCIPTION: 'CM3Prescriptions',
  CM3_GROUP: 'CM3Groups',
  CM3_DEMOGRAPHIC: 'CM3Demographics',
  PROGRESS_NOTE_GROUP: 'GroupNote',

  RPA_RECORD: 'RPARecord',
  RPA_HISTORY: 'RPAHistory',

  SDOH_HISTORY: 'SDOHHistory',
  SDOH_INPUT: 'SDOHInput',

  LAB_HISTORY: 'LabHistory',
  LAB_INPUT: 'LabInput',

  EMAR_HISTORY: 'EmarHistory',
  EMAR_INPUT: 'EmarInput',

  SERVICE_PLAN_HISTORY: 'ServicePlanHistory',
  SERVICE_PLAN_INPUT: 'ServicePlanInput',
  SERVICE_PLAN_AUTO_SAVE: 'ServicePlanAutoSave',

  DYNAMIC_REPORT_HISTORY: 'DynamicReportHistory',
  DYNAMIC_REPORT_FORM: 'DynamicReportForm',
  RECENT_REPORTS: 'RecentReports',
  REPORT_MODULE: 'ReportModule',

  INPATIENT_HISTORY: 'InpatientHistory',

  PAYMENT_HISTORY: 'PaymentHistory',
  PAYMENT_INPUT: 'PaymentInput',

  BATCH_837: 'batch837',
  BATCH_834: 'Batch834',
  ENCOUNTERS: 'encounters',
  RECONCILE_277: 'reconcile277',
  RECONCILE_835: 'reconcile835',
  FEE_SCHEDULE: 'feeSchedule',
  PAYOR_ASSIGNMENT: 'payorAssignment',
  PROVIDERS: 'providers',
  REMIT_EOB: 'remitEob',
  RCM_REPORT: 'RCMReport',
  REPORTING_UPLOAD: 'reportingUpload',
  RENDERING_PROVIDERS: 'renderingProviders',
  PAYOR_EDITOR: 'payorEditor',

  COMMUNICATIONS_CENTER: 'communicationsCenter',

  CURRENT_RX: 'CurrentRx',
  EXPIRED_RX: 'ExpiredRx',
  OTHER_AGENCY_RX: 'OtherAgencyRx',
  TIMELINE_RX: 'TimelineRx',
  RX_HISTORY: 'RxHistory',
  MEDICATION_RECONCILIATION: 'MedicationReconciliation',

  METRIC_PERFORMANCE: 'metricPerformance',
  CLINICAL_ANALYTIC: 'clinicalAnalytic',
  HEALTH_POPULATION_ANALYTIC: 'healthPopulationAnalytic',
  AXIOM_INSIGHT: 'insights',
  FINANCIAL_ANALYTIC: 'financialAnalytic',

  PCP_COMMUNICATION_HISTORY: 'PcpCommunicationHistory',
  PCP_COMMUNICATION_INPUT: 'PcpCommunicationInput',
  PCP_COMMUNICATION_DIAGNOSIS: 'PcpCommunicationDiagnosis',
  PCP_COMMUNICATION_MEDICATIONS: 'PCPCommunicationMedications',
  PCP_COMMUNICATION_HEADER: 'PcpCommunicationHeader',

  QUICK_ENROLLMENT_INPUT: 'QuickEnrollmentForm',
  QUICK_ENROLLMENT_SEARCH: 'QuickEnrollmentSearch',
  QUICK_ENROLLMENT_FORM_EDIT: 'QuickEnrollmentFormEdit',

  HR_ENROLLMENT_SEARCH: 'HREnrollmentSearch',
  HR_ENROLLMENT_FORM: 'HREnrollmentForm',

  PATIENT_ASSIGNMENT_GROUP: 'patientAssignmentGroup',
  PATIENT_ASSIGNMENT_PHARMACY: 'patientAssignmentPharmacy',
  PATIENT_ASSIGNMENT_PRIORITY: 'patientAssignmentPriority',
  PATIENT_ASSIGNMENT_SITE: 'patientAssignmentSite',
  PATIENT_ASSIGNMENT_TEAM: 'patientAssignmentTeam',

  COVER_SHEET_PATIENT: 'coversheet-patient',
  COVER_SHEET_ADDRESS: 'coversheet-address',
  COVER_SHEET_CONTACT: 'coversheet-contact',
  COVER_SHEET_ADDITIONAL_INFO: 'coversheet-additional-info',
  COVER_SHEET_CONSENT: 'coversheet-consent',
  COVER_SHEET_PATIENT_PORTAL: 'coversheet-patient-portal',
  COVER_SHEET_INSURANCE: 'coversheet-insurance',

  CYBHI_CLIENT_DATA: 'cybhi-client-data',

  PROBLEM_LIST_HISTORY: 'problemListHistory',
  PROBLEM_INPUT: 'problemInput',
  BEHAVIORAL_DIAGNOSIS_HISTORY: 'behavioralDiagnosisHistory',

  SECURITY_ROLES_MANAGEMENT: 'securityRolesManagement',
  SECURITY_USER_ROLES_MANAGEMENT: 'securityUserRolesManagement',
  PROGRAM_HISTORY: 'programHistory',
  PROGRAM_INPUT: 'programInput',

  SCAN_DOCUMENTS_HISTORY: 'scanDocumentHistory',
  SCAN_DOCUMENTS_INPUT: 'scanDocumentInput',

  PATIENT_ENROLLMENT_INFO: 'patientEnrollmentInfo',
  ENROLLMENT_HISTORY: 'enrollmentHistory',

  SPECIALIST_REFERRAL_HISTORY: 'specialistReferralHistory',
  SPECIALIST_REFERRAL_INPUT: 'specialistReferralInput',
  DAILY_EXTRACT: 'dailyExtract',

  ENCOUNTER_FORM_MANAGEMENT_SITE: 'encounterFormManagementSite',
  ENCOUNTER_FORM_MANAGEMENT_CODE: 'encounterFormManagementCode',
  ENCOUNTER_FORM_MANAGEMENT_GRID: 'encounterFormManagementGrid',
  ENCOUNTER_FORM_MANAGEMENT_PROGRAM: 'encounterFormManagementProgram',

  FOSTER_HOME_SEARCH: 'fosterHomeSearch',
  FOSTER_HOME_REGISTER: 'fosterHomeRegister',
  FOSTER_HOME_EDIT: 'fosterHomeEdit',
  FOSTER_CHILD_ASSIGNMENT: 'fosterChildAssignment',
  FOSTER_CARE_CASE_NOTE: 'fosterCareCaseNote',
  FOSTER_HOME_DASHBOARD: 'fosterHomeDashboard',

  DEMOGRAPHIC_HISTORY: 'demographicHistory',
  DEMOGRAPHIC_ENROLLMENT: 'demographicEnrollment',
  DEMOGRAPHIC_INPUT: 'demographicInput',

  GROUP_EDITOR_HISTORY: 'groupEditorHistory',
  GROUP_EDITOR_INPUT: 'groupEditorInput',
  GROUP_EDITOR_PATIENT: 'groupEditorPatient',
  GROUP_EDITOR_PATIENT_INPUT: 'groupEditorPatientInput',

  IMMUNIZATION_HISTORY: 'immunizationHistory',
  IMMUNIZATION_REGISTRY_SETTINGS: 'immunizationRegistrySettings',
  IMMUNIZATION_INPUT: 'immunizationInput',
  IMMUNIZATION_TIMELINE: 'immunizationTimeLine',

  SUPPORT_AUTOSAVE_DYNAMIC_FORM: 'supportAutosaveDynamicForm',
  SUPPORT_LOOKUP_TABLE: 'supportLookupTable',
  SUPPORT_AUTOSAVE_HX: 'supportAutosaveHX',
  SUPPORT_ENROLL_REF: 'supportEnrollRef',
  SUPPORT_MENU_MANAGEMENT: 'supportMenuManagement',
  SUPPORT_NEW_ACCOUNT: 'supportNewAccount',
  SUPPORT_PAGE_SECURITIES: 'supportPageSecurities',
  SUPPORT_PRESCRIPTIONS: 'supportPrescriptions',
  SUPPORT_PROGRESS_NOTES: 'supportProgressNotes',
  SUPPORT_RESET_PASSWORD: 'supportResetPassword',
  SUPPORT_RIGHT_TRANSFER: 'supportRightTransfer',
  SUPPORT_SYSTEM_CONFIGURATION: 'supportSystemConfiguration',
  SUPPORT_BYPASS_RESET_MFA: 'byPassResetMFA',
  SUPPORT_DIAGNOSIS_CODE: 'diagnosisCode',
  SUPPORT_SERVICE_PLAN: 'supportServicePlan',

  DATA_IMPORT_HISTORY: 'dataImportHistory',
  DATA_IMPORT_RUNS_HISTORY: 'dataImportRunsHistory',
  DATA_IMPORT_UPLOAD: 'dataImportUpload',
  DATA_IMPORT_TABLE: 'dataImportTable',
  DATA_IMPORT_DIAGRAM: 'dataImportDiagram',

  CLOSURE_ENROLLMENT_HISTORY: 'closureEnrollmentHistory',
  CLOSURE_HISTORY: 'closureHistory',
  CLOSURE_EXTEND: 'closureExtend',

  EXAM_OPTIONS_TYPES: 'examOptionsTypes',
  EXAM_OPTIONS_EDIT: 'examOptionsEdit',
  EXAM_OPTIONS_OPTIONS: 'examOptionsOptions',
  EXAM_OPTIONS_OPTIONS_EDIT: 'examOptionsOptionsEdit',

  SERVICE_PLAN_MAPPING_FIELDS: 'servicePlanMappingFields',
  SERVICE_PLAN_MAPPING_FIELD_INPUT: 'servicePlanMappingFieldInput',
  SERVICE_PLAN_FIELDS: 'servicePlanFields',
  SERVICE_PLAN_FIELD_INPUT: 'servicePlanFieldsInput',
  SERVICE_PLAN_SERVICE_CODES: 'servicePlanServiceCodes',
  SERVICE_PLAN_SERVICE_CODE_INPUT: 'servicePlanServiceCodeInput',
  SERVICE_PLAN_SIGNATURE: 'servicePlanSignature',
  SERVICE_PLAN_SIGNATURE_INPUT: 'servicePlanSignatureInput',
  SERVICE_PLAN_SIGNATURE_HISTORY: 'servicePlanSignature',

  RADIOLOGY_HISTORY: 'RadiologyHistory',
  RADIOLOGY_INPUT: 'RadiologyInput',

  HEALTH_INFORMATION_MEDICAL_RECORDS_RELEASES: 'healthInformationMedicalRecordsReleases',
  HEALTH_INFORMATION_RELEASES: 'healthInformationReleases',
  HEALTH_INFORMATION_RELEASE_INPUT: 'healthInformationReleaseInput',

  QUALITY_MANAGEMENT_CGA_INPUT: 'qualityManagementCGAInput',
  QUALITY_MANAGEMENT_CGA_SEARCH: 'qualityManagementCGASearch',
  QUALITY_MANAGEMENT_REPORT_INPUT: 'qualityManagementReportInput',
  QUALITY_MANAGEMENT_REPORT_SEARCH: 'qualityManagementReportSearch',

  CLINICAL_FORMS_SEARCH: 'clinicalFormsSearch',
  CLINICAL_FORMS_ENCOUNTERS: 'clinicalFormsEncounters',
  CLINICAL_FORMS_EMPLOYEES: 'clinicalFormsEmployees',
  CLINICAL_FORMS_PATIENTS: 'clinicalFormsPatients',
  CLINICAL_FORMS_DYNAMIC_FORMS: 'clinicalFormsDynamicForms',
  RE_ASSIGNMENT_FORMS: 'reAssignmentForms',

  PSYCH_EVALUATION_NOTE_INPUT: 'psychEvaluationNoteInput',

  PSYCHOTHERAPY_NOTE_INPUT: 'psychotherapyNoteInput',

  PAYOR_ASSIGNMENT_HISTORY: 'payorAssignmentHistory',
  PAYOR_ASSIGNMENT_INFORMATION: 'payorAssignmentInformation',

  CRM_DASHBOARD: 'crmDashboard',
  CRM_CLIENT: 'crmClient',
  CRM_ANALYTICS: 'crmAnalytics',
  PSYCHOSOCIAL_ASSESSMENT_NOTE_INPUT: 'psychosocialAssessmentNoteInput',
  INFORMED_CONSENT_HISTORY: 'informedConsentHistory',
  INFORMED_CONSENT_INFO: 'informedConsentInfo',

  PHQ_A_HISTORY: 'phqAHistory',
  PHQ_A_FORM: 'phqAForm',

  FUNDING_SOURCE_HISTORY: 'fundingSourceHistory',
  FUNDING_SOURCE_INPUT: 'fundingSourceInput',

  SUICIDE_SEVERITY_RATING_SCALE_HISTORY: 'suicideSeverityRatingScaleHistory',
  SUICIDE_SEVERITY_RATING_SCALE_FORM: 'suicideSeverityRatingScaleInput',

  PHQ_HISTORY: 'phqHistory',
  PHQ_FORM: 'phqForm',
  PHQ_CHART: 'phqChart',

  ACE_HISTORY: 'aceHistory',
  ACE_FORM: 'aceForm',
  ACE_CHART: 'aceChart',

  PCL5_HISTORY: 'pcl5History',
  PCL5_FORM: 'pcl5Form',
  PCL5_CHART: 'pcl5Chart',

  AUDIT_HISTORY: 'auditHistory',
  AUDIT_FORM: 'auditForm',
  AUDIT_CHART: 'auditChart',

  PC_PTSD_HISTORY: 'pcptsdHistory',
  PC_PTSD_FORM: 'pcptsdForm',
  PC_PTSD_CHART: 'pcptsdChart',

  GAD_7_HISTORY: 'gad7History',
  GAD_7_FORM: 'gad7Form',
  GAD_7_CHART: 'gad7Chart',

  VITAL_HISTORY: 'vitalsHistory',
  VITAL_FORM: 'vitalsForm',
  VITAL_CHART: 'vitalsChart',

  COT_HISTORY: 'cotHistory',
  COT_FORM: 'cotForm',
  COT_AMENDMENTS: 'cotAmendments',
  COT_JUDICIAL_REVIEWS: 'cotJudicialReviews',
  COT_STATUS_REPORTS: 'cotStatusReports',

  TIME_BLOCKED_REASON_HISTORY: 'timeBlockedReasonHistory',
  SCHEDULE_REASON_LIST: 'scheduleReasonList',

  CINA_INPUT: 'cinaInput',
  CINA_REPORTS: 'cinaReport',

  CIWA_INPUT: 'ciwaInput',
  CIWA_REPORTS: 'ciwaReport',

  PRAPARE_INPUT: 'PRAPAREInput',
  PRAPARE_HISTORY: 'PRAPAREHistory',

  COWS_INPUT: 'COWSInput',
  COWS_HISTORY: 'COWSHistory',

  ANNOUNCEMENTS_INPUT: 'ANNOUNCEMENTSInput',
  ANNOUNCEMENTS_HISTORY: 'ANNOUNCEMENTSHistory',

  FAMILY_HEALTH_INPUT: 'FAMILY_HEALTHInput',
  FAMILY_HEALTH_HISTORY: 'FAMILY_HEALTHHistory',

  ENGAGEMENT_HISTORY: 'engagementHistory',
  ENGAGEMENT_INPUT: 'engagementInput',

  CALOCUS_INPUT: 'CALOCUSInput',
  CALOCUS_HISTORY: 'CALOCUSHistory',
  CALOCUS_LEVEL_OF_CARE: 'CALOCUSLevelOfCare',

  LOCUS_INPUT: 'LOCUSInput',
  LOCUS_HISTORY: 'LOCUSHistory',
  LOCUS_LEVEL_OF_CARE: 'LOCUSLevelOfCare',

  UPCOMING_VISIT: 'matUpcomingVisit',
  PENDING_INTAKE: 'matPendingIntake',
  MAT_REPORTS: 'matReports',
  MAT_PATIENT_CENTER: 'matPatientCenter',
  MAT_INVENTORY: 'matInventory',
  MAT_FACILITY_SCHEDULE: 'matFacilitySchedule',
  MAT_AUTO_SCHEDULE_CARE_PLANS: 'matAutoSchedule',
  MAT_DASHBOARD: 'matDashboard',
  MAT_QUEUE_CENTER: 'matQueueCenter',
  MAT_MONITOR_DISPLAY_EDITOR: 'matMonitorDisplayEditor',

  DISCHARGE_PLAN_HISTORY: 'dischargePlanHistory',
  INPUT_CLINICAL_DISCHARGE: 'inputClinicalDischarge',

  ART_CFT_MEETING_SUMMARIES: 'artCftMeetingSummaries',
  ART_CFT_AUTO_SAVE: 'artCftAutoSave',
  ART_CFT_STAFF_SUMMARIES: 'artCftStaffSummaries',
  ART_CFT_INPUT: 'artCftInput',

  LAB_RESULT_ERROR_HISTORY: 'labResultErrorHistory',
  LAB_RESULT_ERROR_LAB_SEARCH: 'labResultErrorLabSearch',
  LAB_RESULT_ERROR_PATIENT_SEARCH: 'labResultErrorPatientSearch',

  INTERNAL_ORDER_INPUT: 'internalOrderInput',
  INTERNAL_ORDER_HISTORY: 'internalOrderHistory',
  INTERNAL_ORDER_ENROLLMENT: 'internalOrderEnrollment',

  ENCOUNTER_ENTRY_PATIENT: 'encounterEntryPatient',
  ENCOUNTER_ENTRY_INPUT: 'encounterEntryInput',
  ENGAGEMENT_SESSION_NOTE_INPUT: 'engagementSessionNoteInput',

  PATIENT_ROLE_RESTRICTION: 'patientRoleRestriction',
  PATIENT_EMPLOYEE_RESTRICTION: 'patientEmployeeRestriction',

  INJECTION_HISTORY: 'injectionHistory',
  INJECTION_INPUT: 'injectionInput',

  PENDING_PATIENT_HISTORY: 'pendingPatientHistory',
  ALLERGY_HISTORY: 'allergyHistory',
  ALLERGY_INPUT: 'allergyInput',

  IBHIS_SEARCH: 'ibhisSearch',
  IBHIS_LINK: 'ibhisLink',
  IBHIS_EPISODES: 'ibhisEpisodes',
  IBHIS_UPDATE: 'ibhisUpdate',
  IBHIS_REPORT: 'ibhisReport',
  IBHIS_SYNC_HISTORY: 'ibhisSyncHistory',
  IBHIS_CANS: 'ibhisCANS',
  IBHIS_PSC: 'ibhisPSC',
  IBHIS_CLIENT_DATA: 'ibhisClientData',
  IBHIS_SERVICE_REQUEST: 'ibhisServiceRequest',
  IBHIS_LOCUS: 'ibhisLOCUS',

  NURSE_PROGRESS_NOTE: 'nurseprogressnoteInput',
  INPATIENT_NOTE_INPUT: 'inpatientNoteInput',

  PLACEMENT_HISTORY: 'placementHistory',
  PLACEMENT_INPUT: 'placementInput',

  IMPLANTABLE_DEVICE_HISTORY: 'implantableDeviceHistory',
  IMPLANTABLE_DEVICE_ENTRY: 'implantableDeviceEntry',

  PATIENT_PROCEDURES_HISTORY: 'patientProcedureHistory',
  PATIENT_PROCEDURE_INPUT: 'patientProcedureInput',

  WHO_ASSIST_HISTORY: 'whoAssistHistory',
  WHO_ASSIST_FORM: 'whoAssistForm',

  DIRECT_MESSAGING: 'directMessaging',
  DIRECT_MESSAGING_ADD: 'directMessagingAdd',

  SUICIDE_ASSESSMENT_FIVE_STEP_EVALUATION_AND_TRIAGE_HISTORY:
    'suicideAssessmentFiveStepEvaluationAndTriageHistory',
  SUICIDE_ASSESSMENT_FIVE_STEP_EVALUATION_AND_TRIAGE_INPUT:
    'suicideAssessmentFiveStepEvaluationAndTriageInput',
  SUICIDE_RISK_SCREENING_FORM: 'suicideRiskScreeningInput',
  SUICIDE_RISK_SCREENING_HISTORY: 'suicideRiskScreeningHistory',
};

export const LOGIN_ROUTE = '/login';
export const LOGOUT_ROUTE = '/logout';
export const FORGOT_PASSWORD_ROUTE = '/login/forgot-pass';
export const EMPTY_ROUTE = '/under-construction';
export const DEFAULT_ROUTE = '/appointment';

export const ROUTE_NAME = {
  APPOINTMENT: '/appointment',
  ANALYTIC: '/analytics',
  DASHBOARD: '/dashboard',

  DYNAMIC_FORM: '/dynamic-form',
  DYNAMIC_FORM_BUILDER: '/dynamic-form/builder',
  // New UI of dynamic form.
  DYNAMIC_FORM_CREATOR: '/dynamic-form/creator',
  DYNAMIC_FORM_VIEWER: '/dynamic-form/viewer',

  NOTE: '/progress-note',
  PSYCH_NOTE: '/progress-note/psych-progress-note',
  CM_NOTE: '/progress-note/cm-progress-note',
  PCP_NOTE: '/progress-note/pcp-progress-note',
  GROUP_NOTE: '/progress-note/group-note',
  PSYCH_EVALUATION_NOTE: '/progress-note/psychiatric-evaluation',
  PSYCHOSOCIAL_ASSESSMENT_NOTE: '/progress-note/psychosocial-assessment-note',
  PSYCHOTHERAPY_PROGRESS_NOTE: '/progress-note/psychotherapy-note',
  ENGAGEMENT_SESSION_NOTE: '/engagement-session-note',

  DYNAMIC_PROGRESS_NOTE: '/dynamic-progress-note',
  DYNAMIC_PROGRESS_NOTE_BUILDER: '/dynamic-progress-note/builder',
  DYNAMIC_PROGRESS_NOTE_VIEWER: '/dynamic-progress-note/viewer',

  SERVICE_PLAN: '/service-plan',
  DYNAMIC_SERVICE_PLAN: '/dynamic-service-plan',
  DYNAMIC_SERVICE_PLAN_BUILDER: '/dynamic-service-plan/builder',
  DYNAMIC_SERVICE_PLAN_VIEWER: '/dynamic-service-plan/viewer',
  INPATIENT: '/inpatient',
  RESIDENTIAL: '/residential',
  E_MAR: '/emar',
  ASSESSMENT: '/assessment',
  SDOH: '/sdoh',
  WELCOME: '/welcome',
  ASAM: '/asam',
  CRISIS_PLAN: '/crisis-plan',
  RPA: '/robotic-process-automation',
  PRESCRIPTION: '/prescription',
  LAB_ORDER: '/laborder',
  BATCH: '/revenue-cycle-management',
  CRISIS_PREVENTION_PLAN: '/crisis-prevention-plan',
  SNCD: '/sncd',
  SUPPORT_PLAN: '/support-plan',
  DYNAMIC_REPORT: '/dynamic-report',
  PAYMENT_MANAGEMENT: '/copay-management',
  PCP_COMMUNICATION: '/pcp-communication',
  QUICK_ENROLLMENT: '/quick-enrollment',
  ADMIN_SPECIFIC_SECURITY: '/specific-security',
  HR: '/hr',
  PATIENT_ASSIGNMENT: '/patient-assignment',
  COVER_SHEET: '/cover-sheet',
  CYBHI: '/cybhi',
  PROBLEM_LIST: '/problem-list',
  SCANNED_DOCUMENTS: '/scanned-documents',
  ENROLLMENT_HISTORY: '/enrollment-history',
  SPECIALIST_REFERRAL: '/specialist-referral',
  DAILY_EXTRACT: '/daily-extract',
  ENCOUNTER_FORM_MANAGEMENT: '/encounter-form-management',
  FOSTER_HOME: '/foster-home',
  DEMOGRAPHIC: '/demographic',
  GROUP_EDITOR: '/group-editor',
  IMMUNIZATION: '/immunization',
  SUPPORT: '/support',
  DATA_IMPORT: '/data-import',
  CLOSURE: '/closure-scope',
  EXAM_OPTIONS: '/exam-options',
  SERVICE_PLAN_FIELD_MANAGER: '/service-plan-field-manager',
  RADIOLOGY: '/radiology',
  HEALTH_INFORMATION: '/health-information',
  QUALITY_MANAGEMENT: '/quality-management',
  CLINICAL_FORMS_SEARCH: '/clinical-forms-search',
  RE_ASSIGNMENT: '/re-assignment',
  PAYOR_ASSIGNMENT: '/payor-assignment',
  CRM: '/client-relationship-management',
  INFORMED_CONSENT: '/informed-consent',
  PHQA: '/phq-a',
  FUNDING_SOURCE: '/funding-source',
  PHQ: '/phq',
  PCL5: '/pcl5',
  ACE: '/ace',
  AUDIT: '/audit',
  PC_PTSD: '/pcptsd',
  GAD7: '/gad7',
  CINA: '/cina',
  CIWA: '/ciwa',
  COWS: '/cows',
  CALOCUS: '/childLOCUS',
  LOCUS: '/LOCUS',
  PRAPARE: '/PRAPARE',
  MEDICATION_ASSISTED_TREATMENT: '/medication-assisted-treatment',
  COURT_ORDERED_TREATMENT: '/court-ordered-treatment',
  DISCHARGE_PLAN: '/discharge-plan',
  ART_CFT: '/art-cft',
  LAB_RESULT_ERROR: '/lab-result-error',
  INTERNAL_ORDERS: '/internal-orders',
  ENCOUNTER_ENTRY: '/encounter-entry',
  COMMUNICATIONS_CENTER: '/communications-center',
  ENGAGEMENT: '/engagement',
  PATIENT_EMPLOYEE_RESTRICTION: '/patient-employee-restriction',
  INJECTION: '/injection',
  PENDING_PATIENT_REQUESTS: '/pending-patient-requests',
  ALLERGY: '/allergy',
  TIME_BLOCK_REASON: '/time-block-reason',
  IBHIS: '/ibhis',
  ANNOUNCEMENTS: '/announcements',
  NURSE_PROGRESS_NOTE: '/nurse-progress-note',
  SUICIDE_SEVERITY_RATING_SCALE: '/suicideSeverityRatingScale',
  VITALS: '/vitals',
  FAMILY_HEALTH: '/familyhealth',
  INPATIENT_NOTE: '/inpatient-note',
  PLACEMENT: '/placement',
  IMPLANTABLE_DEVICES: '/implantableDevice',
  PATIENT_PROCEDURE: '/patientProcedure',
  WHO_ASSIST: '/assist',
  DIRECT_MESSAGING: '/directmessaging',
  SAFET: '/safet',
  SUICIDE_RISK_SCREENING: '/SuicideRiskScreening',
  TRIAGE_OBSERVATION: '/triageobs',
};

export const PUBLIC_ROUTE_NAME = {
  DYNAMIC_FORM_VIEWER: '/public/dynamic-form-viewer',
  MONITOR_DISPLAY_PREVIEW: '/public/monitor-display-preview',
  ZOOM: '/zoom',
};

export const ROUTES = [
  {
    path: ROUTE_NAME.PAYMENT_MANAGEMENT,
    Component: React.lazy(() => import('pages/ContentPage/PaymentManagement')),
    name: t('Payment Management'),
  },

  {
    path: ROUTE_NAME.PRESCRIPTION,
    Component: React.lazy(() => import('pages/ContentPage/Prescription')),
    name: t('Presctiption'),
  },
  {
    path: ROUTE_NAME.RPA,
    Component: React.lazy(() => import('pages/ContentPage/RPA')),
    name: t('Automation'),
  },
  {
    path: ROUTE_NAME.ASAM,
    Component: React.lazy(() => import('pages/ContentPage/ASAM')),
    name: t('ASAM'),
  },
  {
    path: `${ROUTE_NAME.ASAM}/:id`,
    Component: React.lazy(() => import('pages/ContentPage/ASAM')),
    name: t('ASAM'),
  },
  {
    path: ROUTE_NAME.CRISIS_PLAN,
    Component: React.lazy(() => import('pages/ContentPage/CrisisPlan')),
    name: t('Crisis Intervention Relapse Plan'),
  },
  {
    path: `${ROUTE_NAME.CRISIS_PLAN}/:id`,
    Component: React.lazy(() => import('pages/ContentPage/CrisisPlan')),
    name: t('Crisis Intervention Relapse Plan'),
  },
  {
    path: ROUTE_NAME.CRISIS_PREVENTION_PLAN,
    Component: React.lazy(() => import('pages/ContentPage/CrisisPreventionPlan')),
    name: t('Crisis Prevention Plan'),
  },
  {
    path: ROUTE_NAME.DASHBOARD,
    Component: React.lazy(() => import('pages/ContentPage/Dashboard')),
    name: t('Dashboard'),
  },

  {
    path: ROUTE_NAME.DYNAMIC_FORM,
    Component: React.lazy(() => import('pages/ContentPage/DynamicForm')),
    name: t('Dynamic form'),
  },
  {
    path: `${ROUTE_NAME.DYNAMIC_FORM_BUILDER}`,
    Component: React.lazy(() => import('pages/ContentPage/DynamicForm/Builder')),
    name: t('Dynamic form builder'),
  },
  {
    path: `${ROUTE_NAME.DYNAMIC_FORM_BUILDER}/:id`,
    Component: React.lazy(() => import('pages/ContentPage/DynamicForm/Builder')),
    name: t('Dynamic form builder'),
  },
  {
    path: `${ROUTE_NAME.DYNAMIC_FORM_CREATOR}`,
    Component: React.lazy(() => import('pages/ContentPage/DynamicForm/Creator')),
    name: t('Dynamic form creator'),
  },
  {
    path: `${ROUTE_NAME.DYNAMIC_FORM_CREATOR}/:id`,
    Component: React.lazy(() => import('pages/ContentPage/DynamicForm/Creator')),
    name: t('Dynamic form creator'),
  },
  {
    path: `${ROUTE_NAME.DYNAMIC_FORM_VIEWER}/:id`,
    Component: React.lazy(() => import('pages/ContentPage/DynamicForm/Viewer')),
    name: t('Dynamic form Viewer'),
  },
  {
    path: ROUTE_NAME.APPOINTMENT,
    Component: React.lazy(() => import('pages/ContentPage/Appointment')),
    name: t('Appointment'),
  },
  {
    path: ROUTE_NAME.ANALYTIC,
    Component: React.lazy(() => import('pages/ContentPage/Analytic')),
    name: t('Analytics'),
  },
  {
    path: `${ROUTE_NAME.ANALYTIC}/:id`,
    Component: React.lazy(() => import('pages/ContentPage/Analytic')),
    name: t('Analytics'),
  },
  {
    path: ROUTE_NAME.PSYCH_NOTE,
    Component: React.lazy(() => import('pages/ContentPage/ProgressNote/PsychNote')),
    name: t('Psych Progress Note'),
  },
  {
    path: `${ROUTE_NAME.PSYCH_NOTE}/:id`,
    Component: React.lazy(() => import('pages/ContentPage/ProgressNote/PsychNote')),
    name: t('Psych Progress Note Detail'),
  },
  {
    path: ROUTE_NAME.PCP_NOTE,
    Component: React.lazy(() => import('pages/ContentPage/ProgressNote/PCPNote')),
    name: t('PCP Progress Note'),
  },
  {
    path: `${ROUTE_NAME.PCP_NOTE}/:id`,
    Component: React.lazy(() => import('pages/ContentPage/ProgressNote/PCPNote')),
    name: t('PCP Progress Note'),
  },
  {
    path: ROUTE_NAME.CM_NOTE,
    Component: React.lazy(() => import('pages/ContentPage/ProgressNote/CMNote')),
    name: t('CM Progress Note'),
  },
  {
    path: `${ROUTE_NAME.CM_NOTE}/:id`,
    Component: React.lazy(() => import('pages/ContentPage/ProgressNote/CMNote')),
    name: t('CM Progress Note'),
  },
  {
    path: ROUTE_NAME.GROUP_NOTE,
    Component: React.lazy(() => import('pages/ContentPage/ProgressNote/GroupNote')),
    name: t('Group Note'),
  },
  {
    path: `${ROUTE_NAME.GROUP_NOTE}/:id`,
    Component: React.lazy(() => import('pages/ContentPage/ProgressNote/GroupNote')),
    name: t('Group Note'),
  },
  {
    path: ROUTE_NAME.NOTE,
    Component: React.lazy(() => import('pages/ContentPage/ProgressNote')),
    name: t('Progress Note'),
  },
  {
    path: ROUTE_NAME.DYNAMIC_PROGRESS_NOTE,
    Component: React.lazy(() => import('pages/ContentPage/ProgressNote/DynamicProgressNotes')),
    name: t('Dynamic Progress Notes'),
  },
  {
    path: ROUTE_NAME.DYNAMIC_PROGRESS_NOTE_BUILDER,
    Component: React.lazy(() =>
      import('pages/ContentPage/ProgressNote/DynamicProgressNotes/Builder'),
    ),
    name: t('Dynamic Progress Note Builder'),
  },
  {
    path: `${ROUTE_NAME.DYNAMIC_PROGRESS_NOTE_VIEWER}/:id`,
    Component: React.lazy(() =>
      import('pages/ContentPage/ProgressNote/DynamicProgressNotes/Viewer'),
    ),
    name: t('Dynamic Progress Note Viewer'),
  },
  {
    path: `${ROUTE_NAME.DYNAMIC_PROGRESS_NOTE_VIEWER}/:id/:type/:saveId`,
    Component: React.lazy(() =>
      import('pages/ContentPage/ProgressNote/DynamicProgressNotes/Viewer'),
    ),
    name: t('Dynamic Progress Note Viewer Answer'),
  },

  {
    path: `${ROUTE_NAME.DYNAMIC_PROGRESS_NOTE_BUILDER}/:id`,
    Component: React.lazy(() =>
      import('pages/ContentPage/ProgressNote/DynamicProgressNotes/Builder'),
    ),
    name: t('Update Dynamic Progress Note Builder'),
  },
  {
    path: `${ROUTE_NAME.SERVICE_PLAN}/:id`,
    Component: React.lazy(() => import('pages/ContentPage/ServicePlan')),
    name: t('Service Plan Detail'),
  },
  {
    path: ROUTE_NAME.SERVICE_PLAN,
    Component: React.lazy(() => import('pages/ContentPage/ServicePlan')),
    name: t('Service Plan'),
  },
  {
    path: ROUTE_NAME.DYNAMIC_SERVICE_PLAN,
    Component: React.lazy(() => import('pages/ContentPage/DynamicServicePlan')),
    name: t('Dynamic Service Plan'),
  },
  {
    path: ROUTE_NAME.DYNAMIC_SERVICE_PLAN_BUILDER,
    Component: React.lazy(() => import('pages/ContentPage/DynamicServicePlan/Builder')),
    name: t('Dynamic Service Plan Builder'),
  },
  {
    path: `${ROUTE_NAME.DYNAMIC_SERVICE_PLAN_BUILDER}/:id`,
    Component: React.lazy(() => import('pages/ContentPage/DynamicServicePlan/Builder')),
    name: t('Dynamic Service Plan Builder'),
  },
  {
    path: `${ROUTE_NAME.DYNAMIC_SERVICE_PLAN_VIEWER}/:id`,
    Component: React.lazy(() => import('pages/ContentPage/DynamicServicePlan/Viewer')),
    name: t('Dynamic Service Plan Viewer'),
  },
  {
    path: `${ROUTE_NAME.DYNAMIC_SERVICE_PLAN_VIEWER}/:id/:type/:saveId`,
    Component: React.lazy(() => import('pages/ContentPage/DynamicServicePlan/Viewer')),
    name: t('Dynamic Service Plan Viewer Answer'),
  },
  {
    path: ROUTE_NAME.INPATIENT,
    Component: React.lazy(() => import('pages/ContentPage/Inpatient')),
    name: t('Inpatient Module'),
  },
  {
    path: ROUTE_NAME.RESIDENTIAL,
    Component: React.lazy(() => import('pages/ContentPage/Residential')),
    name: t('Residential'),
  },
  {
    path: ROUTE_NAME.E_MAR,
    Component: React.lazy(() => import('pages/ContentPage/NewEmar')),
    name: t('eMar'),
  },
  {
    path: ROUTE_NAME.SDOH,
    Component: React.lazy(() => import('pages/ContentPage/SDOH')),
    name: t('SDOH'),
  },
  {
    path: ROUTE_NAME.LAB_ORDER,
    Component: React.lazy(() => import('pages/ContentPage/Laboratory')),
    name: t('Laboratory'),
  },
  {
    path: ROUTE_NAME.BATCH,
    Component: React.lazy(() => import('pages/ContentPage/Batch')),
    name: t('Revenue Cycle Management'),
  },
  {
    path: `${ROUTE_NAME.BATCH}/:id`,
    Component: React.lazy(() => import('pages/ContentPage/Batch')),
    name: t('Revenue Cycle Management'),
    doNotSupportSplitScreen: true,
  },
  {
    path: `${ROUTE_NAME.BATCH}/:id/:detailId`,
    Component: React.lazy(() => import('pages/ContentPage/Batch')),
    name: t('Revenue Cycle Management'),
    doNotSupportSplitScreen: true,
  },
  {
    path: ROUTE_NAME.SNCD,
    Component: React.lazy(() => import('pages/ContentPage/SNCDPlan')),
    name: t('SNCD Plan'),
  },
  {
    path: ROUTE_NAME.SUPPORT_PLAN,
    Component: React.lazy(() => import('pages/ContentPage/SupportPlan')),
    name: t('Support & Safety Plan'),
  },
  {
    path: ROUTE_NAME.DYNAMIC_REPORT,
    Component: React.lazy(() => import('pages/ContentPage/DynamicReport')),
    name: t('Dynamic Reports'),
  },
  {
    path: ROUTE_NAME.PCP_COMMUNICATION,
    Component: React.lazy(() => import('pages/ContentPage/PcpCommunication')),
    name: t('PCP Communication'),
  },
  {
    path: ROUTE_NAME.QUICK_ENROLLMENT,
    Component: React.lazy(() => import('pages/ContentPage/QuickEnrollment')),
    name: t('Quick Enrollment'),
  },
  {
    path: ROUTE_NAME.ADMIN_SPECIFIC_SECURITY,
    Component: React.lazy(() => import('pages/ContentPage/SpecificSecurity')),
    name: t('Specific Security'),
  },
  {
    path: ROUTE_NAME.HR,
    Component: React.lazy(() => import('pages/ContentPage/HREnrollment')),
    name: t('HR - Employee Enrollment'),
  },
  {
    path: ROUTE_NAME.PATIENT_ASSIGNMENT,
    Component: React.lazy(() => import('pages/ContentPage/PatientAssignment')),
    name: t('Patient Assignment'),
  },
  {
    path: ROUTE_NAME.COVER_SHEET,
    Component: React.lazy(() => import('pages/ContentPage/CoverSheet')),
    name: t('Cover Sheet'),
  },
  {
    path: ROUTE_NAME.CYBHI,
    Component: React.lazy(() => import('pages/ContentPage/CYBHI')),
    name: t('CYBHI'),
  },
  {
    path: ROUTE_NAME.PROBLEM_LIST,
    Component: React.lazy(() => import('pages/ContentPage/ProblemList')),
    name: t('Problems List'),
  },
  {
    path: ROUTE_NAME.SCANNED_DOCUMENTS,
    Component: React.lazy(() => import('pages/ContentPage/ScannedDocuments')),
    name: t('Scanned Documents'),
  },
  {
    path: ROUTE_NAME.ENROLLMENT_HISTORY,
    Component: React.lazy(() => import('pages/ContentPage/EnrollmentHistory')),
    name: t('Enrollment History'),
  },
  {
    path: ROUTE_NAME.SPECIALIST_REFERRAL,
    Component: React.lazy(() => import('pages/ContentPage/SpecialistReferral')),
    name: t('Specialist Referral'),
  },
  {
    path: ROUTE_NAME.DAILY_EXTRACT,
    Component: React.lazy(() => import('pages/ContentPage/DailyExtract')),
    name: t('Daily Extract'),
  },
  {
    path: ROUTE_NAME.ENCOUNTER_FORM_MANAGEMENT,
    Component: React.lazy(() => import('pages/ContentPage/EncounterFormManagement')),
    name: t('Encounter Form Management'),
  },
  {
    path: ROUTE_NAME.FOSTER_HOME,
    Component: React.lazy(() => import('pages/ContentPage/FosterHome')),
    name: t('Foster Home'),
  },
  {
    path: ROUTE_NAME.DEMOGRAPHIC,
    Component: React.lazy(() => import('pages/ContentPage/Demographic')),
    name: t('Demographic'),
  },
  {
    path: ROUTE_NAME.GROUP_EDITOR,
    Component: React.lazy(() => import('pages/ContentPage/GroupEditor')),
    name: t('Group Editor'),
  },
  {
    path: ROUTE_NAME.IMMUNIZATION,
    Component: React.lazy(() => import('pages/ContentPage/Immunization')),
    name: t('Immunization'),
  },
  {
    path: ROUTE_NAME.SUPPORT,
    Component: React.lazy(() => import('pages/ContentPage/Support')),
    name: t('Support'),
  },
  {
    path: ROUTE_NAME.DATA_IMPORT,
    Component: React.lazy(() => import('pages/ContentPage/DataImport')),
    name: t('Data Import'),
  },
  {
    path: ROUTE_NAME.CLOSURE,
    Component: React.lazy(() => import('pages/ContentPage/Closure')),
    name: t('Closure'),
  },
  {
    path: ROUTE_NAME.EXAM_OPTIONS,
    Component: React.lazy(() => import('pages/ContentPage/ExamOptions')),
    name: t('Exam Options'),
  },
  {
    path: ROUTE_NAME.SERVICE_PLAN_FIELD_MANAGER,
    Component: React.lazy(() => import('pages/ContentPage/ServicePlanFieldManager')),
    name: t('Service Plan Field Manager'),
  },
  {
    path: `${ROUTE_NAME.RADIOLOGY}/:id`,
    Component: React.lazy(() => import('pages/ContentPage/Radiology')),
    name: t('Radiology'),
  },
  {
    path: ROUTE_NAME.RADIOLOGY,
    Component: React.lazy(() => import('pages/ContentPage/Radiology')),
    name: t('Radiology'),
  },
  {
    path: ROUTE_NAME.HEALTH_INFORMATION,
    Component: React.lazy(() => import('pages/ContentPage/HealthInformation')),
    name: t('Health Information'),
  },
  {
    path: ROUTE_NAME.QUALITY_MANAGEMENT,
    Component: React.lazy(() => import('pages/ContentPage/QualityManagement')),
    name: t('Quality Management'),
  },
  {
    path: ROUTE_NAME.CLINICAL_FORMS_SEARCH,
    Component: React.lazy(() => import('pages/ContentPage/ClinicalFormsSearch')),
    name: t('Clinical Form Search'),
  },
  {
    path: ROUTE_NAME.RE_ASSIGNMENT,
    Component: React.lazy(() => import('pages/ContentPage/ReAssignment')),
    name: t('Re-Assignment'),
  },
  {
    path: ROUTE_NAME.PSYCH_EVALUATION_NOTE,
    Component: React.lazy(() => import('pages/ContentPage/PsychEvaluationNote')),
    name: t('Psych Evaluation Note'),
  },
  {
    path: `${ROUTE_NAME.PSYCH_EVALUATION_NOTE}/:id`,
    Component: React.lazy(() => import('pages/ContentPage/PsychEvaluationNote')),
    name: t('Psych Evaluation Note'),
  },
  {
    path: ROUTE_NAME.PSYCHOTHERAPY_PROGRESS_NOTE,
    Component: React.lazy(() => import('pages/ContentPage/PsychotherapyProgressNote')),
    name: t('Psychotherapy Progress Note'),
  },
  {
    path: ROUTE_NAME.PAYOR_ASSIGNMENT,
    Component: React.lazy(() => import('pages/ContentPage/PayorAssignment')),
    name: t('Payor Assignment'),
  },
  {
    path: ROUTE_NAME.CRM,
    Component: React.lazy(() => import('pages/ContentPage/CRM')),
    name: t('CRM - Client Relationship Management'),
  },
  {
    path: ROUTE_NAME.PSYCHOSOCIAL_ASSESSMENT_NOTE,
    Component: React.lazy(() => import('pages/ContentPage/PsychosocialAssessmentNote')),
    name: t('Psychosocial Assessment Note'),
  },
  {
    path: ROUTE_NAME.INFORMED_CONSENT,
    Component: React.lazy(() => import('pages/ContentPage/InformedConsent')),
    name: t('Informed Consent'),
  },
  {
    path: ROUTE_NAME.GAD7,
    Component: React.lazy(() => import('pages/ContentPage/GAD7')),
    name: t('GAD-7'),
  },
  {
    path: ROUTE_NAME.PHQA,
    Component: React.lazy(() => import('pages/ContentPage/PHQA')),
    name: t('PHQ-A'),
  },
  {
    path: ROUTE_NAME.FUNDING_SOURCE,
    Component: React.lazy(() => import('pages/ContentPage/FundingSource')),
    name: t('Funding Source'),
  },
  {
    path: ROUTE_NAME.MEDICATION_ASSISTED_TREATMENT,
    Component: React.lazy(() => import('pages/ContentPage/MedicationAssistedTreatment')),
  },
  {
    path: ROUTE_NAME.COURT_ORDERED_TREATMENT,
    Component: React.lazy(() => import('pages/ContentPage/CourtOrderedTreatment')),
    name: t('COT - Court Ordered Treatment'),
  },
  {
    path: `${ROUTE_NAME.DISCHARGE_PLAN}/:id`,
    Component: React.lazy(() => import('pages/ContentPage/DischargePlan')),
    name: t('Discharge Plan'),
  },
  {
    path: ROUTE_NAME.DISCHARGE_PLAN,
    Component: React.lazy(() => import('pages/ContentPage/DischargePlan')),
    name: t('Discharge Plan'),
  },
  {
    path: ROUTE_NAME.CINA,
    Component: React.lazy(() => import('pages/ContentPage/CINA')),
    name: t('CINA'),
  },
  {
    path: ROUTE_NAME.CIWA,
    Component: React.lazy(() => import('pages/ContentPage/CIWA')),
    name: t('CIWA'),
  },
  {
    path: ROUTE_NAME.PRAPARE,
    Component: React.lazy(() => import('pages/ContentPage/PRAPARE')),
    name: t('PRAPARE'),
  },
  {
    path: ROUTE_NAME.CALOCUS,
    Component: React.lazy(() => import('pages/ContentPage/CALOCUS')),
    name: t('CALOCUS'),
  },
  {
    path: ROUTE_NAME.LOCUS,
    Component: React.lazy(() => import('pages/ContentPage/LOCUS')),
    name: t('LOCUS'),
  },
  {
    path: ROUTE_NAME.ART_CFT,
    Component: React.lazy(() => import('pages/ContentPage/ART')),
    name: t('ART/CFT'),
  },
  {
    path: `${ROUTE_NAME.ART_CFT}/:id`,
    Component: React.lazy(() => import('pages/ContentPage/ART')),
    name: t('ART/CFT'),
  },
  {
    path: ROUTE_NAME.LAB_RESULT_ERROR,
    Component: React.lazy(() => import('pages/ContentPage/LabResultsErrors')),
    name: t('Lab Results Errors'),
  },
  {
    path: ROUTE_NAME.INTERNAL_ORDERS,
    Component: React.lazy(() => import('pages/ContentPage/InternalOrders')),
    name: t('Internal Orders'),
  },
  {
    path: ROUTE_NAME.ENCOUNTER_ENTRY,
    Component: React.lazy(() => import('pages/ContentPage/EncouterEntry')),
    name: t('Encounter Entry'),
  },
  {
    path: ROUTE_NAME.COWS,
    Component: React.lazy(() => import('pages/ContentPage/COWS')),
    name: t('COWS'),
  },
  {
    path: `${ROUTE_NAME.ENGAGEMENT_SESSION_NOTE}/:id`,
    Component: React.lazy(() => import('pages/ContentPage/EngagementSessionNote')),
    name: t('Engagement Session Note'),
  },
  {
    path: ROUTE_NAME.ENGAGEMENT_SESSION_NOTE,
    Component: React.lazy(() => import('pages/ContentPage/EngagementSessionNote')),
    name: t('Engagement Session Note'),
  },
  {
    path: ROUTE_NAME.COMMUNICATIONS_CENTER,
    Component: React.lazy(() => import('pages/ContentPage/CommunicationsCenter')),
    name: t('Communications Center'),
  },
  {
    path: ROUTE_NAME.ENGAGEMENT,
    Component: React.lazy(() => import('pages/ContentPage/Engagement')),
    name: t('Engagement'),
  },
  {
    path: ROUTE_NAME.PATIENT_EMPLOYEE_RESTRICTION,
    Component: React.lazy(() => import('pages/ContentPage/PatientEmployeeRestriction')),
    name: t('Patient/Employee Restriction'),
  },
  {
    path: ROUTE_NAME.INJECTION,
    Component: React.lazy(() => import('pages/ContentPage/Injection')),
    name: t('Injection'),
  },
  {
    path: ROUTE_NAME.PENDING_PATIENT_REQUESTS,
    Component: React.lazy(() => import('pages/ContentPage/PendingPatientRequests')),
    name: t('Pending Patient Requests'),
  },
  {
    path: ROUTE_NAME.ALLERGY,
    Component: React.lazy(() => import('pages/ContentPage/Allergy')),
    name: t('Allergy'),
  },
  {
    path: ROUTE_NAME.PHQ,
    Component: React.lazy(() => import('pages/ContentPage/PHQ')),
    name: t('PHQ'),
  },
  {
    path: ROUTE_NAME.PCL5,
    Component: React.lazy(() => import('pages/ContentPage/PCL5')),
    name: t('PCL5'),
  },
  {
    path: ROUTE_NAME.ACE,
    Component: React.lazy(() => import('pages/ContentPage/ACE')),
    name: t('ACE'),
  },
  {
    path: ROUTE_NAME.AUDIT,
    Component: React.lazy(() => import('pages/ContentPage/Audit')),
    name: t('AUDIT'),
  },
  {
    path: ROUTE_NAME.PC_PTSD,
    Component: React.lazy(() => import('pages/ContentPage/PCPTSD')),
    name: t('PC-PTSD'),
  },
  {
    path: ROUTE_NAME.TIME_BLOCK_REASON,
    Component: React.lazy(() => import('pages/ContentPage/TimeBlockReason')),
    name: t('Time Block Reason'),
  },
  {
    path: ROUTE_NAME.IBHIS,
    Component: React.lazy(() => import('pages/ContentPage/IBHIS')),
    name: t('IBHIS'),
  },
  {
    path: ROUTE_NAME.ANNOUNCEMENTS,
    Component: React.lazy(() => import('pages/ContentPage/Announcements')),
    name: t('Announcements Center'),
  },
  {
    path: ROUTE_NAME.NURSE_PROGRESS_NOTE,
    Component: React.lazy(() => import('pages/ContentPage/NurseProgressNote')),
    name: t('Nurse Progress Note'),
  },
  {
    path: ROUTE_NAME.SUICIDE_SEVERITY_RATING_SCALE,
    Component: React.lazy(() => import('pages/ContentPage/SuicideSeverityRatingScale')),
    name: t('Detailed Columbia - Suicide Severity Rating Scale'),
  },
  {
    path: `${ROUTE_NAME.NURSE_PROGRESS_NOTE}/:id`,
    Component: React.lazy(() => import('pages/ContentPage/NurseProgressNote')),
    name: t('Nurse Progress Note'),
  },
  {
    path: ROUTE_NAME.VITALS,
    Component: React.lazy(() => import('pages/ContentPage/Vitals')),
    name: t('Vitals'),
  },
  {
    path: ROUTE_NAME.FAMILY_HEALTH,
    Component: React.lazy(() => import('pages/ContentPage/FamilyHealth')),
    name: t('Family Health'),
  },
  {
    path: ROUTE_NAME.PLACEMENT,
    Component: React.lazy(() => import('pages/ContentPage/Placement')),
    name: t('Out of Home Placement'),
  },
  {
    path: ROUTE_NAME.INPATIENT_NOTE,
    Component: React.lazy(() => import('pages/ContentPage/InpatientNote')),
    name: t('Inpatient Note'),
  },
  {
    path: `${ROUTE_NAME.INPATIENT_NOTE}/:id`,
    Component: React.lazy(() => import('pages/ContentPage/InpatientNote')),
    name: t('Inpatient Note'),
  },
  {
    path: ROUTE_NAME.IMPLANTABLE_DEVICES,
    Component: React.lazy(() => import('pages/ContentPage/ImplantableDevices')),
    name: t('Implantable Devices'),
  },
  {
    path: ROUTE_NAME.PATIENT_PROCEDURE,
    Component: React.lazy(() => import('pages/ContentPage/PatientProcedure')),
    name: t('Patient Procedure'),
  },
  {
    path: `${ROUTE_NAME.PATIENT_PROCEDURE}/:id`,
    Component: React.lazy(() => import('pages/ContentPage/PatientProcedure')),
    name: t('Patient Procedure'),
  },
  {
    path: ROUTE_NAME.WHO_ASSIST,
    Component: React.lazy(() => import('pages/ContentPage/WHOAssist')),
    name: t('WHO - ASSIST'),
  },
  {
    path: ROUTE_NAME.DIRECT_MESSAGING,
    Component: React.lazy(() => import('pages/ContentPage/DirectMessaging')),
    name: t('Direct Messaging'),
  },
  {
    path: ROUTE_NAME.SAFET,
    Component: React.lazy(() => import('pages/ContentPage/SAFET')),
    name: t('SAFET'),
  },
  {
    path: ROUTE_NAME.SUICIDE_RISK_SCREENING,
    Component: React.lazy(() => import('pages/ContentPage/SuicideRiskScreening')),
    name: t('Suicide Risk Screening'),
  },
  {
    path: ROUTE_NAME.TRIAGE_OBSERVATION,
    Component: React.lazy(() => import('pages/ContentPage/TriageObservation')),
    name: t('Triage Observation'),
  },
];

export const PUBLIC_ROUTES = [
  {
    path: `${PUBLIC_ROUTE_NAME.DYNAMIC_FORM_VIEWER}/:id`,
    Component: React.lazy(() => import('pages/PublicPage/DynamicForm/Viewer')),
    name: t('Dynamic form viewer'),
  },
  {
    path: `${PUBLIC_ROUTE_NAME.MONITOR_DISPLAY_PREVIEW}`,
    Component: React.lazy(() => import('pages/PublicPage/MonitorDisplayPreview')),
    name: t('Monitor display preview'),
  },
  {
    path: PUBLIC_ROUTE_NAME.ZOOM,
    Component: React.lazy(() => import('pages/PublicPage/Zoom')),
    name: t('Zoom'),
  },
];
