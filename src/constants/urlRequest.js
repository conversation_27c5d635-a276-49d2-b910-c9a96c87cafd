import envVariables from 'config/envVariables';

const HIMS_LOGIN = '/login';
const HIMS_EXTERNAL_LOGIN = '/external-login';
const HIMS_GET_GOOGLE_DATA = '/google/id-token';
const HIMS_EXTERNAL_LOGIN_PROVIDERS = '/external-login-providers';
const HIMS_INFO = '/info';
const HIMS_VERIFICATION = '/verification';
const HIMS_RESEND_VERIFICATION_CODE = (otpType) => `/resend-code/${otpType}`;
const HIMS_REFRESH_TOKEN = '/refresh';
const HIMS_FORGOT_PASS = '/forgot-password';
const HIMS_RESET_PASS = '/reset-password';
const HIMS_CHANGE_PASS = '/change-password';
const HIMS_ANALYSIS_URL = 'https://analysis.hmsfirst.com/ingest';
const HIMS_RELEASE_ANNOUNCEMENT = '/release-announcement';
const V2_NOTIFICATION = `${envVariables?.apiUrl}/v2-notification-hub`;
const MAT_NOTIFICATION = `${envVariables?.apiUrl}/medication-assisted/queue-hub`;

const HIMS_VA_HANDLE_MESSAGE = '/nlp';

export const API_METHOD = {
  GET: 'get',
  POST: 'post',
  PUT: 'put',
  DELETE: 'delete',
  PATCH: 'patch',
  HEAD: 'head',
};

export const HIMS = {
  HIMS_LOGIN,
  HIMS_GET_GOOGLE_DATA,
  HIMS_EXTERNAL_LOGIN,
  HIMS_EXTERNAL_LOGIN_PROVIDERS,
  HIMS_INFO,
  HIMS_VERIFICATION,
  HIMS_RESEND_VERIFICATION_CODE,
  HIMS_REFRESH_TOKEN,
  HIMS_FORGOT_PASS,
  HIMS_RESET_PASS,
  HIMS_VA_HANDLE_MESSAGE,
  HIMS_CHANGE_PASS,
  HIMS_ANALYSIS_URL,
  HIMS_RELEASE_ANNOUNCEMENT,
  V2_NOTIFICATION,
  MAT_NOTIFICATION,
};

const apiVersion = '/v2-axiom';

const API_DF_DEFAULT = `${apiVersion}/DynamicForms`;
export const API_DYNAMIC_FORM = {
  DEFAULT: API_DF_DEFAULT,
  TEMPLATE: `${API_DF_DEFAULT}/Template`,
  TEMPLATES: `${API_DF_DEFAULT}/Templates`,
  GET_FORM_TYPE: `${API_DF_DEFAULT}/FormTypes`,
  GET_ALL_ROLES: `${API_DF_DEFAULT}/roles`,
  GET_RECENT_FORMS: `${API_DF_DEFAULT}/RecentForms`,
  ANSWER: `${API_DF_DEFAULT}/answers`,
  DELETE_ANSWER: `${API_DF_DEFAULT}/answer`,
  ANSWER_HISTORY: (formId) => `${API_DF_DEFAULT}/${formId}/answers`,
  ANSWER_SUBMIT: (formId) => `${API_DF_DEFAULT}/${formId}/SubmitAnswer`,
  CONVERT_V1_TO_V2: (formId) => `${API_DF_DEFAULT}/convert/${formId}`,
  TEMPLATE_CATEGORIES: `${API_DF_DEFAULT}/Templates/categories`,
  CATEGORIES: `${API_DF_DEFAULT}/categories`,
  GET_LOGIC_TYPE: `${API_DF_DEFAULT}/builder/logicControl`,
  GET_LOGIC_CONTROL_DATA: (logicType) => `${API_DF_DEFAULT}/logicControl/${logicType}`,
  FORM_STATUSES: `${API_DF_DEFAULT}/statuses`,
  ANSWER_STATUSES: (id) => `${API_DF_DEFAULT}/${id}/answer-statuses`,
  QUESTIONS: `${API_DF_DEFAULT}/questions`,
  SAVE_ANSWERS: `${API_DF_DEFAULT}/answer-values`,
  GET_ANSWERS: (saveId) => `${API_DF_DEFAULT}/${saveId}/answer-values`,
  GET_QUESTIONS: (formId) => `${API_DF_DEFAULT}/${formId}/questions`,
};

const API_APPOINTMENT_PREFIX = `${apiVersion}/Appointments`;
export const API_APPOINTMENT = {
  DEFAULT: API_APPOINTMENT_PREFIX,
  FREQUENCIES: `${API_APPOINTMENT_PREFIX}/frequencies`,
  REASON: `${API_APPOINTMENT_PREFIX}/reason`,
  PROVIDER: `${API_APPOINTMENT_PREFIX}/provider`,
  TRANSPORTATION: `${API_APPOINTMENT_PREFIX}/transportation`,
  DURATION: `${API_APPOINTMENT_PREFIX}/duration`,
  TYPE: `${API_APPOINTMENT_PREFIX}/meeting-types`,
  ROOMS: `${API_APPOINTMENT_PREFIX}/rooms`,
  COMPLETE_STATUS: `${API_APPOINTMENT_PREFIX}/completion-status`,
  // TODO will be removed then, use EMPLOYEES instead.
  SCHEDULED_EMPLOYEES: `/employee/acceptScheduled`,
  EMPLOYEES: `${API_APPOINTMENT_PREFIX}/employees`,
  RECURSION: `${API_APPOINTMENT_PREFIX}/recursion`,
  DELAY_REASON: `${API_APPOINTMENT_PREFIX}/delayReason`,
  TIME_BLOCKED: (id = '') => `${API_APPOINTMENT_PREFIX}/time-blocked/${id}`,
  UPDATE_TIME_BLOCKED: (blockedId = '', itemId = '') =>
    `${API_APPOINTMENT_PREFIX}/time-blocked/${blockedId}/items/${itemId}`,
  TIME_BLOCKED_REASON: `${API_APPOINTMENT_PREFIX}/time-blocked/time-blocked-reasons`,
  DOXY_ACCOUNT: `${API_APPOINTMENT_PREFIX}/doxy/account`,
  ZOOM_ACCOUNT: `${API_APPOINTMENT_PREFIX}/zoom/account`,
  ZOOM_CONFIG: `${API_APPOINTMENT_PREFIX}/zoom/config`,
  SPECIALTIES_LOOKUP: `${API_APPOINTMENT_PREFIX}/specialties`,
  FAVORITE_GUEST_LIST: `${API_APPOINTMENT_PREFIX}/guest-list/favorite`,
  GROUP_GUEST_LIST: `${API_APPOINTMENT_PREFIX}/guest-list/patient-groups`,
  GUEST_LIST: `${API_APPOINTMENT_PREFIX}/guest-list/patients`,
  RECENT_GUEST_LIST: `${API_APPOINTMENT_PREFIX}/guest-list/recent`,
  GROUP_APPOINTMENT: `${API_APPOINTMENT_PREFIX}/group`,
  GROUP_APPOINTMENTS: `${API_APPOINTMENT_PREFIX}/groups`,
  GROUP_APPOINTMENTS_CALENDAR_VIEW: `${API_APPOINTMENT_PREFIX}/groups/calendar-view`,
  GET_ZOOM_ACCOUNTS_BY_EMAILS: `${API_APPOINTMENT_PREFIX}/zoom/accounts`,
  PRINT_DAILY_SCHEDULE: `${API_APPOINTMENT_PREFIX}/html/daily-schedule`,
  SCHEDULE_REASON: (id = '') => `${API_APPOINTMENT_PREFIX}/schedule-reasons/${id}`,
  TIME_BLOCK_REASON_COLOR: (id = '') => `${API_APPOINTMENT_PREFIX}/time-blocked/reasons/${id}`,
  PRINT: (id = '') => `${API_APPOINTMENT_PREFIX}/${id}/html`,
  TRACKING_LOOKUP: `${API_APPOINTMENT_PREFIX}/tracking/contact-lookup`,
  TRACKING: `${API_APPOINTMENT_PREFIX}/tracking`,
  CANCEL_RECURRING: `${API_APPOINTMENT_PREFIX}/recursion/cancellation`,
  DELETE_RECURRING: `${API_APPOINTMENT_PREFIX}/recursion/delete`,
  SET_ARRIVED: (appointmentId) => `${API_APPOINTMENT_PREFIX}/${appointmentId}/arrived`,
};

const API_USER_DEFAULT = `/user`;
export const API_USER = {
  GET_ALL: `${API_USER_DEFAULT}/all`,
  CURRENT_PATIENT: `${API_USER_DEFAULT}/current-patient`,
  PATIENT: `patient`,
  USER_2FA: '/2fa',
  VERIFY_2FA: '/2fa/verification',
  QR_CODE: '/mfa/QRCode',
  VERIFY_QR_CODE: '/mfa/verify-QRCode',
  EXTERNAL_LOGIN_ACCOUNT: `${API_USER_DEFAULT}/external-login-account`,
};

const API_EMPLOYEE_DEFAULT = `${apiVersion}/Employee`;
export const API_EMPLOYEE = {
  DEFAULT: API_EMPLOYEE_DEFAULT,
  GET_ASSOCIATED: `${API_EMPLOYEE_DEFAULT}/associated-employees`,
  PORTAL: `${API_EMPLOYEE_DEFAULT}/portal`,
  SIGNATURE: `${API_EMPLOYEE_DEFAULT}/signature`,
  AVATAR: `${API_EMPLOYEE_DEFAULT}/avatar`,
  STATES: `${API_EMPLOYEE_DEFAULT}/states`,
  GET_CREDENTIAL_NAME: `${API_EMPLOYEE_DEFAULT}/credential-name`,
  ENROLLMENT: (id = '') => `${API_EMPLOYEE_DEFAULT}/enrollment/${id}`,
  ENROLLMENT_FORMBASE: `${API_EMPLOYEE_DEFAULT}/enrollment/form-base`,
  CONFIG: `${API_EMPLOYEE_DEFAULT}/config`,
};

const API_PATIENT_DEFAULT = `${apiVersion}/Patient`;
export const API_PATIENT = {
  DEFAULT: API_PATIENT_DEFAULT,
  ASSIGNED_PATIENTS: `${API_PATIENT_DEFAULT}/assigned-patients`,
  SEARCH_HISTORY: `${API_PATIENT_DEFAULT}/search-history`,
  UPDATE_AVATAR: `${API_PATIENT_DEFAULT}/avatar`,
  GET_CITY_BY_ZIP: `${API_PATIENT_DEFAULT}/cities-by-zipcode`,
  CONTACT_TYPE_ITEMS: `${API_PATIENT_DEFAULT}/contact-type-items`,
  GET_PATIENT_INFO: (id) => `${API_PATIENT_DEFAULT}/${id}/address`,
  HEADER_INFO: (id) => `${API_PATIENT_DEFAULT}/${id}/header-info`,
  GET_DETAIL: (id) => `${API_PATIENT_DEFAULT}/${id}/demographics`,
  GET_PATIENT_BY_ID: (id) => `${API_PATIENT_DEFAULT}/${id}`,
};

const API_AIMS_DEFAULT = `${apiVersion}/AIMS`;
export const API_AIMS = {
  DEFAULT: API_AIMS_DEFAULT,
  GET_BASE_FORM: `${API_AIMS_DEFAULT}/aims-base`,
  PRINT_BY_ID: `${API_AIMS_DEFAULT}/html`,
};

const API_FLOW_SHEET_DEFAULT = `${apiVersion}/Flowsheet`;
export const API_FLOW_SHEET = {
  DEFAULT: API_FLOW_SHEET_DEFAULT,
  AIMS: `${API_FLOW_SHEET_DEFAULT}/aims`,
  ALLERGIES: `${API_FLOW_SHEET_DEFAULT}/allergies`,
  PROBLEMS: `${API_FLOW_SHEET_DEFAULT}/problems`,
  INJECTIONS: `${API_FLOW_SHEET_DEFAULT}/injections`,
  VITALS: `${API_FLOW_SHEET_DEFAULT}/vitals`,
  IMMUNIZATIONS: `${API_FLOW_SHEET_DEFAULT}/immunizations`,
  SOCIAL_HISTORY: `${API_FLOW_SHEET_DEFAULT}/socialhx`,
  SURGICAL_HISTORY: `${API_FLOW_SHEET_DEFAULT}/surgicalhx`,
  CONFIGURATION: `${API_FLOW_SHEET_DEFAULT}/flow-sheet-config`,
  FAMILY_HISTORY: `${API_FLOW_SHEET_DEFAULT}/familyhx`,
  APPOINTMENTS: `${API_FLOW_SHEET_DEFAULT}/appointments`,
  MEDICATIONS: `${API_FLOW_SHEET_DEFAULT}/medications`,
  PCP_MEDICATIONS: `${API_FLOW_SHEET_DEFAULT}/pcpmeds`,
  MEDICAL_HISTORY: `${API_FLOW_SHEET_DEFAULT}/medicalhx`,
  LAB_ODER: `${API_FLOW_SHEET_DEFAULT}/laborders`,
  OTHER_AGENCY: `${API_FLOW_SHEET_DEFAULT}/pcpmeds`,
  PROCEDURES: `${API_FLOW_SHEET_DEFAULT}/procedures`,
  CLINICAL_ALERT: `${API_FLOW_SHEET_DEFAULT}/clinicalalerts`,
  PMP_DATA: `${API_FLOW_SHEET_DEFAULT}/pmpdata`,
  PRINT: `${API_FLOW_SHEET_DEFAULT}/html`,
};

const API_TODO_DEFAULT = `${apiVersion}/ToDo`;
export const API_TODO = {
  PRIORITY: `${API_TODO_DEFAULT}/priorities`,
  TYPE: `${API_TODO_DEFAULT}/types`,
  TODO: `${API_TODO_DEFAULT}/todo`,
  GET_TO_DO_PRIORITIES: `${API_TODO_DEFAULT}/priorities`,
  MOVE: (todoId) => `${API_TODO_DEFAULT}/todo/${todoId}/move`,
  PIN: (todoId) => `${API_TODO_DEFAULT}/todo/${todoId}/pin`,
  UNPIN: (todoId) => `${API_TODO_DEFAULT}/todo/${todoId}/unpin`,
};

const API_PROGRESS_NOTE_DEFAULT = `${apiVersion}/ProgressNote`;
export const API_PROGRESS_NOTE = {
  DEFAULT: API_PROGRESS_NOTE_DEFAULT,
  ENCOUNTER: `${API_PROGRESS_NOTE_DEFAULT}/encounter-detail`,
  ENCOUNTER_SAVE: `${API_PROGRESS_NOTE_DEFAULT}/encounter`,
  TYPE: `${API_PROGRESS_NOTE_DEFAULT}/types`,
  POS: `${API_PROGRESS_NOTE_DEFAULT}/pos`,
  AUTO_SAVE: `${API_PROGRESS_NOTE_DEFAULT}/auto-save`,
  GET_AUTO_SAVE: `${API_PROGRESS_NOTE_DEFAULT}/encounter-autosave-detail`,
  PRESCRIPTIONS: `${API_PROGRESS_NOTE_DEFAULT}/prescriptions`,
  PRESCRIPTIONS_SEARCH: `${API_PROGRESS_NOTE_DEFAULT}/prescriptions/search`,
  PRESCRIPTIONS_PROVIDER_LOOKUP: `${API_PROGRESS_NOTE_DEFAULT}/prescriptions/providers/look-up`,
  PRESCRIPTIONS_LOOKUP: `${API_PROGRESS_NOTE_DEFAULT}/prescriptions/look-up`,
  MEDICATIONS: `${API_PROGRESS_NOTE_DEFAULT}/medications`,
  MDM_HISTORY: `${API_PROGRESS_NOTE_DEFAULT}/mdm-history`,
  NEW_PATIENT: `${API_PROGRESS_NOTE_DEFAULT}/new-patient`,
  AUTO_DETECT: `${API_PROGRESS_NOTE_DEFAULT}/service-code-auto-detect`,
  SERVICE_NAME: `${API_PROGRESS_NOTE_DEFAULT}/service-name`,
  ENCOUNTER_HISTORY_LOOKUP: `${API_PROGRESS_NOTE_DEFAULT}/note-history-lookup`,
  GET_PMP_DATA: `${API_PROGRESS_NOTE_DEFAULT}/pmp-data`,
  QUERY_PMP_DATA: `${API_PROGRESS_NOTE_DEFAULT}/query-pmp-data`,
  PSYCHIATRISTS_LOOKUP: `${API_PROGRESS_NOTE_DEFAULT}/psychiatrists-lookup`,
  HISTORY: `${API_PROGRESS_NOTE_DEFAULT}/history`,
  PRINT: `${API_PROGRESS_NOTE_DEFAULT}/html`,
  PRINT_SUMMARY: `${API_PROGRESS_NOTE_DEFAULT}/summary/html`,
  VISIT_SUMMARY: `${API_PROGRESS_NOTE_DEFAULT}/visit-summary`,
  STATUSES: `${API_PROGRESS_NOTE_DEFAULT}/statuses`,
  AMEND: `${API_PROGRESS_NOTE_DEFAULT}/amend`,
  REQUESTOR_LOOKUP: `${API_PROGRESS_NOTE_DEFAULT}/amend/requestor-lookup`,
  UPDATE_AMEND_STATUS: `${API_PROGRESS_NOTE_DEFAULT}/amend/status`,
  CARE_PLAN_PERMISSION: `${API_PROGRESS_NOTE_DEFAULT}/care-plan-permission`,
  LOAD_PREVIOUS_MSE: `${API_PROGRESS_NOTE_DEFAULT}/load-previous-mse`,
  RETRIEVE_AMENDMENT_PEND_STEPS: `${API_PROGRESS_NOTE_DEFAULT}/amend/pend-steps`,
  HIDE_SECTIONS: `${API_PROGRESS_NOTE_DEFAULT}/hide-sections`,
  COMPARE: `${API_PROGRESS_NOTE_DEFAULT}/comparison-details`,
  PROGRAM_LOOKUP: `${API_PROGRESS_NOTE_DEFAULT}/programs`,
};

const API_PROBLEM_DEFAULT = `${apiVersion}/Problem`;
export const API_PROBLEM = {
  DEFAULT: API_PROBLEM_DEFAULT,
  SNOMED_SEARCH: `${API_PROBLEM_DEFAULT}/snomed-code`,
  ACTIVE_PROBLEMS_BY_PATIENT: `${API_PROBLEM_DEFAULT}/by-patient`,
  CODES_LOOKUP: `${API_PROBLEM_DEFAULT}/code-lookup`,
  FAVORITE_PROBLEMS: `${API_PROBLEM_DEFAULT}/favourite`,
  STATUS_LOOKUP: `${API_PROBLEM_DEFAULT}/status-lookup`,
  DIAGNOSTIC_CODE: `${API_PROBLEM_DEFAULT}/diagnostic-code`,
  REMOVE_FAVORITE: `${API_PROBLEM_DEFAULT}/remove-favorites`,
  PRECEDENCES: `${API_PROBLEM_DEFAULT}/precedences`,

  //Program
  GET_PROGRAM_INDICATOR: `${API_PROBLEM_DEFAULT}/program`,
  SAVE_PROGRAM_INDICATOR: `${API_PROBLEM_DEFAULT}/program`,
  REMOVE_PROGRAM: (programHistoryId) => `${API_PROBLEM_DEFAULT}/program/${programHistoryId}`,
  PRINT_PROGRAM: `${API_PROBLEM_DEFAULT}/program/html`,
  GET_PROGRAM_INDICATOR_LOOKUPS: `${API_PROBLEM_DEFAULT}/program/lookup`,
};

const API_SETTING_DEFAULT = '/user-setting';
export const API_SETTING = {
  DEFAULT: `${API_SETTING_DEFAULT}/AxiomV2`,
  SAVE_SETTINGS: `${API_SETTING_DEFAULT}/v2-setting`,
};

const API_LAB_DEFAULT = `${apiVersion}/Lab`;
export const API_LAB = {
  DEFAULT: API_LAB_DEFAULT,
  DELETE: (commonOrderId) => `${API_LAB_DEFAULT}/${commonOrderId}`,
  PENDING: `${API_LAB_DEFAULT}/pending`,
  HISTORY: `${API_LAB_DEFAULT}/history`,
  RESULT: `${API_LAB_DEFAULT}/results`,
  ACKNOWLEDGE: `${API_LAB_DEFAULT}/acknowledge`,
  INPUT_DATA: `${API_LAB_DEFAULT}/input-data`,
  TEMPLATE: `${API_LAB_DEFAULT}/template`,
  PANEL: `${API_LAB_DEFAULT}/panels`,
  PANEL_BY_DXCODE: `${API_LAB_DEFAULT}/dxcode-panels`,
  GET_STATUS: `${API_LAB_DEFAULT}/statuses`,
  GET_MATERNAL_INPUT_DATA: `${API_LAB_DEFAULT}/input-data/maternal`,
  PRINT: `${API_LAB_DEFAULT}/lab-order/html`,
  PRINT_HIMS: `${API_LAB_DEFAULT}/hims-lab-requisition/html`,
  PRINT_REQUISTION: `${API_LAB_DEFAULT}/lab-requisition/html`,
  ASSOCIATED_FILES: `${API_LAB_DEFAULT}/associated-files`,
  ASSOCIATED_FILE: `${API_LAB_DEFAULT}/associated-file`,
  TYPE_LOOKUP: `${API_LAB_DEFAULT}/associated-file/type-lookup`,
  FILE_LOOKUP: `${API_LAB_DEFAULT}/associated-file/file-lookup`,
  SITE_LOOKUP: `${API_LAB_DEFAULT}/sites`,
  CANCEL: (commonOrderId) => `${API_LAB_DEFAULT}/cancel/${commonOrderId}`,
  GET_QUESTIONS: `${API_LAB_DEFAULT}/questions`,
  PRINT_RESULT: `${API_LAB_DEFAULT}/html`,
  PRINT_LABEL: (id) => `${API_LAB_DEFAULT}/${id}/label/html`,
  PENDING_LAB: (id) => `${API_LAB_DEFAULT}/${id}/update-status`,
  COMPARISON: `${API_LAB_DEFAULT}/comparison`,
  ATTENDING_SITES: `${API_LAB_DEFAULT}/attending-sites`,
};

const LAB_RESULT_ERRORS_DEFAULT = `${apiVersion}/lab-result-error`;
export const API_LAB_RESULT_ERRORS = {
  DEFAULT: LAB_RESULT_ERRORS_DEFAULT,
  SEARCH_PATIENT: `${LAB_RESULT_ERRORS_DEFAULT}/search-lab`,
  HIDE_RESULT: (exceptionId) => `${LAB_RESULT_ERRORS_DEFAULT}/${exceptionId}`,
};

const SDOH_DEFAULT = `${apiVersion}/SDOHForm`;
export const API_SDOH = {
  DEFAULT: SDOH_DEFAULT,
  GET_FORM: `${SDOH_DEFAULT}/html`,
  FORM_BASE: `${SDOH_DEFAULT}/sdohform-base`,
};

const API_INTERNAL_ORDER_DEFAULT = `${apiVersion}/InternalOrder`;
export const API_INTERNAL_ORDER = {
  DEFAULT: API_INTERNAL_ORDER_DEFAULT,
  UPDATE: (id) => `${API_INTERNAL_ORDER_DEFAULT}/${id}`,
  HISTORY: `${API_INTERNAL_ORDER_DEFAULT}/history`,
  PROVIDER: `${API_INTERNAL_ORDER_DEFAULT}/providers`,
  IMMUNIZATION: `${API_INTERNAL_ORDER_DEFAULT}/immunization/vaccines`,
  PEND_STEP: `${API_INTERNAL_ORDER_DEFAULT}/pend-step`,
  DESCRIPTION: `${API_INTERNAL_ORDER_DEFAULT}/descriptions`,
};

const PATIENT_DEMOGRAPHIC_DEFAULT = `${apiVersion}/PatientDemographic`;
export const PATIENT_DEMOGRAPHIC = {
  DEFAULT: PATIENT_DEMOGRAPHIC_DEFAULT,
  EMPLOYMENT_STATUSES: `${PATIENT_DEMOGRAPHIC_DEFAULT}/employment-statuses`,
  GENDER_IDENTITIES: `${PATIENT_DEMOGRAPHIC_DEFAULT}/gender-identities`,
  GENDERS: `${PATIENT_DEMOGRAPHIC_DEFAULT}/genders`,
  HISTORIES: `${PATIENT_DEMOGRAPHIC_DEFAULT}/histories`,
  MARITAL_STATUSES: `${PATIENT_DEMOGRAPHIC_DEFAULT}/marital-statuses`,
  PREFERRED_LANGUAGES: `${PATIENT_DEMOGRAPHIC_DEFAULT}/preferred-languages`,
  PREFERRED_PRONOUNS: `${PATIENT_DEMOGRAPHIC_DEFAULT}/preferred-pronouns`,
  PRIMARY_RESIDENCES: `${PATIENT_DEMOGRAPHIC_DEFAULT}/primary-residences`,
  SEXUAL_ORIENTATIONS: `${PATIENT_DEMOGRAPHIC_DEFAULT}/sexual-orientations`,
  SMOKING_STATUSES: `${PATIENT_DEMOGRAPHIC_DEFAULT}/smoking-statuses`,
  LASTEST: `${PATIENT_DEMOGRAPHIC_DEFAULT}/latest-demographic`,
  ENROLLMENT: (id = '') => `${PATIENT_DEMOGRAPHIC_DEFAULT}/enrollment/${id}`,
  HISTORY_ALL: `${PATIENT_DEMOGRAPHIC_DEFAULT}/all`,
  DELETE: (id = '') => `${PATIENT_DEMOGRAPHIC_DEFAULT}/${id}`,
  CANCEL_EXTRACT: (id = '') => `${PATIENT_DEMOGRAPHIC_DEFAULT}/${id}/cancel-extract`,
  DELAY_EXTRACT: (id = '') => `${PATIENT_DEMOGRAPHIC_DEFAULT}/${id}/delay-extract`,
  EXPIRE_EXTRACT: (id = '') => `${PATIENT_DEMOGRAPHIC_DEFAULT}/${id}/expire-extract`,
  EXTRACT: (id = '') => `${PATIENT_DEMOGRAPHIC_DEFAULT}/${id}/extract`,
  LOOKUP_DATA: `${PATIENT_DEMOGRAPHIC_DEFAULT}/lookup-data`,
  VALIDATE: `${PATIENT_DEMOGRAPHIC_DEFAULT}/validate`,
  SUBMISSION_LOOKUP: `${PATIENT_DEMOGRAPHIC_DEFAULT}/submission-interval`,
  POPULATION_LOOKUP: `${PATIENT_DEMOGRAPHIC_DEFAULT}/referral-programs`,
  PRINT: `${PATIENT_DEMOGRAPHIC_DEFAULT}/html`,
  LATEST_COMPLETED_DEMOGRAPHIC: `${PATIENT_DEMOGRAPHIC_DEFAULT}/latest-complete`,
};

const CALOCUS_DEFAULT = `${apiVersion}/child-locus`;
export const API_CALOCUS = {
  DEFAULT: CALOCUS_DEFAULT,
  DELETE: (id) => `${CALOCUS_DEFAULT}/${id}`,
  ANSWERS: `${CALOCUS_DEFAULT}/answers`,
  SAVE_BATCH: `${CALOCUS_DEFAULT}/batch`,
  BASE_FORM: `${CALOCUS_DEFAULT}/form`,
  PRINT: `${CALOCUS_DEFAULT}/html`,
  LOOKUP: `${CALOCUS_DEFAULT}/lookup-data`,
};

const LOCUS_DEFAULT = `${apiVersion}/locus`;
export const API_LOCUS = {
  DEFAULT: LOCUS_DEFAULT,
  DELETE: (id) => `${LOCUS_DEFAULT}/${id}`,
  ANSWERS: `${LOCUS_DEFAULT}/answers`,
  SAVE_BATCH: `${LOCUS_DEFAULT}/batch`,
  BASE_FORM: `${LOCUS_DEFAULT}/form`,
  PRINT: `${LOCUS_DEFAULT}/html`,
  LOOKUP: `${LOCUS_DEFAULT}/lookup-data`,
};

const SPECIALIST_REFERRAL_DEFAULT = `${apiVersion}/SpecialistReferral`;
export const API_SPECIALIST_REFERRAL = {
  DEFAULT: SPECIALIST_REFERRAL_DEFAULT,
  DELETE: (id) => `${SPECIALIST_REFERRAL_DEFAULT}/${id}`,
  CCD_GENERATION: (id) => `${SPECIALIST_REFERRAL_DEFAULT}/${id}/ccd-generation`,
  EXPIRATION: (id) => `${SPECIALIST_REFERRAL_DEFAULT}/${id}/expiration`,
  PRINT: (id) => `${SPECIALIST_REFERRAL_DEFAULT}/${id}/html`,
  PRINT_LIST: `${SPECIALIST_REFERRAL_DEFAULT}/html`,
  LOOK_UP_DATA: `${SPECIALIST_REFERRAL_DEFAULT}/lookup-data`,
  HISTORIES: `${SPECIALIST_REFERRAL_DEFAULT}/histories`,
  PROVIDERS: `${SPECIALIST_REFERRAL_DEFAULT}/providers`,
  SITES: `${SPECIALIST_REFERRAL_DEFAULT}/sites`,
};

const RADIOLOGY_DEFAULT = `${apiVersion}/RadiologyOrder`;
export const API_RADIOLOGY = {
  DEFAULT: RADIOLOGY_DEFAULT,
  CANCEL: (id) => `${RADIOLOGY_DEFAULT}/${id}/cancel`,
  PRINT: (id) => `${RADIOLOGY_DEFAULT}/${id}/html`,
  HISTORY: `${RADIOLOGY_DEFAULT}/history`,
  PROVIDERS: `${RADIOLOGY_DEFAULT}/providers`,
  INPUT_DATA: `${RADIOLOGY_DEFAULT}/input-data`,
  TEMPLATE: (id = '') => `${RADIOLOGY_DEFAULT}/template/${id}`,
  GET_DETAIL: (id = '') => `${RADIOLOGY_DEFAULT}/${id}`,
};

const PATIENT_NOTES_DEFAULT = `${apiVersion}/PatientNote`;
export const API_PATIENT_NOTES = {
  DEFAULT: PATIENT_NOTES_DEFAULT,
  IMPORTANCES: `${PATIENT_NOTES_DEFAULT}/importances`,
  TOPICS: `${PATIENT_NOTES_DEFAULT}/topics`,
};

const PENDING_PATIENT_REQUESTS_DEFAULT = `${apiVersion}/PendingPatientRequest`;
export const API_PENDING_PATIENT_REQUESTS = {
  DEFAULT: PENDING_PATIENT_REQUESTS_DEFAULT,
  FORM_TYPES: `${PENDING_PATIENT_REQUESTS_DEFAULT}/form-types`,
};

export const API_PATIENT_ALERTS = `${apiVersion}/PatientAlert`;

const CRITICALINFO_DEFAULT = `${apiVersion}/CriticalInfo`;
export const CRITICALINFO = {
  DEFAULT: CRITICALINFO_DEFAULT,
  FOLLOWUP: `${CRITICALINFO_DEFAULT}/followUp`,
  INCOMPLETE: `${CRITICALINFO_DEFAULT}/incomplete`,
  OVERDUE_UPCOMING: `${CRITICALINFO_DEFAULT}/overdueUpComing`,
  SUMMARY_OVERDUE_UPCOMING: `${CRITICALINFO_DEFAULT}/summaryOverdueUpComing`,
};

const PATIENT_CHAT_DEFAULT = `${apiVersion}/PatientChat`;
export const API_PATIENT_CHAT = {
  DEFAULT: PATIENT_CHAT_DEFAULT,
  AUDIT_LOGS: `${PATIENT_CHAT_DEFAULT}/audit-logs`,
  CONFIG: `${PATIENT_CHAT_DEFAULT}/config`,
  INFO: `${PATIENT_CHAT_DEFAULT}/info`,
  RECENT: `${PATIENT_CHAT_DEFAULT}/recent`,
};

const CARD_VIEW_VITALS = `${apiVersion}/Vitals`;
export const API_CARD_VIEW_VITALS = {
  DEFAULT: CARD_VIEW_VITALS,
  LIST: `${CARD_VIEW_VITALS}/clientVitals`,
  ADD: `${CARD_VIEW_VITALS}/addVital`,
  CURRENT: `${CARD_VIEW_VITALS}/current`,
  PRINT: `${CARD_VIEW_VITALS}/html`,
  PRINT_HISTORY: `${CARD_VIEW_VITALS}/history/html`,
};

const ALLERGY_DEFAULT = `${apiVersion}/Allergy`;

export const API_ALLERGY = {
  DEFAULT: ALLERGY_DEFAULT, //history
  ALLERGEN_LOOKUP: `${ALLERGY_DEFAULT}/allergen-lookup`,
  ALLERGEN_TYPE_LOOKUP: `${ALLERGY_DEFAULT}/allergen-type-lookup`,
  ALLERGY_REACTION_LOOKUP: `${ALLERGY_DEFAULT}/allergy-reaction-lookup`,
  ALLERGY_SEVERITY_LOOKUP: `${ALLERGY_DEFAULT}/allergy-severity-lookup`,
  ALLERGIES_CARDVIEW: `${ALLERGY_DEFAULT}/cardview`,
  ALLERGIES_AS_HTML: `${ALLERGY_DEFAULT}/html`,
  MEDICATIONS_LOOKUP: `${ALLERGY_DEFAULT}/medication-lookup`,
};

const CARD_VIEW_PROBLEM_LIST = `${apiVersion}/Problem`;
export const API_CARD_VIEW_PROBLEM_LIST = {
  DEFAULT: CARD_VIEW_PROBLEM_LIST,
  PROBLEM_BY_ID: `${CARD_VIEW_PROBLEM_LIST}/by-patient`,
  CODE_LOOKUP: `${CARD_VIEW_PROBLEM_LIST}/code-lookup`,
  DIAGNOSTIC_CODE_HISTORY: `${CARD_VIEW_PROBLEM_LIST}/diagnostic-code/histories`,
  DIAGNOSTIC_CODE: `${CARD_VIEW_PROBLEM_LIST}/diagnostic-code`,
  FAVOURITE: `${CARD_VIEW_PROBLEM_LIST}/favourite`,
  PRINT: `${CARD_VIEW_PROBLEM_LIST}/html`,
  MOVE_PROBLEM: `${CARD_VIEW_PROBLEM_LIST}/move`,
  SAVE_PATIENT_EDUCATION: `${CARD_VIEW_PROBLEM_LIST}/patient-education`,
  SAVE_PROBLEM: `${CARD_VIEW_PROBLEM_LIST}/save`,
  GET_LIST: `${CARD_VIEW_PROBLEM_LIST}/search`,
  GET_SNOMED_CODES: `${CARD_VIEW_PROBLEM_LIST}/snomed-code`,
  GET_STATUS_LOOKUP: `${CARD_VIEW_PROBLEM_LIST}/status-lookup`,
  REPORT: `${CARD_VIEW_PROBLEM_LIST}/report`,
  PRECEDENCES: `${CARD_VIEW_PROBLEM_LIST}/precedences`,
  DEMODXSAVE: `${CARD_VIEW_PROBLEM_LIST}/demo-dx-save`,
  DELETE_PROBLEM_LIST: (problemListId) => `${API_PROBLEM_DEFAULT}/${problemListId}`,
};

const PCL_DEFAULT = `${apiVersion}/PCL`;
export const API_PCL = {
  DEFAULT: PCL_DEFAULT,
  HISTORY: `${PCL_DEFAULT}/history`,
  ANSWERS: `${PCL_DEFAULT}/answers`,
  QUESTIONS: `${PCL_DEFAULT}/questions`,
  PRINT: `${PCL_DEFAULT}/batch/html`,
  PRINT_ALL: `${PCL_DEFAULT}/batches/html`,
  SAVE: `${PCL_DEFAULT}/save-pcl5`,
};

const INJECTION_DEFAULT = `${apiVersion}/Injection`;
export const API_INJECTION = {
  DEFAULT: INJECTION_DEFAULT,
  ADD_A_CLIENT_INJECTION: `${INJECTION_DEFAULT}/add-injection`,
  PATIENT_INJECTION_HISTORY: `${INJECTION_DEFAULT}/history`,
  PRINT_AN_INJECTION_INFO: `${INJECTION_DEFAULT}/html`,
  PRINT_INJECTION_HISTORY: `${INJECTION_DEFAULT}/history/html`,
  INJECTION_SITES: `${INJECTION_DEFAULT}/injection-sites`,
  INJECTION_NURSES: `${INJECTION_DEFAULT}/injection-nurses`,
  NETWORK_PROVIDERS: `${INJECTION_DEFAULT}/network-providers`,
  ATTENDING_PSYCHIATRISTS: `${INJECTION_DEFAULT}/attending-psychiatrists`,
  INJECTIONS_MEDICATION: `${INJECTION_DEFAULT}/medications`,
};

const ACE_DEFAULT = `${apiVersion}/ACE`;
export const API_ACE = {
  DEFAULT: ACE_DEFAULT,
  HISTORY: `${ACE_DEFAULT}/history`,
  ANSWERS: `${ACE_DEFAULT}/answers`,
  QUESTIONS: `${ACE_DEFAULT}/questions`,
  PRINT: `${ACE_DEFAULT}/batch/html`,
  PRINT_ALL: `${ACE_DEFAULT}/batches/html`,
  SAVE: `${ACE_DEFAULT}/save-ace`,
};

const PHQ_DEFAULT = `${apiVersion}/PHQ`;
export const API_PHQ = {
  DEFAULT: PHQ_DEFAULT,
  HISTORY: `${PHQ_DEFAULT}/history`,
  ANSWERS: `${PHQ_DEFAULT}/answer`,
  QUESTIONS: `${PHQ_DEFAULT}/questions`,
  PRINT: `${PHQ_DEFAULT}/batch/html`,
  PRINT_ALL: `${PHQ_DEFAULT}/batches/html`,
};

const AUDIT_DEFAULT = `${apiVersion}/AUDIT`;
export const API_AUDIT = {
  DEFAULT: AUDIT_DEFAULT,
  HISTORY: `${AUDIT_DEFAULT}/history`,
  QUESTIONS: `${AUDIT_DEFAULT}/questions`,
  PRINT: `${AUDIT_DEFAULT}/batch/html`,
  PRINT_ALL: `${AUDIT_DEFAULT}/batches/html`,
};

const PC_PTSD_DEFAULT = `${apiVersion}/PCPTSD`;
export const API_PC_PTSD = {
  DEFAULT: PC_PTSD_DEFAULT,
  HISTORY: `${PC_PTSD_DEFAULT}/history`,
  ANSWERS: `${PC_PTSD_DEFAULT}/answers`,
  QUESTIONS: `${PC_PTSD_DEFAULT}/questions`,
  PRINT: `${PC_PTSD_DEFAULT}/batch/html`,
  PRINT_ALL: `${PC_PTSD_DEFAULT}/batches/html`,
  SUMMARY: `${PC_PTSD_DEFAULT}/summary`,
};

const MEDICATION_DEFAULT = `${apiVersion}/Prescription`;
export const API_MEDICATION = {
  DEFAULT: MEDICATION_DEFAULT,
  LIST: `${MEDICATION_DEFAULT}/cardview`,
  DETAIL_LIST: `${MEDICATION_DEFAULT}/cardview-details`,
  COUNT_LOOKUP: `${MEDICATION_DEFAULT}/count-lookup`,
  FORM_LOOKUP: `${MEDICATION_DEFAULT}/dispensible-form-lookup`,
  PRINT: `${MEDICATION_DEFAULT}/html`,
  INTERVAL_LOOKUP: `${MEDICATION_DEFAULT}/interval-lookup`,
  PHARMACIES_LOOKUP: `${MEDICATION_DEFAULT}/pharmacies-lookup`,
  PROVIDER_DEA_NUMBER_LOOKUP: `${MEDICATION_DEFAULT}/provider-dea-number-lookup`,
  PROVIDER_LOOKUP: `${MEDICATION_DEFAULT}/providers-lookup`,
  SITES_LOOKUP: `${MEDICATION_DEFAULT}/sites-lookup`,
  TARGET_SYMPTOM_LOOKUP: `${MEDICATION_DEFAULT}/target-symptom-lookup`,
  REACTION: `${MEDICATION_DEFAULT}/reaction-html`,
  PRESCRIPTIONS_LOOKUP: `${MEDICATION_DEFAULT}/prescriptions-lookup`,
  RX_HISTORY: `${MEDICATION_DEFAULT}/rx-history`,
  SIG_HISTORY: `${MEDICATION_DEFAULT}/sig`,
  UPDATE_SIG: (id) => `${MEDICATION_DEFAULT}/sig/${id}`,
  ACTION_LOOKUP: `${MEDICATION_DEFAULT}/action-lookup`,
  ROUTE_LOOKUP: `${MEDICATION_DEFAULT}/route-lookup`,
  ORDER_SET: `${MEDICATION_DEFAULT}/orderset`,
  DISCONTINUE: `${MEDICATION_DEFAULT}/discontinue`,
  DISCONTINUE_LIST: `${MEDICATION_DEFAULT}/discontinue-list`,
  DISCONTINUE_REASONS_LOOKUP: `${MEDICATION_DEFAULT}/discontinue-reasons-lookup`,
  NDC_LOOKUP: `${MEDICATION_DEFAULT}/ndc-lookup`,
  CATEGORY_LOOKUP: `${MEDICATION_DEFAULT}/category-lookup`,
  PHARMACIES_INFO: `${MEDICATION_DEFAULT}/pharmacy-info`,
  FAX_DISCONTINUE: `${MEDICATION_DEFAULT}/fax`,
  PRINT_MEDICATION: `${MEDICATION_DEFAULT}/rx-print-copy/html`,
  MEDICATION_VALIDATION: `${MEDICATION_DEFAULT}/validation`,
  PATIENT_EDUCATION: `${MEDICATION_DEFAULT}/patient-education`,
  LEXICOMP_PAMPHLET: `${MEDICATION_DEFAULT}/lexicom-pamphlet`,
  PSYCH_SPI_DETAIL: `${MEDICATION_DEFAULT}/get-psych-spi`,
  POTENCY_UNIT_LOOKUP: `${MEDICATION_DEFAULT}/potency-unit-lookup`,
  FAVORITE: (id = '') => `${MEDICATION_DEFAULT}/favorite/${id}`,
};

const API_EMPLOYEE_PRODUCTIVITY_DEFAULT = `${apiVersion}/EmployeeProductivity`;
export const API_EMPLOYEE_PRODUCTIVITY = {
  DEFAULT: API_EMPLOYEE_PRODUCTIVITY_DEFAULT,
  UPDATE_ADJUSTMENT: `${API_EMPLOYEE_PRODUCTIVITY_DEFAULT}/adjustments`,
  GET_ADJUSTMENTS: (employeeId) =>
    `${API_EMPLOYEE_PRODUCTIVITY_DEFAULT}/employee/${employeeId}/adjustments`,
};

const API_DYNAMIC_PROGRESS_NOTE_DEFAULT = `${apiVersion}/DynamicNote`;
export const API_DYNAMIC_PROGRESS_NOTE = {
  DEFAULT: API_DYNAMIC_PROGRESS_NOTE_DEFAULT,
  GET_NOTE_FORM: `${API_DYNAMIC_PROGRESS_NOTE_DEFAULT}/all`,
  ANSWER: `${API_DYNAMIC_PROGRESS_NOTE_DEFAULT}/answer`,
  SAVE: `${API_DYNAMIC_PROGRESS_NOTE_DEFAULT}/answer`,
  AUTO_SAVE: `${API_DYNAMIC_PROGRESS_NOTE_DEFAULT}/auto-save`,
  CATEGORIES: `${API_DYNAMIC_PROGRESS_NOTE_DEFAULT}/categories`,
  INPUT_TYPES: `${API_DYNAMIC_PROGRESS_NOTE_DEFAULT}/input-types`,
  ROS_MENTAL_STATUSES: `${API_DYNAMIC_PROGRESS_NOTE_DEFAULT}/input-types/ros`,
  NON_BILLABLE_POS: `${API_DYNAMIC_PROGRESS_NOTE_DEFAULT}/non-billable-place-of-services`,
  NON_BILLABLE_SERVICE_CODES: `${API_DYNAMIC_PROGRESS_NOTE_DEFAULT}/non-billable-service-codes`,
  LIST_OF_POS: `${API_DYNAMIC_PROGRESS_NOTE_DEFAULT}/place-of-services`,
  LIST_OF_IDENTIFIED_NEEDS: `${API_DYNAMIC_PROGRESS_NOTE_DEFAULT}/identified-needs`,
  PROVIDER_SITES: `${API_DYNAMIC_PROGRESS_NOTE_DEFAULT}/provider-sites`,
  PROVIDERS: `${API_DYNAMIC_PROGRESS_NOTE_DEFAULT}/providers`,
  ROLES: `${API_DYNAMIC_PROGRESS_NOTE_DEFAULT}/roles`,
  SERVICE_CODES: `${API_DYNAMIC_PROGRESS_NOTE_DEFAULT}/service-codes`,
  STATUSES: `${API_DYNAMIC_PROGRESS_NOTE_DEFAULT}/statuses`,
  VIEWERS: `${API_DYNAMIC_PROGRESS_NOTE_DEFAULT}/viewers`,
  ROS: `${API_DYNAMIC_PROGRESS_NOTE_DEFAULT}/ros`,
  PREVIEW: `${API_DYNAMIC_PROGRESS_NOTE_DEFAULT}/preview`,
  HTML: `${API_DYNAMIC_PROGRESS_NOTE_DEFAULT}/html`,
};

const API_ASAM_DEFAULT = `${apiVersion}/ASAM`;
export const API_ASAM = {
  DEFAULT: API_ASAM_DEFAULT,
  UPDATE_ASAM: (id) => `${API_ASAM_DEFAULT}/${id}`,
  RECOMMENDED_LOC: `${API_ASAM_DEFAULT}/recommended-loc`,
  RISKS: `${API_ASAM_DEFAULT}/risks`,
  COMPLETION_STATUSES: `${API_ASAM_DEFAULT}/completion-statuses`,
  PRINT: `${API_ASAM_DEFAULT}/html`,
  REVIEWER_LOOKUP: `${API_ASAM_DEFAULT}/reviewers`,
  GET_DETAIL_BY_ID: (id) => `${API_ASAM_DEFAULT}/${id}`,
  CONTINUUM: `${API_ASAM_DEFAULT}/continuum`,
  CONTINUUM_ASSESSMENT_TYPES: `${API_ASAM_DEFAULT}/continuum/assessment-types`,
  CONTINUUM_ASSESSMENT_EDIT: `${API_ASAM_DEFAULT}/continuum/edit-url`,
  CONTINUUM_HAS_ACCOUNT: `${API_ASAM_DEFAULT}/continuum/has-account`,
  CONTINUUM_JSON: `${API_ASAM_DEFAULT}/continuum/json`,
  CONTINUUM_PRINT: `${API_ASAM_DEFAULT}/continuum/print-url`,
  CONTINUUM_REPORT: `${API_ASAM_DEFAULT}/continuum/report-url`,
  CONTINUUM_DATA: `${API_ASAM_DEFAULT}/continuum/assessment-data`,
};

export const API_SIGNATURE = `${apiVersion}/Signature`;
export const API_SIGNATURES = {
  TYPE: `${API_SIGNATURE}/types/all`,
};

const API_PENDING_NOTIFICATION_DEFAULT = `${apiVersion}/PendingNotification`;
export const API_PENDING_NOTIFICATION = {
  DEFAULT: API_PENDING_NOTIFICATION_DEFAULT,
  SECTIONS: `${API_PENDING_NOTIFICATION_DEFAULT}/sections`,
  SUMMARY_DATA: `${API_PENDING_NOTIFICATION_DEFAULT}/summary`,
};

const API_PATIENT_ATTRIBUTE_DEFAULT = `${apiVersion}/PatientAttribute`;
export const API_PATIENT_ATTRIBUTE = {
  DEFAULT: API_PATIENT_ATTRIBUTE_DEFAULT,
  GET: `${API_PATIENT_ATTRIBUTE_DEFAULT}/patient-attributes`,
  UPDATE: `${API_PATIENT_ATTRIBUTE_DEFAULT}/update`,
  GET_DYNAMIC_ATTRIBUTES: `${API_PATIENT_ATTRIBUTE_DEFAULT}/dynamic-attributes`,
  GET_ICONS: `${API_PATIENT_ATTRIBUTE_DEFAULT}/icons`,
  ADD_ICON: `${API_PATIENT_ATTRIBUTE_DEFAULT}/icon`,
};

const API_ENROLLMENT_DEFAULT = `${apiVersion}/Enrollment`;
export const API_ENROLLMENT = {
  GRID: `${API_ENROLLMENT_DEFAULT}/grid`,
  GRID_TYPES: `${API_ENROLLMENT_DEFAULT}/types`,
  GRID_STATUS: `${API_ENROLLMENT_DEFAULT}/grid-status`,
};

const API_PCP_NOTE_DEFAULT = `${apiVersion}/PcpNote`;
export const API_PCP_NOTE = {
  DETAIL: API_PCP_NOTE_DEFAULT,
  SAVE_PCP_NOTE_EXAM_TEMPLATE: `${API_PCP_NOTE_DEFAULT}/exam-template`,
  SERVICES: `${API_PCP_NOTE_DEFAULT}/services`,
  AUTO_SAVE: `${API_PCP_NOTE_DEFAULT}/auto-save`,
  PRINT: `${API_PCP_NOTE_DEFAULT}/html`,
};

const API_EMAR_DEFAULT = `${apiVersion}/EMar`;
export const API_EMAR = {
  DEFAULT: API_EMAR_DEFAULT,
  ALLERGIES: `${API_EMAR_DEFAULT}/allergies`,
  PRINTOUT: `${API_EMAR_DEFAULT}/html`,
  GET_ADMINISTERED: `${API_EMAR_DEFAULT}/administered-medications`,
  SAVE_ADMINISTERED: `${API_EMAR_DEFAULT}/administered`,
};

const CM3_DEFAULT = `${apiVersion}/CM3Note`;
export const API_CM3 = {
  DEFAULT: CM3_DEFAULT,
  GET_CM3_BY_ID: (id) => `${CM3_DEFAULT}/${id}`,
  BASE_ENCOUNTER: `${CM3_DEFAULT}/base-encounter`,
  GET_CONTACTS: `${CM3_DEFAULT}/contacts`,
  GET_DIAGNOSIS: `${CM3_DEFAULT}/diagnosis`,
  GET_PATIENT_GROUP: `${CM3_DEFAULT}/groups`,
  GET_IDENTIFIED_NEEDS: `${CM3_DEFAULT}/identified-needs-lookup`,
  GET_SERVICE_SITES: `${CM3_DEFAULT}/serivce-sites-lookup`,
  GET_SERVICE_TYPES: `${CM3_DEFAULT}/service-types-lookup`,
  GET_SERVICES: `${CM3_DEFAULT}/services-lookup`,
  AUTO_SAVE: `${CM3_DEFAULT}/auto-save`,
  GET_CM3_AUTO_SAVE_BY_ID: `${CM3_DEFAULT}/encounter-autosave-detail`,
  PRINT: `${CM3_DEFAULT}/html`,
  GET_ORG_DATA_CONFIG: `${CM3_DEFAULT}/org-data-config`,
};

const SERVICE_PLAN_DEFAULT = `${apiVersion}/ServicePlan`;
export const API_SERVICE_PLAN = {
  DEFAULT: SERVICE_PLAN_DEFAULT,
  GET_SERVICE_PLAN: (servicePlanId) => `${SERVICE_PLAN_DEFAULT}/${servicePlanId}`,
  COMPLETION_STATUSES: `${SERVICE_PLAN_DEFAULT}/completion-statuses`,
  SECTIONS: (typeId) => `${SERVICE_PLAN_DEFAULT}/rendering-input/${typeId}`,
  SERVICE_CODES: (serviceCategoryId) => `${SERVICE_PLAN_DEFAULT}/service-code/${serviceCategoryId}`,
  TYPES: `${SERVICE_PLAN_DEFAULT}/service-plan-type`,
  RECENT: (patientId) => `${SERVICE_PLAN_DEFAULT}/recent/${patientId}`,
  TELEPHONE_CONSENT_QUALIFICATIONS: `${SERVICE_PLAN_DEFAULT}/telephone-consent-qualifications`,
  AGREEMENT_SECTION: `${SERVICE_PLAN_DEFAULT}/agreement-section`,
  PEND_STEP: `${SERVICE_PLAN_DEFAULT}/pend-step`,
  PRINT: (servicePlanId) => `${SERVICE_PLAN_DEFAULT}/${servicePlanId}/html`,
  REOPEN: (servicePlanId) => `${SERVICE_PLAN_DEFAULT}/${servicePlanId}/reopen`,
  LIST_SERVICE_PLAN_BY_TYPE_ID_AND_PLAN_DATE: `${SERVICE_PLAN_DEFAULT}/by-type-and-date`,
  LIST_SERVICE_PLAN_CAN_BE_REOPEN: `${SERVICE_PLAN_DEFAULT}/can-be-reopened-ids`,
  DELETE_SERVICE_PLAN_SIGNATURE: (bhspSignatureId) =>
    `${SERVICE_PLAN_DEFAULT}/signatures/${bhspSignatureId}`,
  GET_PREVIOUS_SERVICE_PLAN: `${SERVICE_PLAN_DEFAULT}/previous-service-plan`,
};

const SERVICE_PLAN_SUPPORT_DEFAULT = `${apiVersion}/service-plan/support`;
export const API_SERVICE_PLAN_SUPPORT = {
  DEFAULT: SERVICE_PLAN_SUPPORT_DEFAULT,
};

const CRISIS_PLAN_DEFAULT = `${apiVersion}/CrisisPlan`;
export const API_CRISIS_PLAN = {
  DEFAULT: CRISIS_PLAN_DEFAULT,
  CHILD_HISTORY: `${CRISIS_PLAN_DEFAULT}/child`,
  GET_PLAN_BY_ID: (crisisPlanId) => `${CRISIS_PLAN_DEFAULT}/${crisisPlanId}`,
  PRINT_CHILD_PLAN: `${CRISIS_PLAN_DEFAULT}/child/html`,
  GET_PLAN_STATUSES: `${CRISIS_PLAN_DEFAULT}/pend-steps`,
  PRINT_PLAN: `${CRISIS_PLAN_DEFAULT}/html`,
  GET_TOD: `${CRISIS_PLAN_DEFAULT}/tod`,
  DELETE: (crisisPlanId) => `${CRISIS_PLAN_DEFAULT}/${crisisPlanId}`,
};

const SUPPORT_SAFETY_PLAN = `${apiVersion}/SupportSafetyPlan`;
export const API_SUPPORT_SAFETY_PLAN = {
  DEFAULT: SUPPORT_SAFETY_PLAN,
  GET_PLAN_BY_ID: (crisisPlanId) => `${SUPPORT_SAFETY_PLAN}/${crisisPlanId}`,
  GET_CRISIS_LOCATION: `${SUPPORT_SAFETY_PLAN}/crisis-locations`,
  GET_HISTORY: `${SUPPORT_SAFETY_PLAN}/history`,
  GET_PLAN_STATUSES: `${SUPPORT_SAFETY_PLAN}/pend-steps`,
  PRINT_PLAN: `${SUPPORT_SAFETY_PLAN}/html`,
  GET_POTENTIAL_SPECIAL_NEEDS: `${SUPPORT_SAFETY_PLAN}/potential-special-needs`,
};

const BATCH_837_DEFAULT = `${apiVersion}/rcm/batch-837`;
export const API_BATCH_837 = {
  DEFAULT: BATCH_837_DEFAULT,
  BATCH_ENTRY: `${BATCH_837_DEFAULT}/batch-entry`,
  LIST: `${BATCH_837_DEFAULT}/list`,
  VALIDATION: `${BATCH_837_DEFAULT}/validation`,
  PAYORS_LOOKUP: `${BATCH_837_DEFAULT}/payors-lookup`,
  BATCH_TYPE: `${BATCH_837_DEFAULT}/batch-type-lookup`,
  BILLING_TYPE: `${BATCH_837_DEFAULT}/billing-type-lookup`,
  SITES_LOOKUP: `${BATCH_837_DEFAULT}/sites-lookup`,
  POPULATIONS_LOOKUP: `${BATCH_837_DEFAULT}/populations-lookup`,
  SERVICES_LOOKUP: `${BATCH_837_DEFAULT}/services-lookup`,
  RENDERING_PROVIDER_LOOKUP: `${BATCH_837_DEFAULT}/rendering-provider-lookup`,
  VALIDATE_ONLY: `${BATCH_837_DEFAULT}/validate-only`,
  RESET_VALIDATION: `${BATCH_837_DEFAULT}/reset-validations`,
  OVERVIEW_FILE: `${BATCH_837_DEFAULT}/overview-file`,
  MARK_SENT: `${BATCH_837_DEFAULT}/mark-sent`,
  UNMARK_SENT: `${BATCH_837_DEFAULT}/unmark-sent`,
  HIPAA: `${BATCH_837_DEFAULT}/clm-file`,
  HIPAA_CONTENT: `${BATCH_837_DEFAULT}/hipaa`,
  DOWNLOAD_DROPPED: `${BATCH_837_DEFAULT}/dropped`,
  VALIDATIONS: `${BATCH_837_DEFAULT}/data-validations`,
  PRE_VALIDATE: `${BATCH_837_DEFAULT}/pre-validate`,
  REMOVE_VALIDATIONS: (id) => `${BATCH_837_DEFAULT}/${id}/remove-data-validations`,
};

const BATCH_INVOICE_DEFAULT = `${apiVersion}/rcm/batch-invoice`;
export const API_BATCH_INVOICE = {
  DEFAULT: BATCH_INVOICE_DEFAULT,
  LIST: `${BATCH_INVOICE_DEFAULT}/list`,
  VALIDATION: `${BATCH_INVOICE_DEFAULT}/validation`,
  PAYORS_LOOKUP: `${BATCH_INVOICE_DEFAULT}/payors-lookup`,
  VALIDATE_ONLY: `${BATCH_INVOICE_DEFAULT}/validate-only`,
  RESET_VALIDATION: `${BATCH_INVOICE_DEFAULT}/reset-validations`,
  OVERVIEW_FILE: `${BATCH_INVOICE_DEFAULT}/overview-file`,
  MARK_SENT: `${BATCH_INVOICE_DEFAULT}/mark-sent`,
  UNMARK_SENT: `${BATCH_INVOICE_DEFAULT}/unmark-sent`,

  BATCH_ENTRY: `${BATCH_INVOICE_DEFAULT}/batch-entry`,
  BATCH_TYPE: `${BATCH_INVOICE_DEFAULT}/batch-type-lookup`,
  SITES_LOOKUP: `${BATCH_INVOICE_DEFAULT}/sites-lookup`,
  DOWNLOAD_FILE: `${BATCH_INVOICE_DEFAULT}/invoice-file`,
};

const RECONCILE_277 = `${apiVersion}/rcm/reconcile-277`;
export const API_RECONCILE_277 = {
  DEFAULT: RECONCILE_277,
  CLAIM_STATUSES: `${RECONCILE_277}/277-statuses`,
  GET_CLAIM_SUBMISSIONS: `${RECONCILE_277}/claim-submissions`,
  HISTORY: `${RECONCILE_277}/history`,
  RESEND: `${RECONCILE_277}/resend`,
  RESEND_STATUSES: `${RECONCILE_277}/resend-statuses`,
  REQUEST_HISTORY: `${RECONCILE_277}/resubmission-requests`,
  GET_277_HISTORY: `${RECONCILE_277}/277-history`,
  GET_835_REMIT_HISTORY: `${RECONCILE_277}/835-remit-history`,
};

const BATCH_HCFA_DEFAULT = `${apiVersion}/rcm/batch-hcfa`;
export const API_BATCH_HCFA = {
  DEFAULT: BATCH_HCFA_DEFAULT,
  LIST: `${BATCH_HCFA_DEFAULT}/list`,
  VALIDATION: `${BATCH_HCFA_DEFAULT}/validation`,
  PAYORS_LOOKUP: `${BATCH_HCFA_DEFAULT}/payors-lookup`,
  VALIDATE_ONLY: `${BATCH_HCFA_DEFAULT}/validate-only`,
  RESET_VALIDATION: `${BATCH_HCFA_DEFAULT}/reset-validations`,
  OVERVIEW_FILE: `${BATCH_HCFA_DEFAULT}/overview-file`,
  MARK_SENT: `${BATCH_HCFA_DEFAULT}/mark-sent`,
  UNMARK_SENT: `${BATCH_HCFA_DEFAULT}/unmark-sent`,
  COLORS_LOOKUP: `${BATCH_HCFA_DEFAULT}/colors-lookup`,
  DOWNLOAD_HIPAA: `${BATCH_HCFA_DEFAULT}/hipaa-file`,

  BATCH_ENTRY: `${BATCH_HCFA_DEFAULT}/batch-entry`,
  BATCH_TYPE: `${BATCH_HCFA_DEFAULT}/batch-type-lookup`,
  BILLING_TYPE: `${BATCH_HCFA_DEFAULT}/billing-type-lookup`,
  POPULATIONS_LOOKUP: `${BATCH_HCFA_DEFAULT}/populations-lookup`,
  SITES_LOOKUP: `${BATCH_HCFA_DEFAULT}/sites-lookup`,
  SERVICES_LOOKUP: `${BATCH_HCFA_DEFAULT}/services-lookup`,
  VALIDATIONS: `${BATCH_HCFA_DEFAULT}/data-validations`,
  REMOVE_VALIDATIONS: (id) => `${BATCH_837_DEFAULT}/${id}/remove-data-validations`,
};

const CRISIS_PREVENTION_PLAN = `${apiVersion}/CrisisPreventionPlan`;
export const API_PREVENTION_PLAN = {
  DEFAULT: CRISIS_PREVENTION_PLAN,
  GET_PLAN_BY_ID: (id) => `${CRISIS_PREVENTION_PLAN}/${id}`,
  GET_STATUSES: `${CRISIS_PREVENTION_PLAN}/completion-statuses`,
  PRINT: `${CRISIS_PREVENTION_PLAN}/html`,
};

const SNCD = `${apiVersion}/SNCD`;
export const API_SNCD_PLAN = {
  DEFAULT: SNCD,
  HISTORY: `${SNCD}/history`,
  PRINT: `${SNCD}/html`,
  GET_REVIEWERS: `${SNCD}/reviewers`,
  GET_INPUT_HISTORY: `${SNCD}/input-history`,
  GET_STATUSES: `${SNCD}/statuses`,
};

const API_RECONCILE_835_DEFAULT = `${apiVersion}/rcm/batch-835`;
export const API_RECONCILE_835 = {
  DEFAULT: API_RECONCILE_835_DEFAULT,
  BATCH_HISTORY: `${API_RECONCILE_835_DEFAULT}/history`,
  BATCH_PAYOR: `${API_RECONCILE_835_DEFAULT}/payors`,
  CLAIM_STATUSES: `${API_RECONCILE_835_DEFAULT}/claim-statuses`,
  ADJUSTMENT_CODES: `${API_RECONCILE_835_DEFAULT}/adjustment-codes`,
  REMARK_CODES: `${API_RECONCILE_835_DEFAULT}/remark-codes`,
  POPULATIONS: `${API_RECONCILE_835_DEFAULT}/populations`,
  RESEND_STATUSES: `${API_RECONCILE_835_DEFAULT}/resend-statuses`,
  PROVIDERS_LOOKUP: `${API_RECONCILE_835_DEFAULT}/providers`,
  GET_CLAIM_SUBMISSIONS: `${API_RECONCILE_835_DEFAULT}/claim-submissions-detail`,
  GET_CLAIM_SUBMISSIONS_INFO: `${API_RECONCILE_835_DEFAULT}/claim-submissions-information`,
  GET_MATCHING_CLAIMS: `${API_RECONCILE_835_DEFAULT}/matching-claims`,
  RESUBMISSION_STATUSES: `${API_RECONCILE_835_DEFAULT}/resend`,
  ADJUSTMENT_HISTORY: `${API_RECONCILE_835_DEFAULT}/adjudication-history`,
  CLAIM_SUBMISSIONS_DETAIL: `${API_RECONCILE_835_DEFAULT}/claim-submissions-detail`,
  BATCH_AUDIT_HISTORY: `${API_RECONCILE_835_DEFAULT}/batch-history`,
  DOWNLOAD_BATCH: `${API_RECONCILE_835_DEFAULT}/spreadsheet-file`,
  CHARGE_PATIENT: `${API_RECONCILE_835_DEFAULT}/charge-to-patient`,
};

const API_RCM_PAYOR_ASSIGNMENT_DEFAULT = `${apiVersion}/rcm/payor-assignment`;
export const API_RCM_PAYOR_ASSIGNMENT = {
  DEFAULT: API_RCM_PAYOR_ASSIGNMENT_DEFAULT,
  INPUT_DATA: `${API_RCM_PAYOR_ASSIGNMENT_DEFAULT}/input-data`,
  ASSOCIATED_FILE: `${API_RCM_PAYOR_ASSIGNMENT_DEFAULT}/associated-file`,
  ASSOCIATED_FILES: `${API_RCM_PAYOR_ASSIGNMENT_DEFAULT}/associated-files`,
  FILE_LOOKUP: `${API_RCM_PAYOR_ASSIGNMENT_DEFAULT}/associated-file/file-lookup`,
  TYPE_LOOKUP: `${API_RCM_PAYOR_ASSIGNMENT_DEFAULT}/associated-file/type-lookup`,
  SERVICE_CODES: `${API_RCM_PAYOR_ASSIGNMENT_DEFAULT}/service-codes`,
  SERVICE_TYPES: `${API_RCM_PAYOR_ASSIGNMENT_DEFAULT}/service-types`,
  DELETE_ASSOCIATED_FILE: (clientFileAssociationId) =>
    `${API_RCM_PAYOR_ASSIGNMENT_DEFAULT}/associated-file/${clientFileAssociationId}`,
};

const API_PAYOR_EDITOR_DEFAULT = `${apiVersion}/PayorEditor`;
export const API_PAYOR_EDITOR = {
  DEFAULT: API_PAYOR_EDITOR_DEFAULT,
  GET_CLASSIFICATIONS: (id) => `${API_PAYOR_EDITOR_DEFAULT}/${id}/classification`,
  GET_ENROLLMENT_CODES: (id) => `${API_PAYOR_EDITOR_DEFAULT}/${id}/enrollment-code`,
  GET_PROGRAM_INDICATOR: (id) => `${API_PAYOR_EDITOR_DEFAULT}/${id}/program-indicator`,
  EXPIRE_CLASSIFICATIONS: (id) => `${API_PAYOR_EDITOR_DEFAULT}/classification/${id}`,
  EXPIRE_ENROLLMENT_CODES: (id) => `${API_PAYOR_EDITOR_DEFAULT}/enrollment-code/${id}`,
  EXPIRE_PROGRAM_INDICATOR: (id) => `${API_PAYOR_EDITOR_DEFAULT}/program-indicator/${id}`,
  GET_CLASSIFICATIONS_LOOKUPS: `${API_PAYOR_EDITOR_DEFAULT}/classification/lookup`,
  GET_ENROLLMENT_CODES_LOOKUPS: `${API_PAYOR_EDITOR_DEFAULT}/enrollment-code/lookup`,
  GET_PROGRAM_INDICATOR_LOOKUPS: `${API_PAYOR_EDITOR_DEFAULT}/program-indicator/lookup`,
  DOWNLOAD: `${API_PAYOR_EDITOR_DEFAULT}/download`,
  INPUT_DATA: `${API_PAYOR_EDITOR_DEFAULT}/input-data`,
  SEARCH_ZIPCODE: `${API_PAYOR_EDITOR_DEFAULT}/zipcode-info`,
};

const API_BATCH_834_DEFAULT = `${apiVersion}/rcm/batch-834`;
export const API_BATCH_834 = {
  DEFAULT: API_BATCH_834_DEFAULT,
  PAYORS_LOOKUP: `${API_BATCH_834_DEFAULT}/payors-lookup`,
  EXTRACT_TYPES_LOOKUP: `${API_BATCH_834_DEFAULT}/extract-types-lookup`,
  BATCH_ENTRY: `${API_BATCH_834_DEFAULT}/batch-entry`,
  LIST: `${API_BATCH_834_DEFAULT}/list`,
  EXTRACT_REQUEST: `${API_BATCH_834_DEFAULT}/extract-request`,
  EXTRACT_REQUESTS_LOOKUP: `${API_BATCH_834_DEFAULT}/extract-requests-lookup`,
  EXTRACT_REASONS_LOOKUP: `${API_BATCH_834_DEFAULT}/extract-reason-lookup`,
  AUDITS: `${API_BATCH_834_DEFAULT}/audits`,
  DELAY: `${API_BATCH_834_DEFAULT}/delay`,
  DELAY_ACTIONS: `${API_BATCH_834_DEFAULT}/delay-action-lookup`,
  REMOVE: `${API_BATCH_834_DEFAULT}/remove`,
  REMOVE_ACTIONS: `${API_BATCH_834_DEFAULT}/remove-action-lookup`,
  OVERRIDE_DATE_EXTRACT: `${API_BATCH_834_DEFAULT}/date-extract`,
};

const API_RCM_FEE_SCHEDULE_DEFAULT = `${apiVersion}/rcm/fee-schedule`;
export const API_RCM_FEE_SCHEDULE = {
  DEFAULT: API_RCM_FEE_SCHEDULE_DEFAULT,
  COPY_SERVICE_MATRIX: `${API_RCM_FEE_SCHEDULE_DEFAULT}/copy-sevice-matrix`,
  PAYORS_LOOKUP: `${API_RCM_FEE_SCHEDULE_DEFAULT}/payors-lookup`, //funding category
  PROVIDER_TYPES_LOOKUP: `${API_RCM_FEE_SCHEDULE_DEFAULT}/provider-types-lookup`,
  SERVICE_CATEGORIES_LOOKUP: `${API_RCM_FEE_SCHEDULE_DEFAULT}/service-categories-lookup`,
  SERVICE_MATRIX: `${API_RCM_FEE_SCHEDULE_DEFAULT}/service-matrix`,
  SERVICES: `${API_RCM_FEE_SCHEDULE_DEFAULT}/services`,
  STATE_REPORTING_LOOKUP: `${API_RCM_FEE_SCHEDULE_DEFAULT}/state-reporting-lookup`,
  STATUS_LOOKUP: `${API_RCM_FEE_SCHEDULE_DEFAULT}/status-lookup`,
  DOWNLOAD_SERVICE_MATRIX: `${API_RCM_FEE_SCHEDULE_DEFAULT}/download-service-matrix`,
  TITLES_LOOKUP: `${API_RCM_FEE_SCHEDULE_DEFAULT}/titles-lookup`,
  POPULATIONS_LOOKUP: `${API_RCM_FEE_SCHEDULE_DEFAULT}/populations-lookup`,
  PLACE_OF_SERVICE_LOOKUP: `${API_RCM_FEE_SCHEDULE_DEFAULT}/pos-lookup`,
  OVERRIDE: `${API_RCM_FEE_SCHEDULE_DEFAULT}/override`,
  QUALIFICATION: `${API_RCM_FEE_SCHEDULE_DEFAULT}/services/qualifications`,
  QUALIFICATIONS_LOOKUP: `${API_RCM_FEE_SCHEDULE_DEFAULT}/qualifications-lookup`,
  CROSS_REFERENCE_LOOKUP: `${API_RCM_FEE_SCHEDULE_DEFAULT}/bundle-codes-lookup`, // bundle code
  BUNDLE_PROVIDERS_LOOKUP: `${API_RCM_FEE_SCHEDULE_DEFAULT}/bundle-providers-lookup`,
  UNIT_OF_MEASURES_LOOKUP: `${API_RCM_FEE_SCHEDULE_DEFAULT}/unit-of-measures-lookup`,
};

const API_ENCOUNTER_DEFAULT = `${apiVersion}/rcm/encounter`;
export const API_ENCOUNTER = {
  DEFAULT: API_ENCOUNTER_DEFAULT,
  SERVICE_POS_RATE: `${API_ENCOUNTER_DEFAULT}/service-pos-rate`,
  TYPES_LOOKUP: `${API_ENCOUNTER_DEFAULT}/encounter-types`,
  PROVIDES_LOOKUP: `${API_ENCOUNTER_DEFAULT}/providers`,
  PAYORS_LOOKUP: `${API_ENCOUNTER_DEFAULT}/payors`,
  SERVICES_LOOKUP: `${API_ENCOUNTER_DEFAULT}/service-codes`,
  GET_BATCH_HISTORY: `${API_ENCOUNTER_DEFAULT}/batch-history`,
  CLAIM_SUBMISSIONS_DETAIL: `${API_ENCOUNTER_DEFAULT}/claim-submission-detail`,
  SERVICES_BY_FORM: `${API_ENCOUNTER_DEFAULT}/service-codes-by-form`,
  SERVICES: `${API_ENCOUNTER_DEFAULT}/encounter-service`,
  RESUBMISSION_REQUESTS: `${API_ENCOUNTER_DEFAULT}/resubmission-requests`,
  ADJUSTMENTS_REASONS_LOOKUP: `${API_ENCOUNTER_DEFAULT}/adjustment-reasons`,
  ADJUSTMENTS: `${API_ENCOUNTER_DEFAULT}/adjustments`,
  PEND_REASONS_LOOKUP: `${API_ENCOUNTER_DEFAULT}/pend-reasons`,
  PENDS: `${API_ENCOUNTER_DEFAULT}/pends`,
  DENIAL_CODES_LOOKUP: `${API_ENCOUNTER_DEFAULT}/denial-codes`,
  ADJUDICATION_HISTORY: `${API_ENCOUNTER_DEFAULT}/adjudication`,
  WRITE_OFF_REASONS_LOOKUP: `${API_ENCOUNTER_DEFAULT}/write-off-reasons`,
  WRITE_OFF_TYPES_LOOKUP: `${API_ENCOUNTER_DEFAULT}/write-off-types`,

  ACTIVE_EMPLOYEE: `${API_ENCOUNTER_DEFAULT}/active-employee`,

  DENITAL_HISTORY: (encounterId, serviceId) =>
    `${API_ENCOUNTER_DEFAULT}/${encounterId}/services/${serviceId}/denials`,
  DELETE_DENIAL: (encounterId, serviceId, denialId, denialSeq) =>
    `${API_ENCOUNTER_DEFAULT}/${encounterId}/services/${serviceId}/denials/${denialId}/seq/${denialSeq}`,
  SERVICE_DETAIL: (encounterId, serviceId) =>
    `${API_ENCOUNTER_DEFAULT}/${encounterId}/services/${serviceId}`,
  GET_EDIT_AUDIT_HISTORY: (encounterId, serviceId) =>
    `${API_ENCOUNTER_DEFAULT}/${encounterId}/services/${serviceId}/edit-audit-history`,
  CROSS_CLAIM_RECONCILIATION: (encounterId, serviceId) =>
    `${API_ENCOUNTER_DEFAULT}/${encounterId}/services/${serviceId}/crossover-claim-reconciliation`,
  DATA_VALIDATION: (encounterId, serviceId) =>
    `${API_ENCOUNTER_DEFAULT}/${encounterId}/services/${serviceId}/mark-data-validation`,
  RESET_CLAIM_ID: (encounterId, serviceId) =>
    `${API_ENCOUNTER_DEFAULT}/${encounterId}/services/${serviceId}/reset-claim-id`,
  MANUALLY_RESET_CLAIM_ID: (encounterId, serviceId, id) =>
    `${API_ENCOUNTER_DEFAULT}/${encounterId}/services/${serviceId}/claim-id/${id}`,
  PENDS_HISTORY: (encounterId, serviceId) =>
    `${API_ENCOUNTER_DEFAULT}/${encounterId}/services/${serviceId}/pends`,
  SERVICE_ADJUSTMENTS: (encounterId, serviceId) =>
    `${API_ENCOUNTER_DEFAULT}/${encounterId}/services/${serviceId}/adjustments`,
  GET_CLAIM_LINE_ITEMS: (encounterId, serviceId) =>
    `${API_ENCOUNTER_DEFAULT}/${encounterId}/services/${serviceId}/claim-line-item`,
  SERVICE_WRITE_OFF: (encounterId, serviceId) =>
    `${API_ENCOUNTER_DEFAULT}/${encounterId}/services/${serviceId}/write-offs`,
  REVERSE_WRITE_OFF: (writeOffId) => `${API_ENCOUNTER_DEFAULT}/write-offs/${writeOffId}/reverse`,
  MARK_REMIT_LATEST: (remitId = '') => `${API_ENCOUNTER_DEFAULT}/remit/${remitId}/mark-as-latest`,
};

const REMIT_EOB_DEFAULT = `${apiVersion}/rcm/Remit-EOB`;
export const API_REMIT_EOB = {
  DEFAULT: REMIT_EOB_DEFAULT,
  ADJUSTMENT_LOOKUP: `${REMIT_EOB_DEFAULT}/adjustments`,
  GET_BATCH: `${REMIT_EOB_DEFAULT}/batches`,
  GET_CLAIM_STATUSES: `${REMIT_EOB_DEFAULT}/claim-statuses`,
  GET_DOCUMENT_SITES: `${REMIT_EOB_DEFAULT}/document-sites`,
  GET_DOCUMENT_TYPES: `${REMIT_EOB_DEFAULT}/document-types`,
  GET_PAPER_INFO: `${REMIT_EOB_DEFAULT}/paper-info`,
  GET_PAYORS: `${REMIT_EOB_DEFAULT}/payors`,
  UPLOAD: `${REMIT_EOB_DEFAULT}/upload-file`,
};

const API_RENDERING_PROVIDER_DEFAULT = `${apiVersion}/rcm/rendering-provider`;
export const API_RENDERING_PROVIDER = {
  DEFAULT: API_RENDERING_PROVIDER_DEFAULT,
  GET_LIST_FACILITIES: `${API_RENDERING_PROVIDER_DEFAULT}/facilities`,
  GET_HISTORY_RENDERING_PROVIDER: `${API_RENDERING_PROVIDER_DEFAULT}/history-rendering-provider`,
  PAYORS_LOOKUP: `${API_RENDERING_PROVIDER_DEFAULT}/payors`,
  RENDERER_LOOKUP: `${API_RENDERING_PROVIDER_DEFAULT}/renderer`,
  RENDERER_TYPE_LOOKUP: `${API_RENDERING_PROVIDER_DEFAULT}/renderer-types`,
  RENDERING_PROVIDER_LOOKUP: `${API_RENDERING_PROVIDER_DEFAULT}/rendering-providers`,
};

const REPORTING_UPLOAD_DEFAULT = `${apiVersion}/reporting-upload`;
export const API_REPORTING_UPLOAD = {
  DEFAULT: REPORTING_UPLOAD_DEFAULT,
  GET_FILE_TYPES: `${REPORTING_UPLOAD_DEFAULT}/file-types`,
  HISTORY: `${REPORTING_UPLOAD_DEFAULT}/history`,
  UPLOAD_BY_ID: (id) => `${REPORTING_UPLOAD_DEFAULT}/${id}`,
  DELETE_UNPROCESSED_FILE: (importId) => `${REPORTING_UPLOAD_DEFAULT}/${importId}`,
};

const FLAG_DEFAULT = `${apiVersion}/rcm/flag`;
export const API_FLAG = {
  DEFAULT: FLAG_DEFAULT,
  BATCH: `${FLAG_DEFAULT}/batch`,
  COLORS_LOOKUP: `${FLAG_DEFAULT}/colors-lookup`,
  WORKLIST: `${FLAG_DEFAULT}/worklist`,
  EMPLOYEE_LOOKUP: `${FLAG_DEFAULT}/employees-lookup`,
};

const RCM_REPORT_DEFAULT = `${apiVersion}/rcm/report`;
export const API_RCM_REPORT = {
  DEFAULT: RCM_REPORT_DEFAULT,
  GET_REPORT_OF_DAY_ADJUST: `${RCM_REPORT_DEFAULT}/day-adjust`,
  GET_TOP_5_DENIED_REASON: `${RCM_REPORT_DEFAULT}/denied-reason`,
  GET_EMPLOYEES: `${RCM_REPORT_DEFAULT}/employees-lookup`,
  GET_OUTSTANDING_RECEIVABLE_AMOUNT: `${RCM_REPORT_DEFAULT}/outstanding`,
  GET_PAYORS: `${RCM_REPORT_DEFAULT}/payors-lookup`,
  GET_REPORT_OF_SERVICES: `${RCM_REPORT_DEFAULT}/service`,
  GET_SITES: `${RCM_REPORT_DEFAULT}/sites-lookup`,
  GET_REPORT_CLAIMS_VOLUME: `${RCM_REPORT_DEFAULT}/volume`,
};

const INPATIENT_DEFAULT = `${apiVersion}/inpatient-module`;
const INPATIENT_MODULE_ADMISSION_DEFAULT = `${INPATIENT_DEFAULT}/admission`;
const CIWA_DEFAULT = `${apiVersion}/ciwaar`;
const CINA_DEFAULT = `${INPATIENT_DEFAULT}/cina`;
const BHT_DEFAULT = `${apiVersion}/bht-rounding-sheet`;
const VALUEABLE_DEFAULT = `${apiVersion}/patient-valueables`;
const DAILY_NURSING_FLOW_SHEET_DEFAULT = `${INPATIENT_DEFAULT}/daily-nursing-flow-sheet`;

export const API_INPATIENT = {
  DEFAULT: INPATIENT_DEFAULT,

  //Inpatient module
  GET_STATUS_LOOKUP: `${INPATIENT_DEFAULT}/inpatient-status-lookup`,
  GET_SITE_LOOKUP: `${INPATIENT_DEFAULT}/site-lookup`,
  GET_UPCOMING_EMAR: `${INPATIENT_DEFAULT}/upcoming-emar`,
  PRINT_LABEL: (id) => `${INPATIENT_DEFAULT}/label/${id}`,
  ALERTS: `${INPATIENT_DEFAULT}/alerts`,
  ALERTS_BY_SITE: `${INPATIENT_DEFAULT}/alerts-by-site`,

  //Site Check
  SITE_CHECK: `${INPATIENT_DEFAULT}/site-check`,
  GET_SITE_CHECK_OPTIONS_LOOKUP: `${INPATIENT_DEFAULT}/site-check-options-lookup`,
  PRINT_SITE_CHECK: (id) => `${INPATIENT_DEFAULT}/site-check/${id}/html`,

  //Comments
  CLIENT_COMMENT: `${INPATIENT_DEFAULT}/client-comment`,
  GET_COMMENT_CATEGORY_LOOKUP: `${INPATIENT_DEFAULT}/client-comment/category-lookup`,

  //Contraband
  CONTRABAND_CHECK: `${INPATIENT_DEFAULT}/contraband-check`,
  GET_CONTRABAND_OPTION_LOOKUP: `${INPATIENT_DEFAULT}/contraband-check-options-lookup`,

  //Room Check
  ROOM_CHECK: `${INPATIENT_DEFAULT}/room-check`,
  UPDATE_ROOM_CHECK: (roomCheckid) => `${INPATIENT_DEFAULT}/room-check/${roomCheckid}`,
  GET_ROOM_OPTION_LOOKUP: `${INPATIENT_DEFAULT}/room-check-options-lookup`,

  //Inpatient Room
  GET_ROOM: `${INPATIENT_DEFAULT}/room`,

  //Inpatient admission
  ADMISSTON_DEFAULT: INPATIENT_MODULE_ADMISSION_DEFAULT,
  ADMISSTON_BED_LOOKUP: `${INPATIENT_MODULE_ADMISSION_DEFAULT}/bed`,
  ADMISSTON_INPUT_DATA: `${INPATIENT_MODULE_ADMISSION_DEFAULT}/input-data`,
  ADMISSTON_TITLE_XIV_CONTACT: `${INPATIENT_MODULE_ADMISSION_DEFAULT}/title-xiv-contact`,
  ADMISSTON_ZIP_CODE: `${INPATIENT_MODULE_ADMISSION_DEFAULT}/zipcode-info`,

  //Inpatient discharge
  DISCHARGE: `${INPATIENT_DEFAULT}/discharge`,
  DISCHARGE_PRINT: (admitId) => `${INPATIENT_DEFAULT}/discharge/${admitId}/html`,
  DISCHARGE_INCOMPLETE: `${INPATIENT_DEFAULT}/discharge/incomplete`,
  DISCHARGE_INPUT_DATA: `${INPATIENT_DEFAULT}/discharge/input-data`,

  //Pending Pharmacy
  GET_PENDING_PHARMACY: `${apiVersion}/pending-pharmacy`,
  ACKNOWLEDGE_PHARMACY: (id) => `${apiVersion}/pending-pharmacy/${id}/acknowledge`,
  UPDATE_PENDING_PHARMACY_STATUS: `${apiVersion}/pending-pharmacy/status`,
  GET_PENDING_PHARMACY_STATUS_LOOKUP: `${apiVersion}/pending-pharmacy/statuses-lookup`,

  //Pending Prescription
  GET_PENDING_PRESCRIPTION: `${apiVersion}/Prescription/pending`,
  UPDATE_PENDING_PRESCRIPTION_STATUS: `${apiVersion}/Prescription/pending/status`,
  GET_PENDING_PRESCRIPTION_STATUS_LOOKUP: `${apiVersion}/Prescription/pending/status-lookup`,

  //CIWA
  CIWA_DEFAULT: CIWA_DEFAULT,
  CIWA_GET_INPUT: `${CIWA_DEFAULT}/inputs`,
  CIWA_GET_REPORT: `${CIWA_DEFAULT}/report`,
  CIWA_PRINT: `${CIWA_DEFAULT}/html`,

  //CINA
  CINA_DEFAULT: CINA_DEFAULT,
  CINA_GET_QUESTION: `${CINA_DEFAULT}/questions`,
  CINA_GET_REPORT: `${CINA_DEFAULT}/report`,
  CINA_PRINT: `${CINA_DEFAULT}/html`,

  //BHT
  BHT_DEFAULT: BHT_DEFAULT,
  BHT_HISTORY_INPATIENT: `${BHT_DEFAULT}/inpatient-module`,
  BHT_HISTORY_RESIDENTIAL: `${BHT_DEFAULT}/residential-module`,
  BHT_GET_INPUT: `${BHT_DEFAULT}/inpatient-module/inputs`,
  BHT_LOG: `${BHT_DEFAULT}/observation-log`,
  BHT_LOG_MULTI: `${BHT_DEFAULT}/observation-log-multiple`,
  BHT_PRINT_LOG: `${BHT_DEFAULT}/observation-log/html`,
  BHT_GET_BEHAVIORS_LOOKUP: `${BHT_DEFAULT}/patient-behavior-lookup`,
  BHT_GET_PATIENT: `${BHT_DEFAULT}/patients`,
  BHT_GET_INPUT_RESIDENTIAL: `${BHT_DEFAULT}/residential-module/inputs`,
  BHT_GET_STAFF_INTERVENTION: `${BHT_DEFAULT}/staff-intervention-lookup`,

  //Patient Valueable
  VALUEABLE_DEFAULT: VALUEABLE_DEFAULT,
  VALUEABLE_CHECK_IN: `${VALUEABLE_DEFAULT}/checkin`,
  VALUEABLE_CHECK_OUT_ALL: `${VALUEABLE_DEFAULT}/checkout-all`,
  VALUEABLE_CHECK_OUT: `${VALUEABLE_DEFAULT}/checkout-partial`,
  VALUEABLE_PRINT: `${VALUEABLE_DEFAULT}/html`,
  VALUEABLE_GET_LOOKUP: `${VALUEABLE_DEFAULT}/valueables-lookup`,

  //Order List
  ORDER_LIST_DEFAULT: `${INPATIENT_DEFAULT}/order-list`,
  ORDER_LIST_GET_INPUT: `${INPATIENT_DEFAULT}/order-list/input-data`,

  //Daily Nursing Flow Sheet
  DAILY_NURSE_DEFAULT: DAILY_NURSING_FLOW_SHEET_DEFAULT,
  DAILY_NURSE_GET_ASSESSMENT: `${DAILY_NURSING_FLOW_SHEET_DEFAULT}/assessment-questions`,
  DAILY_NURSE_GET_EDMONSON: `${DAILY_NURSING_FLOW_SHEET_DEFAULT}/edmonson-psychiatric-fall-risk-assessment-questions`,
  DAILY_NURSE_GET_HISTORY: `${DAILY_NURSING_FLOW_SHEET_DEFAULT}/history`,
  DAILY_NURSE_PRINT: `${DAILY_NURSING_FLOW_SHEET_DEFAULT}/html`,
  DAILY_NURSE_REVERSE: (id) => `${DAILY_NURSING_FLOW_SHEET_DEFAULT}/reverse-shift/${id}`,

  //Overview
  OVERVIEW: `${INPATIENT_DEFAULT}/overview`,

  //Clinical Discharge
  CLINICAL_DISCHARGE: `${INPATIENT_DEFAULT}/clinical-discharge`,
  CLINICAL_DISCHARGE_DETAIL: (dischargeId) =>
    `${INPATIENT_DEFAULT}/clinical-discharge/${dischargeId}`,
  CLINICAL_DISCHARGE_PRINT: (dischargeId) =>
    `${INPATIENT_DEFAULT}/clinical-discharge/${dischargeId}/html`,
  CLINICAL_DISCHARGE_GOAL: `${INPATIENT_DEFAULT}/clinical-discharge/goal`,
  CLINICAL_DISCHARGE_INPUT: `${INPATIENT_DEFAULT}/clinical-discharge/input-data`,
  CLINICAL_DISCHARGE_VALIDATION: `${INPATIENT_DEFAULT}/clinical-discharge/validation`,
};

const INFORM_CONSENT_DEFAULT = `${apiVersion}/informed-consent`;
export const API_INFORM_CONSENT = {
  DEFAULT: INFORM_CONSENT_DEFAULT,
  GET_CODE: `${INFORM_CONSENT_DEFAULT}/discussion-codes`,
  PRINT: `${INFORM_CONSENT_DEFAULT}/html`,
  INFO: `${INFORM_CONSENT_DEFAULT}/info`,
};

const NURSE_NOTE_DEFAULT = `${apiVersion}/NurseProgressNote`;
export const API_NURSE_NOTE = {
  DEFAULT: NURSE_NOTE_DEFAULT,
  BASE_ENCOUNTER: `${NURSE_NOTE_DEFAULT}/base-encounter`,
  GET_PROVIDERS: `${NURSE_NOTE_DEFAULT}/providers`,
  GET_DETAIL: (encounterId) => `${NURSE_NOTE_DEFAULT}/encounter/${encounterId}`,
  SERVICE_CODE: `${NURSE_NOTE_DEFAULT}/services-lookup`,
};

const RESTRAINT_SECLUSION_DEFAULT = `${apiVersion}/restraint-and-seclusion`;
export const API_RESTRAINT_SECLUSION = {
  DEFAULT: RESTRAINT_SECLUSION_DEFAULT,
  PRINT: (id) => `${RESTRAINT_SECLUSION_DEFAULT}/${id}/html`,
  GET_DETAIL: (id) => `${RESTRAINT_SECLUSION_DEFAULT}/${id}`,
};

const STAFF_DAILY_NOTE_DEFAULT = `${apiVersion}/staff-daily-note`;
export const API_STAFF_DAILY_NOTE = {
  DEFAULT: STAFF_DAILY_NOTE_DEFAULT,
  BASE_ENCOUNTER: `${STAFF_DAILY_NOTE_DEFAULT}/base-encounter`,
  GET_SERVICE_SITE: `${STAFF_DAILY_NOTE_DEFAULT}/service-sites-lookup`,
  GET_SERVICE_LIST: `${STAFF_DAILY_NOTE_DEFAULT}/services-lookup`,
  PRINT: `${STAFF_DAILY_NOTE_DEFAULT}/html`,
};

const ENGAGEMENT_NOTE_DEFAULT = `${apiVersion}/engagement-session-note`;
export const API_ENGAGEMENT_NOTE = {
  DEFAULT: ENGAGEMENT_NOTE_DEFAULT,
  AUTO_SAVE: `${ENGAGEMENT_NOTE_DEFAULT}/auto-save`,
  GET_AUTO_SAVE: (id) => `${ENGAGEMENT_NOTE_DEFAULT}/autosave-detail/${id}`,
  GET_MENTAL_STATUS: `${ENGAGEMENT_NOTE_DEFAULT}/mental-statuses`,
  PRINT: `${ENGAGEMENT_NOTE_DEFAULT}/html`,
  GET_SERVICES: `${ENGAGEMENT_NOTE_DEFAULT}/services-lookup`,
};

const COMPREHENSIVE_NURSING_ASESSMENT_DEFAULT = `${apiVersion}/CompNursingNote`;
export const API_COMPREHENSIVE_NOTE = {
  DEFAULT: COMPREHENSIVE_NURSING_ASESSMENT_DEFAULT,
  AUTO_SAVE: `${COMPREHENSIVE_NURSING_ASESSMENT_DEFAULT}/auto-save`,
  GET_AUTO_SAVE: (id) => `${COMPREHENSIVE_NURSING_ASESSMENT_DEFAULT}/auto-save/${id}`,
  PRINT: `${COMPREHENSIVE_NURSING_ASESSMENT_DEFAULT}/html`,
  GET_SERVICES: `${COMPREHENSIVE_NURSING_ASESSMENT_DEFAULT}/services`,
};

const INPATIENT_NOTE_DEFAULT = `${apiVersion}/inpatient-note`;
export const API_INPATIENT_NOTE = {
  DEFAULT: INPATIENT_NOTE_DEFAULT,
  AUTO_SAVE: `${INPATIENT_NOTE_DEFAULT}/auto-save`,
  BASE_ENCOUNTER: `${INPATIENT_NOTE_DEFAULT}/base-encounter`,
  GET_AUTO_SAVE: (id) => `${INPATIENT_NOTE_DEFAULT}/auto-save/${id}`,
  PRINT: `${INPATIENT_NOTE_DEFAULT}/html`,
  GET_SERVICES: `${INPATIENT_NOTE_DEFAULT}/services-lookup`,
  DELETE_TEMPLATE: (id) => `${INPATIENT_NOTE_DEFAULT}/template/${id}`,
  GET_PHYSICAL_EXAM: `${INPATIENT_NOTE_DEFAULT}/template/physical-exam`,
  PHYSICAL_EXAM_BY_ID: (id) => `${INPATIENT_NOTE_DEFAULT}/template/physical-exam/${id}`,
};

const PSYCHOSOCIAL_ASSESSMENT_NOTE_DEFAULT = `${apiVersion}/psychosocial-assessment-note`;
export const API_PSYCHOSOCIAL_ASSESSMENT_NOTE = {
  DEFAULT: PSYCHOSOCIAL_ASSESSMENT_NOTE_DEFAULT,
  AUTO_SAVE: `${PSYCHOSOCIAL_ASSESSMENT_NOTE_DEFAULT}/auto-save`,
  GET_AUTO_SAVE: (id) => `${PSYCHOSOCIAL_ASSESSMENT_NOTE_DEFAULT}/auto-save/${id}`,
  BASE_ENCOUNTER: `${PSYCHOSOCIAL_ASSESSMENT_NOTE_DEFAULT}/base-encounter`,
  DRUGS_TYPE_LOOKUP: `${PSYCHOSOCIAL_ASSESSMENT_NOTE_DEFAULT}/drug-types-lookup`,
  FREQUENCIES_LOOKUP: `${PSYCHOSOCIAL_ASSESSMENT_NOTE_DEFAULT}/frequencies-lookup`,
  PROVIDER_LOOKUP: `${PSYCHOSOCIAL_ASSESSMENT_NOTE_DEFAULT}/psychiatrists-lookup`,
  ROUTES_LOOKUP: `${PSYCHOSOCIAL_ASSESSMENT_NOTE_DEFAULT}/routes-lookup`,
  SERVICES_LOOKUP: `${PSYCHOSOCIAL_ASSESSMENT_NOTE_DEFAULT}/services-lookup`,
  SITES_LOOKUP: `${PSYCHOSOCIAL_ASSESSMENT_NOTE_DEFAULT}/sites-lookup`,
  PRINT: `${PSYCHOSOCIAL_ASSESSMENT_NOTE_DEFAULT}/html`,
};

const PSYCHOTHERAPY_PROGRESS_NOTE_DEFAULT = `${apiVersion}/psychotherapy-progress-note`;
export const API_PSYCHOTHERAPY_PROGRESS_NOTE = {
  DEFAULT: PSYCHOTHERAPY_PROGRESS_NOTE_DEFAULT,
  AUTO_SAVE: `${PSYCHOTHERAPY_PROGRESS_NOTE_DEFAULT}/auto-save`,
  GET_AUTO_SAVE: (id) => `${PSYCHOTHERAPY_PROGRESS_NOTE_DEFAULT}/auto-save/${id}`,
  BASE_ENCOUNTER: `${PSYCHOTHERAPY_PROGRESS_NOTE_DEFAULT}/base-encounter`,
  PRINT: `${PSYCHOTHERAPY_PROGRESS_NOTE_DEFAULT}/html`,
  IDENTIFIED_NEEDS_LOOKUP: `${PSYCHOTHERAPY_PROGRESS_NOTE_DEFAULT}/identified-needs-lookup`,
  PROVIDER_LOOKUP: `${PSYCHOTHERAPY_PROGRESS_NOTE_DEFAULT}/psychiatrists-lookup`,
  SERVICES_LOOKUP: `${PSYCHOTHERAPY_PROGRESS_NOTE_DEFAULT}/services-lookup`,
  SITES_LOOKUP: `${PSYCHOTHERAPY_PROGRESS_NOTE_DEFAULT}/sites-lookup`,
  TREATMENT_PLAN_LOOKUP: `${PSYCHOTHERAPY_PROGRESS_NOTE_DEFAULT}/treatment-plan-lookup`,
};

const COPAY_DEFAULT = `${apiVersion}/copay`;
export const API_COPAY = {
  DEFAULT: COPAY_DEFAULT,
  CHARGE: `${COPAY_DEFAULT}/charge`,
  CHARGE_TYPE_LOOKUP: `${COPAY_DEFAULT}/charge-types`,
  PAYMENT: `${COPAY_DEFAULT}/payment`,
  PAYMENT_TYPE_LOOKUP: `${COPAY_DEFAULT}/payment-types`,
  PAYOR_TYPE_LOOKUP: `${COPAY_DEFAULT}/payor-types`,
  PAYPAL_LOOKUP: `${COPAY_DEFAULT}/paypal-setting`,
  STRIPE_INTENT: `${COPAY_DEFAULT}/stripe/payment-intent`,
  PRINT: (id) => `${COPAY_DEFAULT}/${id}/html`,
  PRINT_LIST: `${COPAY_DEFAULT}/html`,
};

const PROVIDER_BATCH_DEFAULT = `${apiVersion}/rcm/provider`;

export const API_PROVIDER_BATCH = {
  DEFAULT: PROVIDER_BATCH_DEFAULT,
  CLASSIFICATIONS_LOOKUP: `${PROVIDER_BATCH_DEFAULT}/classification-lookups`,
  //Classification
  CLASSIFICATIONS: `${PROVIDER_BATCH_DEFAULT}/classifications`,

  //Contract
  CONTRACTS_HISTORY: (providerId) => `${PROVIDER_BATCH_DEFAULT}/${providerId}/contracts`,
  CONTRACTS: `${PROVIDER_BATCH_DEFAULT}/contracts`,
  CONTRACT_PAYORS_LOOKUP: `${PROVIDER_BATCH_DEFAULT}/contract-payors`,
  CONTRACT_TYPE_LOOKUP: `${PROVIDER_BATCH_DEFAULT}/contract-types`,

  //Contract populations
  POPULATION_HISTORY: (contractId) =>
    `${PROVIDER_BATCH_DEFAULT}/contracts/${contractId}/populations`,
  POPULATION_LOOKUPS: `${PROVIDER_BATCH_DEFAULT}/contracts/population-lookups`,
  POPULATION_TITLES_LOOKUP: `${PROVIDER_BATCH_DEFAULT}/contracts/population-titles`,
  POPULATIONS: `${PROVIDER_BATCH_DEFAULT}/contracts/populations`,
  POPULATION_DELETE: (id) => `${PROVIDER_BATCH_DEFAULT}/contracts/populations/${id}`,

  //Contract Services
  SERVICE_HISTORY: (contractId) => `${PROVIDER_BATCH_DEFAULT}/contracts/${contractId}/services`,
  SERVICE_DELETE: (contractId, id) =>
    `${PROVIDER_BATCH_DEFAULT}/contracts/${contractId}/services/${id}`,
  SERVICES: `${PROVIDER_BATCH_DEFAULT}/contracts/services`,

  //Identifiers
  IDENTIFIER_HISTORY: (providerId) => `${PROVIDER_BATCH_DEFAULT}/${providerId}/identifiers`,
  IDENTIFIER_TYPES_LOOKUP: `${PROVIDER_BATCH_DEFAULT}/identifier-types`,
  IDENTIFIERS: `${PROVIDER_BATCH_DEFAULT}/identifiers`,
  IDENTIFIERS_DELETE: (id) => `${PROVIDER_BATCH_DEFAULT}/identifiers/${id}`,
  IDENTIFIERS_EXPIRE: (id) => `${PROVIDER_BATCH_DEFAULT}/identifiers/${id}/expire`,

  //Licenses
  LICENSES_HISTORY: (providerId) => `${PROVIDER_BATCH_DEFAULT}/${providerId}/licenses`,
  LICENSE_LEVELS_LOOKUP: `${PROVIDER_BATCH_DEFAULT}/license-levels`,
  LICENSE_TYPES_LOOKUP: `${PROVIDER_BATCH_DEFAULT}/license-types`,
  LICENSES: `${PROVIDER_BATCH_DEFAULT}/licenses`,
};

const INPATIENT_STATUS_DEFAULT = `${apiVersion}/inpatient-status`;

export const API_INPATIENT_STATUS = {
  DEFAULT: INPATIENT_STATUS_DEFAULT,
  GET_PATIENTS_BY_SITE_AND_STATUS: `${INPATIENT_STATUS_DEFAULT}/patients`,
  GET_INPATIENT_INFO: (patientId) => `${INPATIENT_STATUS_DEFAULT}/patients/${patientId}/info`,
  //Admission Order
  ADMISSION_ORDER: `${INPATIENT_STATUS_DEFAULT}/admission-order`,
  ADMISSION_ORDER_DETAIL: (id) => `${INPATIENT_STATUS_DEFAULT}/admission-order/${id}`,
  ADMISSION_ORDER_HTML: `${INPATIENT_STATUS_DEFAULT}/admission-order/html`,
  ADMISSION_ORDER_INPUTS_DATA: `${INPATIENT_STATUS_DEFAULT}/admission-order/inputs`,
  //Ancillary Order
  ANCILLARY_ORDER: `${INPATIENT_STATUS_DEFAULT}/ancillary-order`,
  ANCILLARY_ORDER_INPUTS_DATA: `${INPATIENT_STATUS_DEFAULT}/ancillary-order/input-data`,
};

const ASSOCIATED_FILES_HTML_DEFAULT = `${apiVersion}/AssociatedFile`;
export const API_ASSOCIATED_FILES_HTML = {
  DEFAULT: ASSOCIATED_FILES_HTML_DEFAULT,
  FILES_LOOKUP: `${ASSOCIATED_FILES_HTML_DEFAULT}/file-lookup`,
  TYPE_LOOKUP: `${ASSOCIATED_FILES_HTML_DEFAULT}/type-lookup`,
};

const API_AI_VERSION = '/api/v1';
export const API_AI = {
  GENERATE_NOTE_BY_RECORD: '/api/v1/note',
  NOTE_STATUS_RESULT: (taskId) => `/api/v1/note/status/${taskId}/stream`,
  GENERATE_NOTE_BY_TRANSCRIPT: '/api/v1/note_with_transcript',
  GENERATE_GROUP_NOTE_BY_RECORD: '/api/v1/group/note',
  GROUP_NOTE_STATUS_RESULT: (taskId) => `/api/v1/group/note/status/${taskId}/stream`,
  GENERATE_GROUP_NOTE_BY_TRANSCRIPT: '/api/v1/group/note_with_transcript',
  SUMMARY_NOTE: '/api/v1/note/summary',
  SUMMARY_NOTE_BY_ID: '/api/v1/note/summary_with_encounter_id',
  SUMMARY_RESULT: (taskId) => `/api/v1/note/summary/status/${taskId}/stream`,
  TASK: '/api/v1/tasks',
  TASK_RESULT: (taskId) => `/api/v1/tasks/${taskId}/stream`,
  TRANSCRIPT: '/api/v1/transcript/transcribe_with_record',
  PATIENT_THEME: '/api/v1/patient_theme/patient',
  CONVERSATION_THEME: '/api/v1/patient_theme/conversation',
  OVERVIEW_THEME: '/api/v1/patient_theme/overview',
  SDOH_THEME: '/api/v1/patient_theme/sdoh',
  SDOH_THEME_HISTORY: '/api/v1/patient_theme/sdoh_history',
  SENTIMENT_ANALYSIS: '/api/v1/sentiment_analysis',
  GENERATE_SUGGESTION: '/api/v1/suggestion',
  GENERATE_DYNAMIC_SUGGESTION: '/api/v1/dynamic_note/suggestion',
  DYNAMIC_SUGGESTION_RESULT: (taskId) => `/api/v1/dynamic_note/suggestion/status/${taskId}/stream`,
  GENERATE_GROUP_NOTE_SUGGESTION: '/api/v1/group/suggestion',
  GROUP_NOTE_SUGGESTION_RESULT: (taskId) => `/api/v1/group/suggestion/status/${taskId}/stream`,
  ACTION_RECOGNITION: '/api/v1/action_recognition',
  LANGUAGES_LOOKUP: '/api/v1/transcript/supported_languages',
  WS_TRANSCRIPT: `${(envVariables?.aiUrl || '')?.replace('http', 'ws')}/api/v1/transcript/listen`,
  WS_INTERPRETER: `${(envVariables?.aiUrl || '')?.replace(
    'http',
    'ws',
  )}/api/v1/transcript/listen_and_translate`,
  WS_GROUP_TRANSCRIPT: `${(envVariables?.aiUrl || '')?.replace('http', 'ws')}/api/v1/group/listen`,

  CHAT: '/api/v1/chat',
  CHAT_STREAM: '/api/v1/ai_chat/stream',
  INIT_QUESTIONS: '/api/v1/init_question',
  FEEDBACK: '/api/v1/feedback',
  SUGGESSTION_FEEDBACK: '/api/v1/suggestion/feedback',
  SUMMARY_FEEDBACK: '/api/v1/note/summary/feedback',

  PATIENT_INIT_QUESTIONS: '/api/v1/virtual_patient/init_question',
  PATIENT_CHAT_STREAM: '/api/v1/virtual_patient/stream',

  GET_MESSAGE: '/chat/ai-assistant/messages',
  MARK_MESSAGE_IMPORTANT: (id) => `/chat/ai-assistant/messages/${id}/mark-as-important`,
  DF_ANALYZE_DOCUMENT: `${API_AI_VERSION}/document/analyze`,

  PRESCRIPTION_RECOGNITION: `${API_AI_VERSION}/prescription_recognition`,
  SIG_RECOGNITION: `${API_AI_VERSION}/direction_recognition`,

  RCM_PREDICT_VALIDATION: '/api/v1/rcm/batch/prediction',
  RCM_PREDICT: '/api/v1/rcm/batch/async_prediction',
  RCM_PREDICT_STATUS: (id) => `/api/v1/rcm/batch/async_prediction/status/${id}`,
  RCM_PREDICT_MULTIPLE: '/api/v1/rcm/batch/async_multi_prediction',
  RCM_PREDICT_MULTIPLE_STATUS: (task_id) =>
    `/api/v1/rcm/batch/async_multi_prediction/status/${task_id}/stream`,
  CREATE_ZOOM_MEETING: `${API_AI_VERSION}/meeting/zoom/`,
  GET_ZOOM_TRANSCRIPT: (id) => `${API_AI_VERSION}/meeting/zoom/${id}/transcript`,
  GET_ZOOM_STATUS: (id) => `${API_AI_VERSION}/meeting/zoom/${id}/status`,
  GET_ZOOM_STATUS_STREAM: (id) => `${API_AI_VERSION}/meeting/zoom/${id}/status/stream`,
  GET_ZOOM_RECORDING: (id) => `${API_AI_VERSION}/meeting/zoom/${id}/recordings`,
};

const FORM_AUTO_SAVE_DEFAULT = `${apiVersion}/PatientFormAutoSave`;

export const API_AUTO_SAVE_GENERAL = {
  DEFAULT: FORM_AUTO_SAVE_DEFAULT,
  GET_FORM_BY_ID: (autoSaveId) => `${FORM_AUTO_SAVE_DEFAULT}/${autoSaveId}`,
  DELETE: (autoSaveId) => `${FORM_AUTO_SAVE_DEFAULT}/${autoSaveId}`,
  COMPLETE: (autoSaveId) => `${FORM_AUTO_SAVE_DEFAULT}/${autoSaveId}/complete`,
};

const RESIDENTAL_DEFAULT = `${apiVersion}/residential-module`;
const RESIDENTAL_MODULE_IMPORTANT_DATE = `${RESIDENTAL_DEFAULT}/important-date`;
const RESIDENTAL_MODULE_DEPENDENTS = `${RESIDENTAL_DEFAULT}/dependents`;
const RESIDENTAL_MODULE_ROOM = `${RESIDENTAL_DEFAULT}/room`;
const RESIDENTAL_ADMISSION = `${apiVersion}/residential-admission`;

export const API_RESIDENTAL = {
  DEFAULT: RESIDENTAL_DEFAULT,
  CONTRABAND_CHECK: `${RESIDENTAL_DEFAULT}/contraband-check`,
  GET_SITE_LOOKUP: `${RESIDENTAL_DEFAULT}/site-lookup`,
  STATUS_LOOKUP: `${RESIDENTAL_DEFAULT}/status-lookup`,
  UPCOMING_EMAR: `${RESIDENTAL_DEFAULT}/upcoming-emar`,

  //Important Dates
  IMPORTANT_DATE_COMMENT: `${RESIDENTAL_MODULE_IMPORTANT_DATE}/comment`,
  IMPORTANT_DATE_COMMENT_TYPE: `${RESIDENTAL_MODULE_IMPORTANT_DATE}/comment-types`,
  IMPORTANT_DATE_RESIDENTIAL_COMMENTS: `${RESIDENTAL_MODULE_IMPORTANT_DATE}/residential-comments`,

  //Residential Dependents
  DEPENDENTS_DEFAULT: RESIDENTAL_MODULE_DEPENDENTS,
  DEPENDENT_MARK_AS_ABSENT: (dependentId) => `${RESIDENTAL_MODULE_DEPENDENTS}/${dependentId}`,
  DEPENDENTS_DELETE: (dependentId) => `${RESIDENTAL_MODULE_DEPENDENTS}/${dependentId}`,
  DEPENDENTS_BEDS: `${RESIDENTAL_MODULE_DEPENDENTS}/beds`,
  DEPENDENTS_ENROLLMENT: `${RESIDENTAL_MODULE_DEPENDENTS}/enrollment`,
  DEPENDENTS_FUNDING_CATEGORY_LOOKUP: `${RESIDENTAL_MODULE_DEPENDENTS}/funding-category`,
  DEPENDENTS_SEARCH_PATIENT: `${RESIDENTAL_MODULE_DEPENDENTS}/patient`,
  DEPENDENTS_POPULATION_LOOKUP: `${RESIDENTAL_MODULE_DEPENDENTS}/population`,

  //Room
  ROOM_DEFAULT: RESIDENTAL_MODULE_ROOM,
  DELETE_ROOM: (roomId) => `${RESIDENTAL_MODULE_ROOM}/${roomId}`,

  //Admission
  ADMISSION_DEFAULT: RESIDENTAL_ADMISSION,
  ADMISSION_GET_ADMISSION_DETAIL: (admitId) => `${RESIDENTAL_ADMISSION}/${admitId}`,
  ADMISSION_GET_ADMIT_REASONS: `${RESIDENTAL_ADMISSION}/admit-reasons`,
  ADMISSION_GET_BEDS: `${RESIDENTAL_ADMISSION}/beds`,
  ADMISSION_SEARCH_PATIENTS: `${RESIDENTAL_ADMISSION}/patient`,
  ADMISSION_GET_PAYORS: `${RESIDENTAL_ADMISSION}/payors`,
  ADMISSION_GET_RESIDENTIAL_SITES: `${RESIDENTAL_ADMISSION}/provider-sites`,

  //Site Check
  SITE_CHECK: `${RESIDENTAL_DEFAULT}/site-check`,
  GET_SITE_CHECK_OPTIONS_LOOKUP: `${RESIDENTAL_DEFAULT}/site-check-options-lookup`,
  PRINT_SITE_CHECK: (id) => `${RESIDENTAL_DEFAULT}/site-check/${id}/html`,

  //Discharge
  DISCHARGE: `${RESIDENTAL_DEFAULT}/discharge`,
  DISCHARGE_UPDATE_DATE: (id) => `${RESIDENTAL_DEFAULT}/discharge/${id}`,
  DISCHARGE_PRINT: (id) => `${RESIDENTAL_DEFAULT}/discharge/${id}/html`,
  DISCHARGE_GET_INPUT_DATA: `${RESIDENTAL_DEFAULT}/discharge/input-data`,

  //Room check
  ROOM_CHECK: `${RESIDENTAL_DEFAULT}/room-check`,
  UPDATE_ROOM_CHECK: (roomCheckId) => `${RESIDENTAL_DEFAULT}/room-check/${roomCheckId}`,
  //Daily Attendance
  ATTENDANCE: `${RESIDENTAL_DEFAULT}/attendance`,
};

const PLAN_BUILDER_DEFAULT = `${apiVersion}/plan-builder`;
export const API_PLAN_BUILDER = {
  DEFAULT: PLAN_BUILDER_DEFAULT,
  UPDATE: (id) => `${PLAN_BUILDER_DEFAULT}/${id}`,
};

const SCANED_DOCUMENTS_DEFAULT = `${apiVersion}/scanned-document`;
export const API_SCAN_DOCUMENTS = {
  DEFAULT: SCANED_DOCUMENTS_DEFAULT,
  UPDATE: (id) => `${SCANED_DOCUMENTS_DEFAULT}/${id}`,
  DELETE: (id) => `${SCANED_DOCUMENTS_DEFAULT}/${id}`,
  DOWNLOAD: `${SCANED_DOCUMENTS_DEFAULT}/document-file`,
  GET_SEARCH_TYPE: `${SCANED_DOCUMENTS_DEFAULT}/document-search-types`,
  GET_DOCUMENT_TYPE: `${SCANED_DOCUMENTS_DEFAULT}/document-types`,
  HISTORY: `${SCANED_DOCUMENTS_DEFAULT}/documents`,
  POST_MULTI_FILES: `${SCANED_DOCUMENTS_DEFAULT}/multiple`,
  VIEW_CCD: (id) => `${SCANED_DOCUMENTS_DEFAULT}/${id}/xsd-document`,
};

const NURSE_DAILY_NOTE_DEFAULT = `${apiVersion}/nurse-daily-note`;
export const API_NURSE_DAILY_NOTE = {
  DEFAULT: NURSE_DAILY_NOTE_DEFAULT,
  BASE_ENCOUNTER: `${NURSE_DAILY_NOTE_DEFAULT}/base-encounter`,
  PRINT: `${NURSE_DAILY_NOTE_DEFAULT}/html`,
  SERVICES_LOOKUP: `${NURSE_DAILY_NOTE_DEFAULT}/services-lookup`,
};

const DAILY_STAFF_NOTE_DEFAULT = `${apiVersion}/daily-staff-note`;
export const API_DAILY_STAFF_NOTE = {
  DEFAULT: DAILY_STAFF_NOTE_DEFAULT,
  BASE_ENCOUNTER: `${DAILY_STAFF_NOTE_DEFAULT}/base-encounter`,
  PRINT: `${DAILY_STAFF_NOTE_DEFAULT}/html`,
  SERVICES_LOOKUP: `${DAILY_STAFF_NOTE_DEFAULT}/services-lookup`,
};

const NURSING_ADMISSION_ASSESSMENT_DEFAULT = `${apiVersion}/nursing-assessment-note`;
export const API_NURSING_ADMISSION_ASSESSMENT = {
  DEFAULT: NURSING_ADMISSION_ASSESSMENT_DEFAULT,
  AUTO_SAVE: `${NURSING_ADMISSION_ASSESSMENT_DEFAULT}/auto-save`,
  GET_AUTO_SAVE: (id) => `${NURSING_ADMISSION_ASSESSMENT_DEFAULT}/auto-save/${id}`,
  BASE_ENCOUNTER: `${NURSING_ADMISSION_ASSESSMENT_DEFAULT}/base-encounter`,
  DRUGS_TYPE_LOOKUP: `${NURSING_ADMISSION_ASSESSMENT_DEFAULT}/drug-types-lookup`,
  FREQUENCIES_LOOKUP: `${NURSING_ADMISSION_ASSESSMENT_DEFAULT}/frequencies-lookup`,
  PRINT: `${NURSING_ADMISSION_ASSESSMENT_DEFAULT}/html`,
  ROUTES_LOOKUP: `${NURSING_ADMISSION_ASSESSMENT_DEFAULT}/routes-lookup`,
  SERVICES_LOOKUP: `${NURSING_ADMISSION_ASSESSMENT_DEFAULT}/services-lookup`,
  SITES_LOOKUP: `${NURSING_ADMISSION_ASSESSMENT_DEFAULT}/sites-lookup`,
  TEMPLATE_BY_ID: (id) => `${NURSING_ADMISSION_ASSESSMENT_DEFAULT}/template/${id}`,
  GET_PHYSICAL_EXAM_TEMPLATES: `${NURSING_ADMISSION_ASSESSMENT_DEFAULT}/template/physical-exam`,
  UPDATE_PHYSICAL_EXAM_TEMPLATES: (id) =>
    `${NURSING_ADMISSION_ASSESSMENT_DEFAULT}/template/physical-exam/${id}`,
  GET_ROS_TEMPLATES: `${NURSING_ADMISSION_ASSESSMENT_DEFAULT}/template/review-of-system`,
  UPDATE_ROS_TEMPLATES: (id) =>
    `${NURSING_ADMISSION_ASSESSMENT_DEFAULT}/template/review-of-system/${id}`,
};

const CLINICAL_ANALYTIC_DEFAULT = `${apiVersion}/clinical-analytic`;
export const API_CLINICAL_ANALYTIC = {
  DEFAULT: CLINICAL_ANALYTIC_DEFAULT,
  COMPLIANCE_SERVICE: `${CLINICAL_ANALYTIC_DEFAULT}/compliance-service`,
  DIMENSION: `${CLINICAL_ANALYTIC_DEFAULT}/dimension`,
  PATIENT_ENROLLMENT: `${CLINICAL_ANALYTIC_DEFAULT}/patient-enrollment`,
  TOP_CHRONIC_CONDITION: `${CLINICAL_ANALYTIC_DEFAULT}/top-chronic-condition`,
  WORD_CLOUD: `${CLINICAL_ANALYTIC_DEFAULT}/word-cloud`,
};

const HEATH_POPULATION_ANALYTIC_DEFAULT = `${apiVersion}/health-pop-analytic`;
export const API_HEATH_POPULATION_ANALYTIC = {
  DEFAULT: HEATH_POPULATION_ANALYTIC_DEFAULT,
  DEMOGRAPHIC: `${HEATH_POPULATION_ANALYTIC_DEFAULT}/demographic`,
};

const INSIGHT_ANALYTIC_DEFAULT = `${apiVersion}/insight`;
export const API_INSIGHT_ANALYTIC = {
  APPOINTMENT: `${INSIGHT_ANALYTIC_DEFAULT}/appointment`,
  HIGH_RISK: `${INSIGHT_ANALYTIC_DEFAULT}/high-risk`,
};

const FINANCIAL_ANALYTIC_DEFAULT = `${apiVersion}/financial-analytic`;
export const API_FINALCIAL_ANALYTIC = {
  DEFAULT: FINANCIAL_ANALYTIC_DEFAULT,
  AGING_SUMMARY: `${FINANCIAL_ANALYTIC_DEFAULT}/aging-summary`,
  AGING_SUMMARY_TOTAL: `${FINANCIAL_ANALYTIC_DEFAULT}/aging-summary-total`,
  DAILY_CENSUSES: `${FINANCIAL_ANALYTIC_DEFAULT}/daily-censuses`,
  ENCOUNTERS_REPORT: `${FINANCIAL_ANALYTIC_DEFAULT}/encounters-report`,
  FINANCIAL_PATIENTS: `${FINANCIAL_ANALYTIC_DEFAULT}/financial-patients`,
  INDICATOR_SUMMARY: `${FINANCIAL_ANALYTIC_DEFAULT}/indicator-summary`,
};

const PCP_COMMUNICATION_DEFAULT = `${apiVersion}/PcpNotify`;
export const API_PCP_COMMUNICATION = {
  DEFAULT: PCP_COMMUNICATION_DEFAULT,
  PRINT: (pcpNotifyId) => `${PCP_COMMUNICATION_DEFAULT}/${pcpNotifyId}/html`,
  INPUT_DATA: `${PCP_COMMUNICATION_DEFAULT}/input-data`,
};

const ART_CFT_DEFAULT = `${apiVersion}/child-and-family`;
export const API_ART_CFT = {
  DEFAULT: ART_CFT_DEFAULT,
  UPDATE_BY_ID: (id) => `${ART_CFT_DEFAULT}/${id}`,
  AUTO_SAVE: `${ART_CFT_DEFAULT}/auto-saves`,
  COLLABBORATORS_HISTORY: `${ART_CFT_DEFAULT}/collaborators`,
  MEETING_SUMMARY_HISTORY: `${ART_CFT_DEFAULT}/meeting-summaries`,
  STAFF_SUMMARY_HISTORY: `${ART_CFT_DEFAULT}/staffing-summaries`,
  PROBLEM_HISTORY: `${ART_CFT_DEFAULT}/problem-list`,
  GET_STATUSES: `${ART_CFT_DEFAULT}/completion-statuses`,
  GET_LOCATIONS: `${ART_CFT_DEFAULT}/locations`,
  GET_MEETING_TYPES: `${ART_CFT_DEFAULT}/meeting-types`,
  PRINT: `${ART_CFT_DEFAULT}/html`,
  PRINT_STAFF_SUMMARY: (id) => `${ART_CFT_DEFAULT}/staffing-summaries/${id}/html`,
  GET_RATING_SCALE_ANSWER: `${ART_CFT_DEFAULT}/rating-scale/answer`,
  GET_RATING_SCALE_QUESTIONS: `${ART_CFT_DEFAULT}/rating-scales`,
  GET_SIGNATURE_LOOKUP: `${ART_CFT_DEFAULT}/signature-relationship`,
  GET_SITES: `${ART_CFT_DEFAULT}/sites`,
  INPUT_DATA: `${ART_CFT_DEFAULT}/input-data`,
  GET_FACILITATOR: `${ART_CFT_DEFAULT}/facilitator`,
};

const DYNAMIC_SERVICE_PLAN_DEFAULT = `${apiVersion}/dynamic-service-plan`;

export const API_DYNAMIC_SERVICE_PLAN = {
  GET_HISTORIES: DYNAMIC_SERVICE_PLAN_DEFAULT,
  SAVE: DYNAMIC_SERVICE_PLAN_DEFAULT,
  GET_DETAIL: (dynamicServicePlanId) => `${DYNAMIC_SERVICE_PLAN_DEFAULT}/${dynamicServicePlanId}`,
  DELETE: (dynamicServicePlanId) => `${DYNAMIC_SERVICE_PLAN_DEFAULT}/${dynamicServicePlanId}`,
  SAVE_ANSWER: `${DYNAMIC_SERVICE_PLAN_DEFAULT}/answer`,
  GET_ANSWER: (saveId) => `${DYNAMIC_SERVICE_PLAN_DEFAULT}/answer/${saveId}`,
  DELETE_ANSWER: (saveId) => `${DYNAMIC_SERVICE_PLAN_DEFAULT}/answer/${saveId}`,
  GET_ANSWER_HISTORIES: `${DYNAMIC_SERVICE_PLAN_DEFAULT}/answers`,
  GET_ANSWER_TYPE: `${DYNAMIC_SERVICE_PLAN_DEFAULT}/answer-types`,
  GET_QUESTIONS: `${DYNAMIC_SERVICE_PLAN_DEFAULT}/questions`,
  SAVE_QUESTION: `${DYNAMIC_SERVICE_PLAN_DEFAULT}/question`,
  GET_QUESTION_DETAIL: (questionId) => `${DYNAMIC_SERVICE_PLAN_DEFAULT}/question/${questionId}`,
  DELETE_QUESTION: (questionId) => `${DYNAMIC_SERVICE_PLAN_DEFAULT}/question/${questionId}`,
  RETURN_FOR_EDIT: (dynamicServicePlanId) =>
    `${DYNAMIC_SERVICE_PLAN_DEFAULT}/${dynamicServicePlanId}/returned-for-edit`,
};

const HEALTH_PHYSICAL_NOTE_DEFAULT = `${apiVersion}/health-physical-note`;
export const API_HEALTH_PHYSICAL_NOTE = {
  DEFAULT: HEALTH_PHYSICAL_NOTE_DEFAULT,
  AUTO_SAVE: `${HEALTH_PHYSICAL_NOTE_DEFAULT}/auto-save`,
  PROLEMS_LOOKUP: `${HEALTH_PHYSICAL_NOTE_DEFAULT}/active-problems`,
  GET_AUTO_SAVE: (id) => `${HEALTH_PHYSICAL_NOTE_DEFAULT}/auto-save/${id}`,
  BASE_ENCOUNTER: `${HEALTH_PHYSICAL_NOTE_DEFAULT}/base-encounter`,
  PRINT: `${HEALTH_PHYSICAL_NOTE_DEFAULT}/html`,
  SERVICES_LOOKUP: `${HEALTH_PHYSICAL_NOTE_DEFAULT}/services-lookup`,
  TEMPLATE_BY_ID: (id) => `${HEALTH_PHYSICAL_NOTE_DEFAULT}/template/${id}`,
  TEMPLATE: `${HEALTH_PHYSICAL_NOTE_DEFAULT}/template`,
};

const PAGE_ACTION_DEFAULT = `${apiVersion}/page-action`;
export const API_PAGE_ACTION = {
  DEFAULT: PAGE_ACTION_DEFAULT,
  UPDATE: (id) => `${PAGE_ACTION_DEFAULT}/${id}`,
};

const PAGE_CONTROLLER_DEFAULT = `${apiVersion}/page`;
export const API_PAGE_CONTROLLER = {
  DEFAULT: PAGE_CONTROLLER_DEFAULT,
  GET_DYNAMIC: `${PAGE_CONTROLLER_DEFAULT}/dynamic`,
};

const RCM_PREFILTER_DEFAULT = `${apiVersion}/rcm/pre-filter`;
export const API_RCM_PREFILTER = {
  GET_PREFILTERS: RCM_PREFILTER_DEFAULT,
  SAVE_PREFILTER: RCM_PREFILTER_DEFAULT,
  UPLOAD_LAST_USED: (id) => `${RCM_PREFILTER_DEFAULT}/${id}/last-used`,
  SAVE_FOLDER: `${RCM_PREFILTER_DEFAULT}/folder`,
  GET_LIST_FOLDERS: `${RCM_PREFILTER_DEFAULT}/folders-lookup`,
  DELETE_FOLDER: (folderId) => `${RCM_PREFILTER_DEFAULT}/folder/${folderId}`,
  DELETE_PREFILTER: (preFilterId) => `${RCM_PREFILTER_DEFAULT}/${preFilterId}`,
};

const ROLE_ADMINISTRATION_DEFAULT = `${apiVersion}/RoleAdministration`;
export const API_ROLE_ADMINISTRATION = {
  DEFAULT: ROLE_ADMINISTRATION_DEFAULT,
  GET_APPROVAL_REQUESTS: `${ROLE_ADMINISTRATION_DEFAULT}/approval-requests`,
  GET_APPROVAL_DOCTORS_ROLE: `${ROLE_ADMINISTRATION_DEFAULT}/approval-requests/approvers`,
  APPROVE_REQUESTS: (id) => `${ROLE_ADMINISTRATION_DEFAULT}/approval-requests/${id}/approve`,
  DENY_REQUESTS: (id) => `${ROLE_ADMINISTRATION_DEFAULT}/approval-requests/${id}/deny`,
  USER_ROLES: `${ROLE_ADMINISTRATION_DEFAULT}/roles`,
  ALL_USER: `${ROLE_ADMINISTRATION_DEFAULT}/users`,
  SEARCH_USERS: `${ROLE_ADMINISTRATION_DEFAULT}/search-users`,
  GET_ALL_ROLES: `${ROLE_ADMINISTRATION_DEFAULT}/all-roles`,
};

const SPECIFIC_SECURITY_DEFAULT = `${apiVersion}/SpecificSecurity`;
export const API_SPECIFIC_SECURITY = {
  DEFAULT: SPECIFIC_SECURITY_DEFAULT,
  GET_PAGE_LISTS_CURRENT_USER: `${SPECIFIC_SECURITY_DEFAULT}/me/page-list`,
  SEARCH_ROLES: `${SPECIFIC_SECURITY_DEFAULT}/security-roles`,
  PAGE_LISTS_BY_ROLE_ID: (id) => `${SPECIFIC_SECURITY_DEFAULT}/roles/${id}/pages`,
  ROLE_LIST_BY_PAGE_ID: (id) => `${SPECIFIC_SECURITY_DEFAULT}/pages/${id}/roles`,
  PAGE_LISTS: (id) => `${SPECIFIC_SECURITY_DEFAULT}/page-list/${id || ''}`,
  SECURITY_PAGES: `${SPECIFIC_SECURITY_DEFAULT}/user-pages`,
};

const SECURITY_FORMS_DEFAULT = `${apiVersion}/PatientFormSecurity`;
export const API_SECURITY_FORMS = {
  DEFAULT: SECURITY_FORMS_DEFAULT,
  BY_ROLE: `${SECURITY_FORMS_DEFAULT}/by-role`,
  BY_USER: `${SECURITY_FORMS_DEFAULT}/by-user`,
  SAVE_ROLE: (roleId) => `${SECURITY_FORMS_DEFAULT}/roles/${roleId}/forms`,
  SAVE_USER: `${SECURITY_FORMS_DEFAULT}/user-forms`,
};

const QUICK_ENROLLMENT_DEFAULT = `${apiVersion}/QuickEnrollment`;
export const API_QUICK_ENROLLMENT = {
  DEFAULT: QUICK_ENROLLMENT_DEFAULT,
  GET_DETAIL: (id) => `${QUICK_ENROLLMENT_DEFAULT}/${id}`,
  SAVE_STRUCT: `${QUICK_ENROLLMENT_DEFAULT}/form-save`,
  GET_LOOK_UP: `${QUICK_ENROLLMENT_DEFAULT}/lookup-data`,
  GET_QUESTIONS: `${QUICK_ENROLLMENT_DEFAULT}/questions`,
  VALIDATE: `${QUICK_ENROLLMENT_DEFAULT}/validation`,
  SEARCH_PHARMACY: `${QUICK_ENROLLMENT_DEFAULT}/pharmacies`,
  PHARMACY_LOOKUP: `${QUICK_ENROLLMENT_DEFAULT}/pharmacies/lookup`,
  GET_CHILD_ITEMS: `${QUICK_ENROLLMENT_DEFAULT}/child-items`,
  GET_SECTIONS: `${QUICK_ENROLLMENT_DEFAULT}/sections`,
  GET_HISTORIES: `${QUICK_ENROLLMENT_DEFAULT}/histories`,
  PRIORITIES_LOOKUP: `${QUICK_ENROLLMENT_DEFAULT}/priorities`,
  LEVEL_OF_CARE_LOOKUP: `${QUICK_ENROLLMENT_DEFAULT}/level-of-care`,
};

const RPA_DEFAULT = `${apiVersion}/RPA`;
export const API_RPA = {
  DEFAULT: RPA_DEFAULT,
  FORM_TYPE: `${RPA_DEFAULT}/form-type`,
  GET_BY_KEYSTROKES: `${RPA_DEFAULT}/get-by-keystrokes`,
  KEYSTROKES: `${RPA_DEFAULT}/keystrokes`,
  RECENT: `${RPA_DEFAULT}/recent`,
  RUN: (rpaId) => `${RPA_DEFAULT}/${rpaId}/run`,
  DYNAMIC_STRING: `${RPA_DEFAULT}/dynamic-string`,
};

const DYNAMIC_REPORT_DEFAULT = `${apiVersion}/dynamic-report`;
export const API_DYNAMIC_REPORT = {
  //for view
  RETRIEVES_THE_HISTORY: DYNAMIC_REPORT_DEFAULT,
  RETRIEVES_DETAIL: (dynamicReportId) => `${DYNAMIC_REPORT_DEFAULT}/${dynamicReportId}`,
  EXCUTE_A_DYNAMIC_REPORT: (dynamicReportId) => `${DYNAMIC_REPORT_DEFAULT}/${dynamicReportId}`,
  RETRIEVES_THE_SEARCH_PARAMETERS_OF_A_DYNAMIC_REPORT: (dynamicReportId) =>
    `${DYNAMIC_REPORT_DEFAULT}/${dynamicReportId}/search-parameters`,
  //for build
  GET_INDIVIDUAL_VALUES: (table, column) =>
    `${DYNAMIC_REPORT_DEFAULT}/${table}/${column}/individual-values`,
  RETRIEVES_PIVOT_REPORT_FILE: `${DYNAMIC_REPORT_DEFAULT}/pivot-file`,
  SAVE_PIVOT_TABLE: `${DYNAMIC_REPORT_DEFAULT}/pivot-table`,
  GENERATE_PIVOT_TABLE_DATA: `${DYNAMIC_REPORT_DEFAULT}/pivot-table`,
  SAVE_REPORT_STANDARD_TABLE_AND_SQL_BOARD: `${DYNAMIC_REPORT_DEFAULT}/query-table`,
  GENERATE_QUERY_STANDARD_TABLE: `${DYNAMIC_REPORT_DEFAULT}/query-table`,
  GET_REPORT_FILE: `${DYNAMIC_REPORT_DEFAULT}/report-file`,
  RETRIEVES_A_LIST_OF_DYNAMIC_REPORT_TABLES: `${DYNAMIC_REPORT_DEFAULT}/report-tables`,
  CATEGORIES_LOOKUP: `${DYNAMIC_REPORT_DEFAULT}/categories`,
  DOWNLOAD_DYNAMIC_REPORT_RESULT_BY_ID: (dynamicReportId) =>
    `${DYNAMIC_REPORT_DEFAULT}/file/${dynamicReportId}`,
  GET_QUERY_STANDARD_TABLE_STATEMENT: `${DYNAMIC_REPORT_DEFAULT}/statement`,
  GET_EMAIL_SETTING_OF_DYNAMIC_REPORT: (dynamicReportId) =>
    `${DYNAMIC_REPORT_DEFAULT}/${dynamicReportId}/email-settings`,
  GET_SCHEDULED_OF_DYNAMIC_REPORT: (dynamicReportId) =>
    `${DYNAMIC_REPORT_DEFAULT}/${dynamicReportId}/scheduled`,
  GET_JOIN_CLAUSE: (primary, secondary) =>
    `${DYNAMIC_REPORT_DEFAULT}/${primary}/${secondary}/join-clause`,
  EXECUTE_DYNAMIC_REPORT_BACKGROUND: (dynamicReportId) =>
    `${DYNAMIC_REPORT_DEFAULT}/${dynamicReportId}/background`,
  SCHEDULE_EXISTING_DYNAMIC_REPORT: (dynamicReportId) =>
    `${DYNAMIC_REPORT_DEFAULT}/${dynamicReportId}/schedule`,
};

const REPORT_MODULE_DEFAULT = `${apiVersion}/report-module`;
export const API_REPORT_MODULE = {
  DEFAULT: REPORT_MODULE_DEFAULT,
  GET_835_FILES: `${REPORT_MODULE_DEFAULT}/835-files`,
  BATCH_FORMS: `${REPORT_MODULE_DEFAULT}/batch-forms`,
  BATCH_HISTORY: `${REPORT_MODULE_DEFAULT}/batch-history`,
  EVV_ENCOUNTERS: `${REPORT_MODULE_DEFAULT}/evv-encounters`,
  MU_MEASUREMENTS: `${REPORT_MODULE_DEFAULT}/mu-measurements`,
  PARAMETER_VALUES: `${REPORT_MODULE_DEFAULT}/parameter/values`,
  PARAMETERS: `${REPORT_MODULE_DEFAULT}/parameters`,
  REPORT_DATA: `${REPORT_MODULE_DEFAULT}/report-data`,
  DOWNLOAD_REPORT_FILE: `${REPORT_MODULE_DEFAULT}/report-file`,
  REPORT_MODULES_CATEGORY: `${REPORT_MODULE_DEFAULT}/categories`,
  RUN_ALL_TYPES_OF_REPORT_MODULE: `${REPORT_MODULE_DEFAULT}/execute`,
  SCHEDULE: `${REPORT_MODULE_DEFAULT}/schedule`,
  FAVORITE: (id) => `${REPORT_MODULE_DEFAULT}/${id}/favorite`,
  SAVE_BATCH_PRINT_RECORDS: `${REPORT_MODULE_DEFAULT}/print-batch`,
  GET_EHI_EXPORT_HISTORY: `${REPORT_MODULE_DEFAULT}/ehi-export-history`,
};

const EMPLOYEE_EDUCATION_DEFAULT = `${apiVersion}/EmployeeEducation`;
export const API_EMPLOYEE_EDUCATION = {
  DEFAULT: EMPLOYEE_EDUCATION_DEFAULT,
  DELETE: (empEducationId) => `${EMPLOYEE_EDUCATION_DEFAULT}/${empEducationId}`,
  EDUCATION_LOOKUPS: `${EMPLOYEE_EDUCATION_DEFAULT}/lookups`,
  EMPLOYEE_LICENSE: `${EMPLOYEE_EDUCATION_DEFAULT}/license`,
  DELETE_EMPLOYEE_LICENSE: (empLicenseId) =>
    `${EMPLOYEE_EDUCATION_DEFAULT}/license/${empLicenseId}`,
  LICENSE_LOOKUPS: `${EMPLOYEE_EDUCATION_DEFAULT}/license/lookups`,
  EMPLOYEE_QUALIFICATIONS: `${EMPLOYEE_EDUCATION_DEFAULT}/qualification`,
  DELETE_EMPLOYEE_QUALIFICATIONS: (empQualificationId) =>
    `${EMPLOYEE_EDUCATION_DEFAULT}/qualification/${empQualificationId}`,
  QUALIFICATION_LOOKUPS: `${EMPLOYEE_EDUCATION_DEFAULT}/qualification/lookups`,
};

const EMPLOYMENT_ACTION_INFO_DEFAULT = `${apiVersion}/EmployeeActionInformation`;
export const API_EMPLOYMENT_ACTION_INFO = {
  DEFAULT: EMPLOYMENT_ACTION_INFO_DEFAULT,
  DELETE: (id) => `${EMPLOYMENT_ACTION_INFO_DEFAULT}/${id}`,
  GET_REASONS: `${EMPLOYMENT_ACTION_INFO_DEFAULT}/reasons`,
  GET_ACTIONS: `${EMPLOYMENT_ACTION_INFO_DEFAULT}/statuses`,
};

const EMPLOYEE_COMMENT_DEFAULT = `${apiVersion}/hr/employee-comment`;
export const API_EMPLOYEE_COMMENT = {
  DEFAULT: EMPLOYEE_COMMENT_DEFAULT,
  GET_CATEGORIES: `${EMPLOYEE_COMMENT_DEFAULT}/categories`,
  COMMENT: `${EMPLOYEE_COMMENT_DEFAULT}/comments`,
};

const EMPLOYEE_PROGRAM_DEFAULT = `${apiVersion}/hr/employee-program`;
export const API_EMPLOYEE_PROGRAM = {
  DEFAULT: EMPLOYEE_PROGRAM_DEFAULT,
  GET_PROGRAMS_LOOKUP: `${EMPLOYEE_PROGRAM_DEFAULT}/programs-lookup`,
  DELETE: (id) => `${EMPLOYEE_PROGRAM_DEFAULT}/${id}`,
};

const EMPLOYEE_SPECIALTIES_DEFAULT = `${apiVersion}/Employee/specialties`;
export const API_EMPLOYEE_SPECIALTIES = {
  DEFAULT: EMPLOYEE_SPECIALTIES_DEFAULT,
  GET: (id) => `${EMPLOYEE_SPECIALTIES_DEFAULT}/${id}`,
};

const EMPLOYEE_POSITION_DEFAULT = `${apiVersion}/EmployeePosition`;
export const API_EMPLOYEE_POSITION = {
  DEFAULT: (id = '') => `${EMPLOYEE_POSITION_DEFAULT}/${id}`,
  ASSOCIATION: (id = '') => `${EMPLOYEE_POSITION_DEFAULT}/association/${id}`,
  ASSOCIATION_EMPLOYEES: `${EMPLOYEE_POSITION_DEFAULT}/association/employees`,
  ASSOCIATION_TYPE_LOOKUPS: `${EMPLOYEE_POSITION_DEFAULT}/association/types`,
  JOB_LOOKUPS: `${EMPLOYEE_POSITION_DEFAULT}/jobs`,
  PROGRAM_LOOPKUPS: `${EMPLOYEE_POSITION_DEFAULT}/programs`,
  SITE_LOOPKUPS: `${EMPLOYEE_POSITION_DEFAULT}/sites`,
};

const EMPLOYEE_IDENTIFIER_DEFAULT = `${apiVersion}/Employee/identifier-types`;
export const API_EMPLOYEE_IDENTIFIER = {
  DEFAULT: EMPLOYEE_IDENTIFIER_DEFAULT,
  GET: (id) => `${EMPLOYEE_IDENTIFIER_DEFAULT}/${id}`,
};

const EMPLOYEE_PSYCHIATRIST_DEFAULT = `${apiVersion}/EmployeePsychiatristInfo`;
export const API_EMPLOYEE_PSYCHIATRIST = {
  DEFAULT: EMPLOYEE_PSYCHIATRIST_DEFAULT,
  ADD_SCRIPT: (id) => `${EMPLOYEE_PSYCHIATRIST_DEFAULT}/${id}/provider`,
  SAVE_ASSOCIATION: `${EMPLOYEE_PSYCHIATRIST_DEFAULT}/associations`,
  GET_ASSOCIATION: (id) => `${EMPLOYEE_PSYCHIATRIST_DEFAULT}/associations/${id}`,
  GET_CREDENTIALS: `${EMPLOYEE_PSYCHIATRIST_DEFAULT}/credentials`,
  GET_PMP_ROLES: `${EMPLOYEE_PSYCHIATRIST_DEFAULT}/pmp-roles`,
  GET_E_PRESCRIBING: (id) => `${EMPLOYEE_PSYCHIATRIST_DEFAULT}/e-prescribing-sites/${id}`,
};

const EMPLOYEE_ROLES_DEFAULT = `${apiVersion}/hr/employee-roles`;
export const API_EMPLOYEE_ROLES = {
  DEFAULT: (id = '') => `${EMPLOYEE_ROLES_DEFAULT}/${id}`,
  EXPIRE: (id = '') => `${EMPLOYEE_ROLES_DEFAULT}/${id}/expire`,
  ROLE_TYPE_LOOKUPS: `${EMPLOYEE_ROLES_DEFAULT}/role-types`,
  COPY_HR_ROLES: `${EMPLOYEE_ROLES_DEFAULT}/copy`,
  ADD_MULTIPLE: `${EMPLOYEE_ROLES_DEFAULT}/multiples`,
};

const API_SURECRIPTS_DEFAULT = `${apiVersion}/surescripts`;
export const API_SURECRIPTS = {
  DEFAULT: API_SURECRIPTS_DEFAULT,
  DETAIL: (id = '') => `${API_SURECRIPTS_DEFAULT}/${id}`,
  RESPONSE: `${API_SURECRIPTS_DEFAULT}/SendResponseMessage`,
};

const PATIENT_ASSIGNMENT_DEFAULT = `${apiVersion}/PatientAssignment`;
export const API_PATIENT_ASSIGNMENT = {
  DEFAULT: `${PATIENT_ASSIGNMENT_DEFAULT}`,

  //GROUP
  DELETE_GROUP: (id = '') => `${PATIENT_ASSIGNMENT_DEFAULT}/group/${id}`,
  GROUP: `${PATIENT_ASSIGNMENT_DEFAULT}/group`,
  GROUP_LOOKUP: `${PATIENT_ASSIGNMENT_DEFAULT}/group/lookup`,
  FREQUECY_LOOKUP: `${PATIENT_ASSIGNMENT_DEFAULT}/group/lookup/frequency-length`,

  //PCP
  PCP: `${PATIENT_ASSIGNMENT_DEFAULT}/pcp`,
  SEARCH: `${PATIENT_ASSIGNMENT_DEFAULT}/pcp/pharmacy`,
  GET_PHARMACY_INFO: `${PATIENT_ASSIGNMENT_DEFAULT}/pcp/pharmacy-info-lookup`,

  //SITE
  SITE: `${PATIENT_ASSIGNMENT_DEFAULT}/site`,
  EXPIRED_SITE: (id = '') => `${PATIENT_ASSIGNMENT_DEFAULT}/site/${id}`,
  SITE_LOOKUP: `${PATIENT_ASSIGNMENT_DEFAULT}/site/lookup`,
  SITE_TYPE_LOOKUP: `${PATIENT_ASSIGNMENT_DEFAULT}/site/lookup/types`,

  //TEAM
  TEAM: `${PATIENT_ASSIGNMENT_DEFAULT}/team`,
  EXPIRED_TEAM: (id = '') => `${PATIENT_ASSIGNMENT_DEFAULT}/team/${id}`,
  EMPLOYEE_LOOKUP: `${PATIENT_ASSIGNMENT_DEFAULT}/team/lookup/employees`,
  ROLES_LOOKUP: `${PATIENT_ASSIGNMENT_DEFAULT}/team/lookup/roles`,

  //PRIORITY
  PRIORITY: `${PATIENT_ASSIGNMENT_DEFAULT}/priority`,
  EXPIRED_PRIORITY: (id = '') => `${PATIENT_ASSIGNMENT_DEFAULT}/priority/${id}`,
  PRIORITY_LOOKUP: `${PATIENT_ASSIGNMENT_DEFAULT}/priority/lookup`,
  LV_OF_CARE_LOOKUP: `${PATIENT_ASSIGNMENT_DEFAULT}/priority/lookup/level-of-cares`,
  SCHOOL_LOOKUP: `${PATIENT_ASSIGNMENT_DEFAULT}/priority/lookup/schools`,
};

const COVER_SHEET_DEFAULT = `${apiVersion}/cover-sheet`;
export const API_COVER_SHEET = {
  DEFAULT: `${COVER_SHEET_DEFAULT}`,

  //ADDRESS
  DELETE_GROUP: (id = '') => `${COVER_SHEET_DEFAULT}/group/${id}`,
  ADDRESS: `${COVER_SHEET_DEFAULT}/address`,
  ADDRESS_FORM_BASE: `${COVER_SHEET_DEFAULT}/address/form-base`,
  ADDRESS_PRINT: `${COVER_SHEET_DEFAULT}/address/html`,

  //INSURANCE
  INSURANCE: `${COVER_SHEET_DEFAULT}/insurance`,
  INSURANCE_FORM_BASE: `${COVER_SHEET_DEFAULT}/insurance/form-base`,

  //ADDITIONAL INFO
  ADDITIONAL_INFO: `${COVER_SHEET_DEFAULT}/additional-info`,
  ADDITIONAL_INFO_LOOKUP: `${COVER_SHEET_DEFAULT}/additional-info/type-lookup`,

  //PORTAL
  PATIENT_PORTAL: `${COVER_SHEET_DEFAULT}/patient-portal`,
  PATIENT_PORTAL_PRINT: `${COVER_SHEET_DEFAULT}/patient-portal/html`,
  PATIENT_PORTAL_DEPENDENTS: `${COVER_SHEET_DEFAULT}/patient-portal/dependent`,
  PATIENT_PORTAL_RESET_PASS: `${COVER_SHEET_DEFAULT}/patient-portal/password-reset`,

  //PATIENT
  PATIENT_IDENTIFIER: `${COVER_SHEET_DEFAULT}/identifier`,
  PATIENT_LOOKUP: `${COVER_SHEET_DEFAULT}/lookup`,
  PATIENT_PRINT: `${COVER_SHEET_DEFAULT}/html`,
  PATIENT_CCD: `${COVER_SHEET_DEFAULT}/generate-ccd`,

  //CONTACT
  CONTACT: `${apiVersion}/PatientContact`,
  CONTACT_TYPES: `${apiVersion}/PatientContact/contact-types`,
  CONTACT_CUSTODY: `${apiVersion}/PatientContact/custody`,
  CONTACT_PRINT: `${apiVersion}/PatientContact/html`,
  CONTACT_LANGUAGE: `${apiVersion}/PatientContact/languages`,
  CONTACT_PREFERRED: `${apiVersion}/PatientContact/preferred-contacts`,
  EXPIRED: (id = '') => `${apiVersion}/PatientContact/${id}/expire`,
  UN_EXPIRED: (id = '') => `${apiVersion}/PatientContact/${id}/un-expire`,
};

const CYBHI_DEFAULT = `${apiVersion}/cybhi`;
export const API_CYBHI = {
  DEFAULT: `${CYBHI_DEFAULT}`,
  PATIENT_LOOKUP: `${CYBHI_DEFAULT}/lookup`,
};

export const API_FORM_TEMPLATE = `${apiVersion}/FormTemplate`;

const RATE_CODE_DEFAULT = `${apiVersion}/RateCode`;
export const API_RATE_CODE = {
  DEFAULT: (id = '') => `${RATE_CODE_DEFAULT}/${id}`,
  PRINT: (id = '') => `${RATE_CODE_DEFAULT}/${id}/html`,
  RATE_CODE_LOOKUP: `${RATE_CODE_DEFAULT}/lookup-data`,
};

const HEALTH_PLAN_DEFAULT = `${apiVersion}/health-plan`;
export const API_HEALTH_PLAN = {
  DEFAULT: (id = '') => `${HEALTH_PLAN_DEFAULT}/${id}`,
  PRINT: (id = '') => `${HEALTH_PLAN_DEFAULT}/${id}/html`,
  HEALTH_PLAN_TYPE_LOOKUPS: `${HEALTH_PLAN_DEFAULT}/lookup`,
};

const CLOSURE_DEFAULT = `${apiVersion}/Closure`;
export const API_CLOSURE = {
  DEFAULT: (id = '') => `${CLOSURE_DEFAULT}/${id}`,
  DETAIL: (id) => `${CLOSURE_DEFAULT}/${id}`,
  PRINT: (id = '') => `${CLOSURE_DEFAULT}/${id}/html`,
  ENROLLMENT: `${CLOSURE_DEFAULT}/enrollment`,
  CONFLICT_DATA: `${CLOSURE_DEFAULT}/conflict-data`,
  LOOKUP_DATA: `${CLOSURE_DEFAULT}/lookup-data`,
  PREVIOUS_DATA: `${CLOSURE_DEFAULT}/previous-data`,
  REOPEN: `${CLOSURE_DEFAULT}/reopen`,
};

const DAILY_EXTRACT_DEFAULT = `${apiVersion}/daily-extract`;
export const API_DAILY_EXTRACT = {
  DEFAULT: DAILY_EXTRACT_DEFAULT,
  DOWNLOAD: `${DAILY_EXTRACT_DEFAULT}/download`,
  EXPLORER: `${DAILY_EXTRACT_DEFAULT}/explorer`,
};

const ENCOUNTER_FORM_MANAGEMENT_DEFAULT = `${apiVersion}/EncounterFormManagement`;
export const API_ENCOUNTER_FORM_MANAGEMENT = {
  DEFAULT: ENCOUNTER_FORM_MANAGEMENT_DEFAULT,
  SERVICE_CODES: `${ENCOUNTER_FORM_MANAGEMENT_DEFAULT}/service-codes`,
  SERVICE_SITES: `${ENCOUNTER_FORM_MANAGEMENT_DEFAULT}/service-sites`,
  SERVICE_CODES_LOOKUP: `${ENCOUNTER_FORM_MANAGEMENT_DEFAULT}/service-codes/lookup`,
  GRID_CATEGORIES: `${ENCOUNTER_FORM_MANAGEMENT_DEFAULT}/service-grid-categories`,
  GRID_CATEGORIES_LOOKUP: `${ENCOUNTER_FORM_MANAGEMENT_DEFAULT}/service-grid-categories/lookup`,
  PROGRAM: `${ENCOUNTER_FORM_MANAGEMENT_DEFAULT}/program`,
  DELETE_ENCOUNTER_PROGRAM: (id) => `${ENCOUNTER_FORM_MANAGEMENT_DEFAULT}/program/${id}`,
};

const FOSTER_HOME_DEFAULT = `${apiVersion}/foster-home`;
export const API_FOSTER_HOME = {
  DEFAULT: FOSTER_HOME_DEFAULT,
  FOSTER_HOME_DETAIL: (fosterHomeId = '') => `${FOSTER_HOME_DEFAULT}/${fosterHomeId}`,
  FOSTER_HOME_LOOKUP_DATA: `${FOSTER_HOME_DEFAULT}/lookup-data`,
  FOSTER_HOME_CHILD_PRINT: `${FOSTER_HOME_DEFAULT}/case-note/html`,
  FOSTER_HOME_PRINT: (id) => `${FOSTER_HOME_DEFAULT}/${id}/html`,
  FOSTER_HOME_DASHBOARD: (fosterHomeId) => `${FOSTER_HOME_DEFAULT}/${fosterHomeId}/dashboard`,

  //foster address
  FOSTER_HOME_ADDRESS_HISTORY: `${FOSTER_HOME_DEFAULT}/address`,
  //foster license
  FOSTER_HOME_LICENSE_HISTORIES: `${FOSTER_HOME_DEFAULT}/license`,
  FOSTER_HOME_LICENSE_LOOKUP_DATA: `${FOSTER_HOME_DEFAULT}/license/lookup-data`,
  //foster requirement
  FOSTER_HOME_REQUIREMENT_HISTORIES: `${FOSTER_HOME_DEFAULT}/requirement`,
  FOSTER_HOME_REQUIREMENT_LOOKUP_DATA: `${FOSTER_HOME_DEFAULT}/requirement/lookup-data`,
  //foster case note
  FOSTER_HOME_CASE_NOTE: `${FOSTER_HOME_DEFAULT}/case-note`,
  FOSTER_HOME_CASE_NOTE_BY_ID: (id) => `${FOSTER_HOME_DEFAULT}/case-note/${id}`,
  FOSTER_HOME_CASE_NOTE_PRINT: (id) => `${FOSTER_HOME_DEFAULT}/case-note/${id}/html`,
  FOSTER_HOME_CASE_NOTE_TYPE: `${FOSTER_HOME_DEFAULT}/case-note/note-types`,

  //foster case member
  FOSTER_HOME_MEMBER: `${FOSTER_HOME_DEFAULT}/member`,
  FOSTER_HOME_MEMBER_DETAIL: (guardianId) => `${FOSTER_HOME_DEFAULT}/member/${guardianId}`,
  FOSTER_HOME_MEMBER_REQUIREMENT: (id) => `${FOSTER_HOME_DEFAULT}/member/${id}/requirements`,
  FOSTER_HOME_MEMBER_DELETE_REQUIREMENT: (id) => `${FOSTER_HOME_DEFAULT}/member/requirements/${id}`,
  FOSTER_HOME_MEMBER_LOOKUP: `${FOSTER_HOME_DEFAULT}/member/lookup-data`,
  FOSTER_HOME_MEMBER_SAVE_REQUIREMENT: `${FOSTER_HOME_DEFAULT}/member/requirements`,

  UPDATE_FOSTER_HOME_MEMBER_PHOTO: `${FOSTER_HOME_DEFAULT}/member/photo`,
  EXPIRE_FOSTER_HOME_MEMBER_PHOTO: (guardianId) =>
    `${FOSTER_HOME_DEFAULT}/member/${guardianId}/photo`,
  //foster child assignment
  FOSTER_HOME_CHILD_ASSIGNMENT_HISTORY: `${FOSTER_HOME_DEFAULT}/child-assignment`,
  DELETE_FOSTER_HOME_CHILD_ASSIGNMENT: (fosterHomeClientId = '') =>
    `${FOSTER_HOME_DEFAULT}/child-assignment/${fosterHomeClientId}`,
  FOSTER_HOME_CHILD_ASSIGNMENT_LOOKUP_DATA: `${FOSTER_HOME_DEFAULT}/child-assignment/lookup-data`,
  FOSTER_HOME_CHILD_ASSIGNMENT_CASE_NOTE_HISTORY: `${FOSTER_HOME_DEFAULT}/child-assignment/case-notes`,
  FOSTER_HOME_CHILD_ASSIGNMENT_CASE_NOTE_DETAIL: `${FOSTER_HOME_DEFAULT}/child-assignment/case-notes/form`,
  CHILD_ASSIGNMENT_CASE_NOTE_TYPES: `${FOSTER_HOME_DEFAULT}/child-assignment/case-notes/note-types`,
  DELETE_FOSTER_HOME_PHOTO: (fosterHomeId) => `${FOSTER_HOME_DEFAULT}/${fosterHomeId}/photo`,
};

const GROUP_EDITOR_DEFAULT = `${apiVersion}/group-editor`;
export const API_GROUP_EDITOR = {
  DEFAULT: GROUP_EDITOR_DEFAULT,
  GET_DETAIL: (id) => `${GROUP_EDITOR_DEFAULT}/${id}`,
  PRINT: (id) => `${GROUP_EDITOR_DEFAULT}/${id}/html`,
  ASSIGN_PATIENT: `${GROUP_EDITOR_DEFAULT}/assigned-patient`,
  LOOKUP: `${GROUP_EDITOR_DEFAULT}/lookup-data`,
  SEARCH_PATIENT: `${GROUP_EDITOR_DEFAULT}/patients`,
};

const GROUP_NOTE_DEFAULT = `${apiVersion}/group-note`;
export const API_GROUP_NOTE = {
  DEFAULT: GROUP_NOTE_DEFAULT,
  SEARCH_PATIENT: `${GROUP_NOTE_DEFAULT}/patients-lookup`,
  LOOKUP: `${GROUP_NOTE_DEFAULT}/lookup`,
  CHANGE_FACILITATOR: `${GROUP_NOTE_DEFAULT}/change-facilitator`,
  SERVICE_SITES: `${GROUP_NOTE_DEFAULT}/service-sites-lookup`,
  SERVICES_LOOKUP: `${GROUP_NOTE_DEFAULT}/services-lookup`,
  IDENTIFIED_LOOKUP: `${GROUP_NOTE_DEFAULT}/identified-needs-lookup`,
  PRINT: `${GROUP_NOTE_DEFAULT}/html`,
  VALIDATE: `${GROUP_NOTE_DEFAULT}/validate`,
  AUTO_SAVE: `${GROUP_NOTE_DEFAULT}/auto-save`,
};

const SUPPORT_DEFAULT = `${apiVersion}/support`;
export const API_SUPPORT = {
  DEFAULT: SUPPORT_DEFAULT,
  ACTIVE_EMPLOYEES_LOOKUP: `${SUPPORT_DEFAULT}/active-employees-lookup`,
  PROGRESS_NOTES_AUTO_SAVE: `${SUPPORT_DEFAULT}/auto-save/progress-note`,
  PROGRESS_NOTES: `${SUPPORT_DEFAULT}/prog-notes`,
  PROGRESS_NOTES_ORG_FIELDS: `${SUPPORT_DEFAULT}/prog-note-org-fields`,
  REACTIVE_NOTES: (id) => `${SUPPORT_DEFAULT}/auto-save/progress-note/${id}/re-activate`,
  DYNAMIC_FORM_AUTO_SAVE: `${SUPPORT_DEFAULT}/auto-save/dynamic-form`,
  PRESCRIBER_LOOKUP: `${SUPPORT_DEFAULT}/prescriber-lookup`,
  PRESCRIPTIONS: `${SUPPORT_DEFAULT}/prescriptions`,
  RESEND: (id) => `${SUPPORT_DEFAULT}/resend-prescription/${id}`,
  ENROLL: `${SUPPORT_DEFAULT}/enroll`,
  LOOKUP_TABLE: `${SUPPORT_DEFAULT}/lookup-table`,
  LOOKUP_TABLE_DATA: `${SUPPORT_DEFAULT}/lookup-table/data`,
  LOOKUP_TABLE_METADATA: `${SUPPORT_DEFAULT}/lookup-table/metadata`,
  USER_INFO: `${SUPPORT_DEFAULT}/user-info`,
  RESET_PASSWORD: (username) => `${SUPPORT_DEFAULT}/${username}/reset-pass`,
  COPY_ROLES: `${SUPPORT_DEFAULT}/copy-roles`,
  EMPLOYEE_LOOKUP: `${SUPPORT_DEFAULT}/transferable-rights-employees-lookup`,
  CONFIGURATION: `${SUPPORT_DEFAULT}/configuration`,
  PUT_CONFIGURATION: (id) => `${SUPPORT_DEFAULT}/configuration/${id}`,

  // New Account
  ACCOUNT_DEFAULT: `${SUPPORT_DEFAULT}/account`,
  EMPLOYEE_WITHOUT_ACCOUNT: `${SUPPORT_DEFAULT}/account/employee-without-account`,
  AVAILABLE_LOGINS: `${SUPPORT_DEFAULT}/account/available-logins`,
};

const IMMUNIZATION_DEFAULT = `${apiVersion}/Immunization`;
export const API_IMMUNIZATION = {
  GET_IMMUNIZATION_HISTORIES: IMMUNIZATION_DEFAULT,
  POST_SAVE_IMMUNIZATION: IMMUNIZATION_DEFAULT,
  GET_IMMUNIZATION_DETAIL: (immunizationId) => `${IMMUNIZATION_DEFAULT}/${immunizationId}`,
  DELETE_IMMUNIZATION: (immunizationId) => `${IMMUNIZATION_DEFAULT}/${immunizationId}`,
  POST_SEND_HL7_FILE: (immunizationId) => `${IMMUNIZATION_DEFAULT}/${immunizationId}/hl7-file`,
  POST_GENERATE_REGISTRY_SUBMISSION: (immunizationId) =>
    `${IMMUNIZATION_DEFAULT}/${immunizationId}/registry-submission`,
  GET_PRINT_IMMUNIZATIONS: `${IMMUNIZATION_DEFAULT}/html`,
  GET_IMMUNIZATION_LOOKUP_DATA: `${IMMUNIZATION_DEFAULT}/lookup-data`,
  GET_IMMUNIZATION_REGISTRY_SETTING: `${IMMUNIZATION_DEFAULT}/registry-setting`,
  POST_SAVE_IMMUNIZATION_REGISTRY_SETTING: `${IMMUNIZATION_DEFAULT}/registry-setting`,
};

const SUPPORT_MFA_DEFAULT = `${apiVersion}/support/mfa`;
export const API_SUPPORT_MFA = {
  GET_MFA_REQUEST_HISTORY: SUPPORT_MFA_DEFAULT,
  POST_BYPASS_MFA: `${SUPPORT_MFA_DEFAULT}/bypass`,
  POST_CREATE_MFA_REQUEST: `${SUPPORT_MFA_DEFAULT}/request`,
  GET_MFA_REQUEST_TYPES: `${SUPPORT_MFA_DEFAULT}/request-type`,
  PUT_APPROVE_MFA_REQUEST: (mfaRequestId) => `${SUPPORT_MFA_DEFAULT}/request/${mfaRequestId}`,
  POST_RESET_MFA: `${SUPPORT_MFA_DEFAULT}/reset`,
};

const DATA_IMPORT_DEFAULT = `${apiVersion}/data-import`;
export const API_DATA_IMPORT = {
  DEFAULT: DATA_IMPORT_DEFAULT,
  DETAIL: (id) => `${DATA_IMPORT_DEFAULT}/${id}`,
  LAUNCH: (id) => `${DATA_IMPORT_DEFAULT}/${id}/launch`,
  FILE_DETAIL: (id) => `${DATA_IMPORT_DEFAULT}/${id}/file`,
  VALIDATION_DETAIL: (id, filter) => `${DATA_IMPORT_DEFAULT}/${id}/validation-details/${filter}`,
  TABLE_PREVIEW: (tableName) => `${DATA_IMPORT_DEFAULT}/${tableName}/preview`,
  RETRIEVE_TABLE_DIAGRAM: `${DATA_IMPORT_DEFAULT}/diagram`,
  RETRIEVE_TABLE_LIST: `${DATA_IMPORT_DEFAULT}/tables`,
  IMPORT: `${DATA_IMPORT_DEFAULT}/csv`,
  PREVIEW: `${DATA_IMPORT_DEFAULT}/csv/preview`,
  RESTORE_SQL: `${DATA_IMPORT_DEFAULT}/sql`,
};

const EXAM_OPTIONS_DEFAULT = `${apiVersion}/exam`;
export const API_EXAM_OPTIONS = {
  DEFAULT: EXAM_OPTIONS_DEFAULT,
  LOOKUP_DATA: `${EXAM_OPTIONS_DEFAULT}/lookup`,
  OPTIONS: `${EXAM_OPTIONS_DEFAULT}/option`,
};

const SERVICE_PLAN_FIELD_MANAGER_DEFAULT = `${apiVersion}/service-plan-field-manager`;
export const API_SERVICE_PLAN_FIELD_MANAGER = {
  GET_SERVICE_PLAN_SERVICE_CODE_ALIAS: `${SERVICE_PLAN_FIELD_MANAGER_DEFAULT}/service-plan-alias`,
  POST_SAVE_SERVICE_PLAN_SERVICE_CODE_ALIAS: `${SERVICE_PLAN_FIELD_MANAGER_DEFAULT}/service-plan-alias`,
  GET_SERVICE_PLAN_CATEGORIES: `${SERVICE_PLAN_FIELD_MANAGER_DEFAULT}/service-plan-categories`,
  POST_SAVE_SERVICE_PLAN_FIELD: `${SERVICE_PLAN_FIELD_MANAGER_DEFAULT}/service-plan-field`,
  GET_OPTION_SERVICE_PLAN_FIELDS: `${SERVICE_PLAN_FIELD_MANAGER_DEFAULT}/service-plan-field-options`,
  GET_SERVICE_PLAN_FIELD_TYPES: `${SERVICE_PLAN_FIELD_MANAGER_DEFAULT}/service-plan-field-types`,
  GET_SERVICE_PLAN_FIELDS: `${SERVICE_PLAN_FIELD_MANAGER_DEFAULT}/service-plan-fields`,
  POST_SAVE_SERVICE_PLAN_MAPPING_FIELD: `${SERVICE_PLAN_FIELD_MANAGER_DEFAULT}/service-plan-mapping-field`,
  GET_SERVICE_PLAN_MAPPING_FIELDS: `${SERVICE_PLAN_FIELD_MANAGER_DEFAULT}/service-plan-mapping-fields`,
  GET_SERVICE_PLAN_SECTIONS: `${SERVICE_PLAN_FIELD_MANAGER_DEFAULT}/service-plan-sections`,
  GET_SERVICE_PLAN_SERVICE_CODES: `${SERVICE_PLAN_FIELD_MANAGER_DEFAULT}/service-plan-service-codes`,
  GET_SERVICE_PLAN_TYPES: `${SERVICE_PLAN_FIELD_MANAGER_DEFAULT}/service-plan-types`,
  GET_SERVICE_PLAN_DROPDOWN_OPTIONS: `${SERVICE_PLAN_FIELD_MANAGER_DEFAULT}/service-plan-field-dropdown-options`,
};

const SERVICE_PLAN_SIGNATURE_DEFAULT_MANAGEMENT = `${apiVersion}/service-plan-signature-management`;
export const API_SERVICE_PLAN_SIGNATURE_MANAGEMENT = {
  GET_SERVICE_PLAN_SIGNATURE: SERVICE_PLAN_SIGNATURE_DEFAULT_MANAGEMENT,
  POST_SAVE_SERVICE_PLAN_SIGNATURE: SERVICE_PLAN_SIGNATURE_DEFAULT_MANAGEMENT,
  GET_SERVICE_PLAN_SIGNATURE_TYPES: `${SERVICE_PLAN_SIGNATURE_DEFAULT_MANAGEMENT}/signature-types`,
  DELETE_SERVICE_PLAN_SIGNATURE: (servicePlanSignatureMapId) =>
    `${SERVICE_PLAN_SIGNATURE_DEFAULT_MANAGEMENT}/${servicePlanSignatureMapId}`,
};

const HEALTH_INFORMATION_DEFAULT = `${apiVersion}/health-information`;
export const API_HEALTH_INFORMATION = {
  POST_CREATE_NEW_RELEASE: HEALTH_INFORMATION_DEFAULT,
  PUT_EDIT_RESOLVE_RELEASE: (releaseId) => `${HEALTH_INFORMATION_DEFAULT}/${releaseId}`,
  DELETE_RELEASE: (releaseId) => `${HEALTH_INFORMATION_DEFAULT}/${releaseId}`,
  GET_ALL_MEDICAL_RECORDS_RELEASES_BY_PATIENT: `${HEALTH_INFORMATION_DEFAULT}/by-client`,
  GET_ALL_MEDICAL_RECORDS_RELEASES_BY_CURRENT_EMPLOYEE: `${HEALTH_INFORMATION_DEFAULT}/by-employee`,
  GET_LOOKUP_DATA: `${HEALTH_INFORMATION_DEFAULT}/lookup`,
  PRINT: `${HEALTH_INFORMATION_DEFAULT}/html`,
};

const API_ID_ME_DEFAULT = `${apiVersion}/IdMeIdentity`;
export const API_ID_ME = {
  AUTHENTICATE: `${API_ID_ME_DEFAULT}/authenticate`,
  GET_STATUS: `${API_ID_ME_DEFAULT}/authenticate/status`,
  AUTHORIZE_BUTTON: `${API_ID_ME_DEFAULT}/authorize`,
};

const CLINICAL_FORMS_SEARCH_DEFAULT = `${apiVersion}/clinical-forms-search`;
export const API_CLINICAL_FORMS_SEARCH = {
  DEFAULT: CLINICAL_FORMS_SEARCH_DEFAULT,
  DOWNLOAD_ENCOUNTERS: `${CLINICAL_FORMS_SEARCH_DEFAULT}/download`,
  DYNAMIC_FORM_TYPES: `${CLINICAL_FORMS_SEARCH_DEFAULT}/dynamic-form-types`,
  SEARCH_DYNAMIC_FORMS: `${CLINICAL_FORMS_SEARCH_DEFAULT}/dynamic-forms`,
  SEARCH_EMPLOYEE: `${CLINICAL_FORMS_SEARCH_DEFAULT}/employees`,
  SEARCH_PATIENTS: `${CLINICAL_FORMS_SEARCH_DEFAULT}/patients`,
  GET_PROGRESS_NOTE_TYPES: `${CLINICAL_FORMS_SEARCH_DEFAULT}/note-types`,
  GET_LIST_PAYORS: `${CLINICAL_FORMS_SEARCH_DEFAULT}/payors`,
  PRINT: `${CLINICAL_FORMS_SEARCH_DEFAULT}/html`,
  RETURN_FOR_EDIT: `${CLINICAL_FORMS_SEARCH_DEFAULT}/return-for-edit`,
};

const QUALITY_MANAGEMENT_DEFAULT = `${apiVersion}/quality-management`;
export const API_QUALITY_MANAGEMENT = {
  //Incident report
  GET_INCIDENT_REPORT_HISTORY: `${QUALITY_MANAGEMENT_DEFAULT}/incident-report`,
  POST_CREATE_NEW_INCIDENT_REPORT: `${QUALITY_MANAGEMENT_DEFAULT}/incident-report`,
  GET_INCIDENT_REPORT_DETAIL: (incidentReportId) =>
    `${QUALITY_MANAGEMENT_DEFAULT}/incident-report/${incidentReportId}`,
  PUT_UPDATE_EXISTS_INCIDENT_REPORT: (incidentReportId) =>
    `${QUALITY_MANAGEMENT_DEFAULT}/incident-report/${incidentReportId}`,
  GET_PRINT_INCIDENT_REPORT: `${QUALITY_MANAGEMENT_DEFAULT}/incident-report/html`,
  GET_INCIDENT_REPORT_LOOKUP_DATA: `${QUALITY_MANAGEMENT_DEFAULT}/incident-report/lookup`,

  //Cga
  GET_CGA_HISTORY: `${QUALITY_MANAGEMENT_DEFAULT}/cga`,
  POST_CREATE_NEW_CGA: `${QUALITY_MANAGEMENT_DEFAULT}/cga`,
  GET_CGA_DETAIL: (cgaId) => `${QUALITY_MANAGEMENT_DEFAULT}/cga/${cgaId}`,
  PUT_UPDATE_EXISTS_CGA: (cgaId) => `${QUALITY_MANAGEMENT_DEFAULT}/cga/${cgaId}`,
  GET_PRINT_CGA: `${QUALITY_MANAGEMENT_DEFAULT}/cga/html`,
  GET_CGA_LOOKUP_DATA: `${QUALITY_MANAGEMENT_DEFAULT}/cga/lookup`,
};

const CALL_OUTSIDE_DEFAULT = `/call-outside`;
export const API_CALL_OUTSIDE = {
  FAVORITE_PATIENTS: `${CALL_OUTSIDE_DEFAULT}/favorite-patients`,
  ADD_PATIENT_TO_FAVORITE: (id) => `${CALL_OUTSIDE_DEFAULT}/favorite-patients/${id}`,
  HISTORY: `${CALL_OUTSIDE_DEFAULT}/history`,
  GET_TOKEN: `${CALL_OUTSIDE_DEFAULT}/access-token`,
  START_STREAM: (callSid) => `${CALL_OUTSIDE_DEFAULT}/${callSid}/streaming`,
  WS_STREAM_LISTEN: (streamId) =>
    `${envVariables.apiUrl.replace(
      'https://',
      'wss://',
    )}${CALL_OUTSIDE_DEFAULT}/transcription-stream/listen?streamSid=${streamId}`,
  GET_RECORD_FILE: (callSid) => `/call-outside/${callSid}/recording`,
  RECORD_AUDIO: (callSid) => `${CALL_OUTSIDE_DEFAULT}/${callSid}/recording`,
  UPDATE_PATIENT: `${CALL_OUTSIDE_DEFAULT}/patient-contact`,
  CONTRACT_STATUS: `${CALL_OUTSIDE_DEFAULT}/patient-contact-status`,
};

const API_RE_ASSIGNMENT_DEFAULT = `${apiVersion}/ReAssignment`;
export const API_RE_ASSIGNMENT = {
  DEFAULT: API_RE_ASSIGNMENT_DEFAULT,
  SITE_LOOKUP: `${API_RE_ASSIGNMENT_DEFAULT}/employee-sites-lookup`,
  EMPLOYEE_LOOKUP: `${API_RE_ASSIGNMENT_DEFAULT}/employees-lookup`,
  JOB_TYPE_LOOKUP: `${API_RE_ASSIGNMENT_DEFAULT}/job-types-lookup`,
  ORIGINAL_SITE_LOOKUP: `${API_RE_ASSIGNMENT_DEFAULT}/original-sites-lookup`,
  POPULATIONS_LOOKUP: `${API_RE_ASSIGNMENT_DEFAULT}/populations-lookup`,
  GET_CLIENT_ASSIGNED: `${API_RE_ASSIGNMENT_DEFAULT}/patient`,
  SITE_TYPES_LOOKUP: `${API_RE_ASSIGNMENT_DEFAULT}/site-types-lookup`,
};

const API_PATIENT_ACCESS_REQUEST_DEFAULT = `${apiVersion}/patient-access-request`;
export const API_PATIENT_ACCESS_REQUEST = {
  DEFAULT: API_PATIENT_ACCESS_REQUEST_DEFAULT,
  CHECK_PATIENT_ACCESS: `${API_PATIENT_ACCESS_REQUEST_DEFAULT}/is-allowed`,
  CHECK_PERMISSION: `${API_PATIENT_ACCESS_REQUEST_DEFAULT}/is-enabled`,
  REASON_LOOKUP: `${API_PATIENT_ACCESS_REQUEST_DEFAULT}/reason-lookup`,
};

const API_EMPLOYEE_NOTE_DEFAULT = `${apiVersion}/crm/employee-note`;
export const API_EMPLOYEE_NOTE = {
  DEFAULT: API_EMPLOYEE_NOTE_DEFAULT,
  DELETE: (id) => `${API_EMPLOYEE_NOTE_DEFAULT}/${id}`,
};

const API_CRM_TASK_DEFAULT = `${apiVersion}/crm/task`;
export const API_CRM_TASK = {
  GET_POTENTIAL_CLIENT_TASKS_BY_CURRENT_USER: API_CRM_TASK_DEFAULT,
  POST_SAVE_POTENTIAL_CLIENT_TASK: API_CRM_TASK_DEFAULT,
  PUT_COMPLETE_POTENTIAL_CLIENT_TASK: (taskId = '') => `${API_CRM_TASK_DEFAULT}/${taskId}`,
  DELETE_POTENTIAL_CLIENT_TASK: (taskId = '') => `${API_CRM_TASK_DEFAULT}/${taskId}`,
  GET_TASK_TAG_CATEGORIES: `${API_CRM_TASK_DEFAULT}/category`,
  POST_SAVE_TASK_TAG_CATEGORY: `${API_CRM_TASK_DEFAULT}/category`,
  DELETE_TASK_TAG_CATEGORY: (categoryId = '') => `${API_CRM_TASK_DEFAULT}/category/${categoryId}`,
  GET_TASK_PRIORITIES: `${API_CRM_TASK_DEFAULT}/priority`,
  GET_TASK_TAGS: `${API_CRM_TASK_DEFAULT}/tag`,
  POST_SAVE_TASK_TAG: `${API_CRM_TASK_DEFAULT}/tag`,
  DELETE_TASK_TAG: (tagId = '') => `${API_CRM_TASK_DEFAULT}/tag/${tagId}`,
};

const API_CRM_CLIENT_DEFAULT = `${apiVersion}/crm`;
export const API_CRM_CLIENT = {
  DEFAULT: API_CRM_CLIENT_DEFAULT,
  UPDATE_CLIENT: (id = '') => `${API_CRM_CLIENT_DEFAULT}/${id}`,
  SOURCE_LOOKUP: `${API_CRM_CLIENT_DEFAULT}/source`,
  STATUS_LOOKUP: `${API_CRM_CLIENT_DEFAULT}/status`,
  DATA_LOOKUP: `${API_CRM_CLIENT_DEFAULT}/lookup-data`,
  CLIENT_TAG: (id = '') => `${API_CRM_CLIENT_DEFAULT}/client-tag/${id}`,
  CLIENT_TAG_CATEGORY: (id = '') => `${API_CRM_CLIENT_DEFAULT}/client-tag/category/${id}`,
  CLIENT_STATUS: `${API_CRM_CLIENT_DEFAULT}/client-status`,
  CLIENT_STATUS_HISTORY: (id = '') => `${API_CRM_CLIENT_DEFAULT}/${id}/client-status`,
  EXPORT: `${API_CRM_CLIENT_DEFAULT}/export`,
  SEND_EMAIL: `${API_CRM_CLIENT_DEFAULT}/send-email`,
  AUTOMATED_TRIGGERS: `${API_CRM_CLIENT_DEFAULT}/automated-trigger`,
  AUTOMATED_TRIGGERS_LOOK_UP: `${API_CRM_CLIENT_DEFAULT}/automated-trigger/lookup-data`,
  DOWNLOAD_IMPORT_TEMPLATE: `${API_CRM_CLIENT_DEFAULT}/import/template`,
  REVIEW_IMPORT_DATA: `${API_CRM_CLIENT_DEFAULT}/import/preview`,
  VALIDATE_IMPORT_DATA: `${API_CRM_CLIENT_DEFAULT}/import/validation`,
  UPDATE_CLIENTS_NOTE: `${API_CRM_CLIENT_DEFAULT}/client-note`,

  //ANALYTIC
  ACQUISITION_CHANNELS: `${API_CRM_CLIENT_DEFAULT}/analytic/acquisition-channels`,
  CHURN_RATE: `${API_CRM_CLIENT_DEFAULT}/analytic/churn-rate`,
  CONVERSION_RATE: `${API_CRM_CLIENT_DEFAULT}/analytic/conversion-rate`,
  LEAD_SOURCES: `${API_CRM_CLIENT_DEFAULT}/analytic/lead-sources`,
  STATUS_DISTRIBUTION: `${API_CRM_CLIENT_DEFAULT}/analytic/status-distribution`,
  TIME_TO_CONVERSION: `${API_CRM_CLIENT_DEFAULT}/analytic/time-to-conversion`,
};

//SECTION IN DYNAMIC REPORT
const API_RECENT_REPORTS_DEFAULT = `${apiVersion}/recent-report`;
export const API_RECENT_REPORTS = {
  GET_RECENT_REPORTS: API_RECENT_REPORTS_DEFAULT,
  DELETE_REPORT: (recentReportId) => `${API_RECENT_REPORTS_DEFAULT}/${recentReportId}`,
  POST_EXCUTE_REPORT: (recentReportId) => `${API_RECENT_REPORTS_DEFAULT}/${recentReportId}/execute`,
  GET_PARAMETER_VALUES_BY_RECENT_REPORT: (recentReportId) =>
    `${API_RECENT_REPORTS_DEFAULT}/${recentReportId}/parameter-values`,
  POST_SCHEDULE_REPORT: (recentReportId) =>
    `${API_RECENT_REPORTS_DEFAULT}/${recentReportId}/schedule`,
  GET_STATUS_COUNT_OF_ALL_RECENT_REPORTS: `${API_RECENT_REPORTS_DEFAULT}/status-count`,
  GET_MAT_RECENT_REPORTS: `${API_RECENT_REPORTS_DEFAULT}/mat`,
  GET_MAT_STATUS_COUNT: `${API_RECENT_REPORTS_DEFAULT}/mat/status-count`,
  DELETE_CANCEL_SCHEDULED_REPORT: (recentReportId) =>
    `${API_RECENT_REPORTS_DEFAULT}/${recentReportId}/cancel`,
  SHARE_COMPLETED_REPORT: (recentReportId) =>
    `${API_RECENT_REPORTS_DEFAULT}/${recentReportId}/share`,
  MOVE_TO_CLOUD_STORAGE: (recentReportId) =>
    `${API_RECENT_REPORTS_DEFAULT}/${recentReportId}/move-to-cloud-storage`,
};

const API_DIAGNOSIS_CODE_DEFAULT = `${apiVersion}/diagnosis-code-editor`;
export const API_DIAGNOSIS_CODE = {
  DEFAULT: API_DIAGNOSIS_CODE_DEFAULT,
  GET_DETAIL: (id = '') => `${API_DIAGNOSIS_CODE_DEFAULT}/${id}`,
};

const API_QUICK_FILTER_GROUP_DEFAULT = `${apiVersion}/quick-filter-group`;
export const API_QUICK_FILTER_GROUP = {
  DEFAULT: API_QUICK_FILTER_GROUP_DEFAULT,
  DETAIL: (id = '') => `${API_QUICK_FILTER_GROUP_DEFAULT}/${id}`,
};

const API_GAD_7_DEFAULT = `${apiVersion}/Gad`;
export const API_GAD_7 = {
  DEFAULT: (batchId = '') => `${API_GAD_7_DEFAULT}/${batchId}`,
  PRINT: `${API_GAD_7_DEFAULT}/html`,
  PRINT_ALL: `${API_GAD_7_DEFAULT}/batches/html`,
  GET_QUESTION: `${API_GAD_7_DEFAULT}/questions`,
};

const API_PHQ_A_DEFAULT = `${apiVersion}/PhqA`;
export const API_PHQ_A = {
  DEFAULT: (batchId = '') => `${API_PHQ_A_DEFAULT}/${batchId}`,
  PRINT: `${API_PHQ_A_DEFAULT}/html`,
  GET_QUESTION: `${API_PHQ_A_DEFAULT}/questions`,
};

const API_PRAPARE_DEFAULT = `${apiVersion}/PRAPARE`;
export const API_PRAPARE = {
  DEFAULT: (batchId = '') => `${API_PRAPARE_DEFAULT}/${batchId}`,
  PRINT: `${API_PRAPARE_DEFAULT}/html`,
  GET_QUESTION: `${API_PRAPARE_DEFAULT}/questions`,
};

const API_COWS_DEFAULT = `${apiVersion}/cows`;
export const API_COWS = {
  DEFAULT: (batchId = '') => `${API_COWS_DEFAULT}/${batchId}`,
  PRINT: `${API_COWS_DEFAULT}/html`,
  GET_QUESTION: `${API_COWS_DEFAULT}/questions`,
};

const API_ENGAGEMENT_DEFAULT = `${apiVersion}/engagement`;
export const API_ENGAGEMENT = {
  DEFAULT: (engagementId = '') => `${API_ENGAGEMENT_DEFAULT}/${engagementId}`,
  PRINT: (engagementId = '') => `${API_ENGAGEMENT_DEFAULT}/${engagementId}/html`,
  GET_QUESTION: `${API_ENGAGEMENT_DEFAULT}/questions`,
  GET_STATUS: `${API_ENGAGEMENT_DEFAULT}/status`,
};

const COT_DEFAULT = `${apiVersion}/CourtOrderedTreatment`;
export const API_COT = {
  DEFAULT: (COTId = '') => `${COT_DEFAULT}/${COTId}`,
  ATTORNEYS: `${COT_DEFAULT}/attorneys`,
  HISTORY: `${COT_DEFAULT}/history`,
  REASONS: `${COT_DEFAULT}/reasons`,
  TYPES: `${COT_DEFAULT}/types`,

  //AMENDMENT
  AMENDMENT: (amendmentId = '') => `${COT_DEFAULT}/amendment/${amendmentId}`,
  AMENDMENTS: `${COT_DEFAULT}/amendments`,

  //COMMENT
  COMMENT: (commentId = '') => `${COT_DEFAULT}/comment/${commentId}`,
  COMMENTS: `${COT_DEFAULT}/comments`,

  //STATUS REPORT
  STATUS_REPORT: (statusReportId = '') => `${COT_DEFAULT}/status-report/${statusReportId}`,
  STATUS_REPORTS: `${COT_DEFAULT}/status-reports`,
  STATUS_REPORT_TYPES: `${COT_DEFAULT}/status-report/types`,

  //JUDICIAL REVIEWS
  JUDICIAL_REVIEW: (judicialReviewId = '') => `${COT_DEFAULT}/judicial-review/${judicialReviewId}`,
  JUDICIAL_REVIEWS: `${COT_DEFAULT}/judicial-reviews`,
};

const API_ENCOUNTER_ENTRY_DEFAULT = `${apiVersion}/encounter-entry`;
export const API_ENCOUNTER_ENTRY = {
  DEFAULT: `${API_ENCOUNTER_ENTRY_DEFAULT}/encounter-form`,
  SAVE: API_ENCOUNTER_ENTRY_DEFAULT,
  GET_ENCOUNTER_TYPE: `${API_ENCOUNTER_ENTRY_DEFAULT}/encounter-type`,
  SEARCH_PATIENTS: `${API_ENCOUNTER_ENTRY_DEFAULT}/patient`,
  SEARCH_EMPLOYEES: `${API_ENCOUNTER_ENTRY_DEFAULT}/employee`,
  GET_POS: `${API_ENCOUNTER_ENTRY_DEFAULT}/pos`,
  GET_SERVICE_POS_RATE: `${API_ENCOUNTER_ENTRY_DEFAULT}/pos-rate`,
  GET_RATE_CODE: `${API_ENCOUNTER_ENTRY_DEFAULT}/rate-code`,
  GET_SERVICES: `${API_ENCOUNTER_ENTRY_DEFAULT}/service`,
  GET_SERVICE_SITES: `${API_ENCOUNTER_ENTRY_DEFAULT}/service-site`,
};

const API_MEDICATION_ASSISTED_DEFAULT = `${apiVersion}/medication-assisted`;
export const API_MEDICATION_ASSISTED = {
  DEFAULT: API_MEDICATION_ASSISTED_DEFAULT,
  INVENTORY: `${API_MEDICATION_ASSISTED_DEFAULT}/inventory`,
  POST_INVENTORY: (id = '') => `${API_MEDICATION_ASSISTED_DEFAULT}/inventory/${id}`,
  TRANSFER_INVENTORY: `${API_MEDICATION_ASSISTED_DEFAULT}/inventory/transfer`,
  TOGGLE_INVENTORY: (id = '') => `${API_MEDICATION_ASSISTED_DEFAULT}/inventory/${id}/toggle`,

  DASHBOARD: `${API_MEDICATION_ASSISTED_DEFAULT}/dashboard`,

  ADMISSION: `${API_MEDICATION_ASSISTED_DEFAULT}/admission`,
  ADMISSION_DETAIL: (id = '') => `${API_MEDICATION_ASSISTED_DEFAULT}/admission/${id}`,
  NO_SHOW: `${API_MEDICATION_ASSISTED_DEFAULT}/admission/no-show`,
  ADMISSION_CONTACTED: (id = '') => `${API_MEDICATION_ASSISTED_DEFAULT}/admission/${id}/contact`,
  ADD_CONTACT_RECORD: `${API_MEDICATION_ASSISTED_DEFAULT}/admission/contact`,

  LOOKUP: `${API_MEDICATION_ASSISTED_DEFAULT}/lookup`,
  ATTENDINGS: `${API_MEDICATION_ASSISTED_DEFAULT}/lookup/attendings`,

  PENDING_INTAKE: `${API_MEDICATION_ASSISTED_DEFAULT}/pending-intake`,
  DISPENSE: `${API_MEDICATION_ASSISTED_DEFAULT}/dispense`,
  TAKE_OUT: `${API_MEDICATION_ASSISTED_DEFAULT}/take-out`,
  SKIP: `${API_MEDICATION_ASSISTED_DEFAULT}/skip`,

  EPISODE: `${API_MEDICATION_ASSISTED_DEFAULT}/episode`,
  ORDER: `${API_MEDICATION_ASSISTED_DEFAULT}/order`,
  FAST_DOSE: `${API_MEDICATION_ASSISTED_DEFAULT}/fast-dose`,
  EXCEPTION: `${API_MEDICATION_ASSISTED_DEFAULT}/exception`,
  SPILL: `${API_MEDICATION_ASSISTED_DEFAULT}/spill`,
  ADMITTED_MEDICATION_ORDER: `${API_MEDICATION_ASSISTED_DEFAULT}/admitted-medication-order`,
  PRINT_MEDICATION_ORDER: `${API_MEDICATION_ASSISTED_DEFAULT}/medication-order/html`,
  DISCHARGE: `${API_MEDICATION_ASSISTED_DEFAULT}/discharge`,

  FACILITY_SCHEDULE: (id = '') => `${API_MEDICATION_ASSISTED_DEFAULT}/facility-schedule/${id}`,

  CARE_PLAN_SCHEDULE: (id = '') => `${API_MEDICATION_ASSISTED_DEFAULT}/care-plan-schedule/${id}`,
  CARE_PLAN: `${API_MEDICATION_ASSISTED_DEFAULT}/care-plan`,
  CARE_PLAN_PRINT: `${API_MEDICATION_ASSISTED_DEFAULT}/care-plan/html`,
  CARE_PLAN_RESULT: `${API_MEDICATION_ASSISTED_DEFAULT}/care-plan/result`,

  GET_QUEUE: (siteId = '') => `${API_MEDICATION_ASSISTED_DEFAULT}/queue/${siteId}`,
  CHECK_IN: ({ siteId = '', matDispenseId = '' } = {}) =>
    `${API_MEDICATION_ASSISTED_DEFAULT}/queue/check-in/${siteId}/${matDispenseId}`,
  ENQUEUE: ({ siteId = '', matQueueId = '' } = {}) =>
    `${API_MEDICATION_ASSISTED_DEFAULT}/queue/enqueue/${siteId}/${matQueueId}`,
  NEXT_SERVING: (siteId = '') => `${API_MEDICATION_ASSISTED_DEFAULT}/queue/next-serving/${siteId}`,

  MONITOR: (siteId = '') => `${API_MEDICATION_ASSISTED_DEFAULT}/monitor/${siteId}`,
  MONITOR_SAVE_CONFIG: (siteId = '') =>
    `${API_MEDICATION_ASSISTED_DEFAULT}/monitor/${siteId}/configurations`,
  MONITOR_SAVE_MESSAGE: (siteId = '') =>
    `${API_MEDICATION_ASSISTED_DEFAULT}/monitor/${siteId}/messages`,

  WS_PUMP: `/general-setting/devices-connector`,
  PUMP_DISPENSE: (deviceId) => `/api/pumps/${deviceId}/dispensation`,
  PUMP_CONNECT: (deviceId) => `/api/pumps/${deviceId}/connection`,
  PUMP_DISCONNECT: (deviceId) => `/api/pumps/${deviceId}/disconnection`,
  PUMP_CLEAN: (deviceId) => `/api/pumps/${deviceId}/cleaning`,
  PUMP_STOP: (deviceId) => `/api/pumps/${deviceId}/suspension`,
  PUMP_PRIME: (deviceId) => `/api/pumps/${deviceId}/priming`,
  PUMP_PRINT: 'api/printers/label/printing',

  ORDER_TEMPLATE: (id = '') => `${API_MEDICATION_ASSISTED_DEFAULT}/order-template/${id}`,

  PENDING_APPROVAL: `${API_MEDICATION_ASSISTED_DEFAULT}/pending-approval`,
  PENDING_APPROVAL_HTML: `${API_MEDICATION_ASSISTED_DEFAULT}/pending-approval/html`,
};

const API_EMPLOYEE_RESTRICTIONS_DEFAULT = `${apiVersion}/employee-restrictions`;
export const API_EMPLOYEE_RESTRICTIONS = {
  GET_LOOKUP_DATA: `${API_EMPLOYEE_RESTRICTIONS_DEFAULT}/lookup`,
  PATIENT_LIMITER: `${API_EMPLOYEE_RESTRICTIONS_DEFAULT}/patient-limiter`,
  ROLE_LIMITER: `${API_EMPLOYEE_RESTRICTIONS_DEFAULT}/role-limiter`,
  UPDATE_ROLE_LIMITER: (clientRuleId = '') =>
    `${API_EMPLOYEE_RESTRICTIONS_DEFAULT}/role-limiter/${clientRuleId}`,
};

const API_ANNOUNCEMENTS_DEFAULT = `${apiVersion}/announcement`;
export const API_ANNOUNCEMENTS = {
  DEFAULT: (id = '') => `${API_ANNOUNCEMENTS_DEFAULT}/${id}`,
  ACKNOWLEDGE: (id) => `${API_ANNOUNCEMENTS_DEFAULT}/${id}/acknowledge`,
  DOWNLOAD_ALL: (id) => `${API_ANNOUNCEMENTS_DEFAULT}/${id}/download`,
  DOWNLOAD_FILE: (id, fileId) => `${API_ANNOUNCEMENTS_DEFAULT}/${id}/download/${fileId}`,
  MARK_AS_READ: (id) => `${API_ANNOUNCEMENTS_DEFAULT}/${id}/mark-as-read`,
  CANCEL: (id) => `${API_ANNOUNCEMENTS_DEFAULT}/${id}/cancel`,
  RE_SCHEDULE: (id) => `${API_ANNOUNCEMENTS_DEFAULT}/${id}/re-schedule`,
  FAVORITE_RECIPIENTS: `${API_ANNOUNCEMENTS_DEFAULT}/favorite-recipients`,
  RECENT_RECIPIENTS: `${API_ANNOUNCEMENTS_DEFAULT}/recent-recipients`,
  SEARCH_RECIPIENTS: `${API_ANNOUNCEMENTS_DEFAULT}/recipients`,
  HISTORY: `${API_ANNOUNCEMENTS_DEFAULT}/history`,
};

const API_EMPLOYEE_NOTIFICATION_DEFAULT = `${apiVersion}/EmployeeNotification`;
export const API_EMPLOYEE_NOTIFICATION = {
  DEFAULT: (id = '') => `${API_EMPLOYEE_NOTIFICATION_DEFAULT}/${id}`,
  MARK_AS_READ: (id) => `${API_EMPLOYEE_NOTIFICATION_DEFAULT}/${id}/read`,
  READ_ALL: `${API_EMPLOYEE_NOTIFICATION_DEFAULT}/read-all`,
  UNREAD: `${API_EMPLOYEE_NOTIFICATION_DEFAULT}/unread`,
};

const API_SUICIDE_SEVERITY_RATING_SCALE_DEFAULT = `${apiVersion}/ssrs`;
export const API_SUICIDE_SEVERITY_RATING_SCALE = {
  DEFAULT: (id = '') => `${API_SUICIDE_SEVERITY_RATING_SCALE_DEFAULT}/${id}`,
  GET_FORM: `${API_SUICIDE_SEVERITY_RATING_SCALE_DEFAULT}/form`,
  PRINT: `${API_SUICIDE_SEVERITY_RATING_SCALE_DEFAULT}/html`,
};

const API_FAMILY_HEALTH_DEFAULT = `${apiVersion}/family-health`;
export const API_FAMILY_HEALTH = {
  DEFAULT: (id = '') => `${API_FAMILY_HEALTH_DEFAULT}/${id}`,
  SAVE_PATIENT_INFO: (id = '') => `${API_FAMILY_HEALTH_DEFAULT}/${id}/patient-education`,
  GET_CONDITIONS: `${API_FAMILY_HEALTH_DEFAULT}/conditions`,
  GET_FAMILY_MEMBERS: `${API_FAMILY_HEALTH_DEFAULT}/family-members`,
  GET_PROBLEM_CODES: `${API_FAMILY_HEALTH_DEFAULT}/problem-codes`,
  GET_STATUSES: `${API_FAMILY_HEALTH_DEFAULT}/statuses`,
  HISTORY: `${API_FAMILY_HEALTH_DEFAULT}/histories`,
  PRINT: `${API_FAMILY_HEALTH_DEFAULT}/html`,
};

const API_PLACEMENT_DEFAULT = `${apiVersion}/placement`;
export const API_PLACEMENT = {
  DEFAULT: (id = '') => `${API_PLACEMENT_DEFAULT}/${id}`,
  SAVE_PATIENT_INFO: (id = '') => `${API_PLACEMENT_DEFAULT}/${id}/patient-education`,
  GET_ADMISSION_TYPES: `${API_PLACEMENT_DEFAULT}/admission-types`,
  GET_ADMIT_CODES: `${API_PLACEMENT_DEFAULT}/admit-codes`,
  GET_DISPOSITIONS: `${API_PLACEMENT_DEFAULT}/dispositions`,
  GET_SERVICES: `${API_PLACEMENT_DEFAULT}/services`,
  HISTORY: `${API_PLACEMENT_DEFAULT}/histories`,
  GET_STAFF_MEMBERS: `${API_PLACEMENT_DEFAULT}/staff-members`,
};

const API_IMPLANTABLE_DEVICE_DEFAULT = `${apiVersion}/implantable-device`;
export const API_IMPLANTABLE_DEVICE = {
  DEFAULT: (id = '') => `${API_IMPLANTABLE_DEVICE_DEFAULT}/${id}`,
  SEARCH_DEVICES: `${API_IMPLANTABLE_DEVICE_DEFAULT}/devices`,
};

const API_IBHIS_DEFAULT = `${apiVersion}/ibhis`;
export const API_IBHIS = {
  DEFAULT: API_IBHIS_DEFAULT,
  DICTIONARY: `${API_IBHIS_DEFAULT}/dictionary`,
  MESSAGE: `${API_IBHIS_DEFAULT}/message`,
  MESSAGE_CONTENT: `${API_IBHIS_DEFAULT}/message/content`,
  CSI: `${API_IBHIS_DEFAULT}/csi`,
  CSI_LOOKUP: `${API_IBHIS_DEFAULT}/csi/lookup`,
  SEARCH_CLIENT: `${API_IBHIS_DEFAULT}/client`,
  PATIENT_LINK_LOOkUP: `${API_IBHIS_DEFAULT}/client/patient-link-lookup`,
  CLIENT_SYNC_HISTORY: `${API_IBHIS_DEFAULT}/client/sync`,
  LINK_CLIENT: `${API_IBHIS_DEFAULT}/client/link`,
  ADMIT_CLIENT: `${API_IBHIS_DEFAULT}/client/admit`,
  ADMIT_LOOKUP: `${API_IBHIS_DEFAULT}/client/admit/lookups`,
  SEARCH_TYPE: `${API_IBHIS_DEFAULT}/client/search-type`,
  EPISODES: (ibhisPatientId) => `${API_IBHIS_DEFAULT}/client/${ibhisPatientId}/episode`,
  DEMOGRAPHIC: `${API_IBHIS_DEFAULT}/client/demographic`,
  DEMOGRAPHIC_SYNC: `${API_IBHIS_DEFAULT}/client/demographic/sync`,
  DEMOGRAPHIC_LOOKUP: `${API_IBHIS_DEFAULT}/client/demographic/lookup`,
  UMDAP: `${API_IBHIS_DEFAULT}/umdap`,
  PREGNANCY: `${API_IBHIS_DEFAULT}/client/pregnancy`,
  FINANCIAL_ELIGIBILITY: `${API_IBHIS_DEFAULT}/client/financial-eligibility`,
  SERVICE_HISTORY: `${API_IBHIS_DEFAULT}/client/service-history`,
  LEGACY_SERVICE_HISTORY: `${API_IBHIS_DEFAULT}/client/legacy-service`,
  DCFS_SERVICE_HISTORY: `${API_IBHIS_DEFAULT}/client/dcfs-service-history`,
  DISCHARGE_CLIENT_LOOKUP: `${API_IBHIS_DEFAULT}/discharge/lookup`,
  DISCHARGE_CLIENT: `${API_IBHIS_DEFAULT}/discharge`,
  DIAGNOSIS: `${API_IBHIS_DEFAULT}/diagnosis`,
  DIAGNOSIS_LOOKUP: `${API_IBHIS_DEFAULT}/diagnosis/lookups`,
  PUBLIC_GUARDIAN_SERVICE: `${API_IBHIS_DEFAULT}/client/public-guardian-service`,

  //CANs
  CANS: (submissionId = '') => `${API_IBHIS_DEFAULT}/epsdt/cans/${submissionId}`,
  CANS_DICTIONARY: `${API_IBHIS_DEFAULT}/epsdt/cans/dictionary`,
  CANS_PREVIOUS: `${API_IBHIS_DEFAULT}/epsdt/cans/previous`,
  CANS_CAREGIVERS: (id = '') => `${API_IBHIS_DEFAULT}/epsdt/cans/caregivers/${id}`,
  CANS_PRINT: (id = '') => `${API_IBHIS_DEFAULT}/epsdt/cans/${id}/html`,

  //PSC
  PSC: (id = '') => `${API_IBHIS_DEFAULT}/epsdt/psc/${id}`,
  PSC_HISTORY: `${API_IBHIS_DEFAULT}/epsdt/psc/histories`,
  PSC_LOOKUP: `${API_IBHIS_DEFAULT}/epsdt/psc/lookups`,
  PSC_PRINT: (id = '') => `${API_IBHIS_DEFAULT}/epsdt/psc/${id}/html`,

  //Service Request
  GET_SERVICE_REQUEST: `${API_IBHIS_DEFAULT}/service-request`,
  UPDATE_SERVICE_REQUEST: `${API_IBHIS_DEFAULT}/service-request`,
  ADD_SERVICE_REQUEST: `${API_IBHIS_DEFAULT}/service-request`,
  DELETE_SERVICE_REQUEST: (serviceRequestId = '') =>
    `${API_IBHIS_DEFAULT}/service-request/${serviceRequestId}`,
  SEARCH_SERVICE_REQUEST: `${API_IBHIS_DEFAULT}/service-request/histories`,
  GET_SERVICE_REQUEST_LOOKUPS: `${API_IBHIS_DEFAULT}/service-request/lookups`,
  GET_SERVICE_CALL_LOG: `${API_IBHIS_DEFAULT}/service-request/logs`,
};

const API_HIDEX_LOCUS_DEFAULT = `${apiVersion}/hidex/locus`;
export const API_HIDEX_LOCUS = {
  DEFAULT: API_HIDEX_LOCUS_DEFAULT,
  GET_LOCUS_QUESTION: `${API_HIDEX_LOCUS_DEFAULT}/questions`,
  COMPUTE_SCORE: `${API_HIDEX_LOCUS_DEFAULT}/score`,
  GET_PATIENT: `${API_HIDEX_LOCUS_DEFAULT}/patient`,
  PRINT: `${API_HIDEX_LOCUS_DEFAULT}/html`,
};

const API_GLOABL_TABS_SET_DEFAULT = `${apiVersion}/global-tabs-set`;
export const API_GLOBAL_TABS_SET = {
  DEFAULT: (id = '') => `${API_GLOABL_TABS_SET_DEFAULT}/${id}`,
  SECURITY_ROLE: `${API_GLOABL_TABS_SET_DEFAULT}/roles`,
};

const API_SHORTCUT_DEFAULT = `${apiVersion}/shorthand`;
export const API_SHORTCUT = {
  DEFAULT: (id = '') => `${API_SHORTCUT_DEFAULT}/${id}`,
  EXPIRE: (id = '') => `${API_SHORTCUT_DEFAULT}/${id}/expire`,
  GET_ALL: `${API_SHORTCUT_DEFAULT}/all`,
  PARAMETER: `${API_SHORTCUT_DEFAULT}/parameter`,
};

const API_PATIENT_PROCEDURE_DEFAULT = `${apiVersion}/patient-procedure`;
export const API_PATIENT_PROCEDURE = {
  GET_PATIENT_PROCEDURE_HISTORY: API_PATIENT_PROCEDURE_DEFAULT,
  CREATE_PATIENT_PROCEDURE: API_PATIENT_PROCEDURE_DEFAULT,
  DELETE_PATIENT_PROCEDURE: API_PATIENT_PROCEDURE_DEFAULT,
  GET_PATIENT_PROCEDURE_DETAIL: (patientProcedureId) =>
    `${API_PATIENT_PROCEDURE_DEFAULT}/${patientProcedureId}`,
  UPDATE_PATIENT_PROCEDURE: (patientProcedureId) =>
    `${API_PATIENT_PROCEDURE_DEFAULT}/${patientProcedureId}`,
  GET_PATIENT_PROCEDURE_LOOKUP_DATA: `${API_PATIENT_PROCEDURE_DEFAULT}/general-lookup`,
  PRINT_PATIENT_PROCEDURE: `${API_PATIENT_PROCEDURE_DEFAULT}/html`,
  PROCEDURES_LOOKUP: `${API_PATIENT_PROCEDURE_DEFAULT}/procedure-lookup`,
  PROCEDURES_RESULTS_LOOKUP: `${API_PATIENT_PROCEDURE_DEFAULT}/result-lookup`,
  STATUSES: `${API_PATIENT_PROCEDURE_DEFAULT}/statuses`,
};

const API_WHO_ASSIST_DEFAULT = `${apiVersion}/assist`;
export const API_WHO_ASSIST = {
  DEFAULT: (batchId = '') => `${API_WHO_ASSIST_DEFAULT}/${batchId}`,
  PRINT: `${API_WHO_ASSIST_DEFAULT}/html`,
  GET_QUESTION: `${API_WHO_ASSIST_DEFAULT}/questions`,
};

const API_DIRECT_MESSAGING_DEFAULT = `${apiVersion}/direct-message`;
export const API_DIRECT_MESSAGING = {
  DEFAULT: (id = '') => `${API_DIRECT_MESSAGING_DEFAULT}/${id}`,
  GET_ADDRESS: `${API_DIRECT_MESSAGING_DEFAULT}/direct-address`,
  HISTORY_USER: `${API_DIRECT_MESSAGING_DEFAULT}/history`,
  GET_DOCUMENT_TYPES: `${API_DIRECT_MESSAGING_DEFAULT}/document-types`,
  HISTORY_PATIENT: (id = '') => `${API_DIRECT_MESSAGING_DEFAULT}/patient/${id}`,
  DOWNLOAD_ATTACHMENT: (id = '') => `${API_DIRECT_MESSAGING_DEFAULT}/${id}/attachments/download`,
  PATCH_FLAG: (id = '') => `${API_DIRECT_MESSAGING_DEFAULT}/${id}/new-flag`,
};

const API_SAFET_DEFAULT = `${apiVersion}/safet`;
export const API_SAFET = {
  DEFAULT: API_SAFET_DEFAULT,
  DELETE_SUICIDE_RISK_SCREENING_ANSWERS: (batchId = '') => `${API_SAFET_DEFAULT}/${batchId}`,
  HISTORY: `${API_SAFET_DEFAULT}/histories`,
  PRINT: `${API_SAFET_DEFAULT}/html`,
  GET_PEND_STEPS: `${API_SAFET_DEFAULT}/pend-steps`,
  GET_SUICIDE_RISK_SCREENING_FORM_QUESTIONS: `${API_SAFET_DEFAULT}/questions`,
};

const API_SUICIDE_RISK_SCREENING_DEFAULT = `${apiVersion}/suicide-risk-screening`;
export const API_SUICIDE_RISK_SCREENING = {
  DEFAULT: (id = '') => `${API_SUICIDE_RISK_SCREENING_DEFAULT}/${id}`,
  HISTORY: `${API_SUICIDE_RISK_SCREENING_DEFAULT}/histories`,
  GET_FORM: `${API_SUICIDE_RISK_SCREENING_DEFAULT}/questions`,
  PRINT: `${API_SUICIDE_RISK_SCREENING_DEFAULT}/html`,
  GET_PEND_STEP: `${API_SUICIDE_RISK_SCREENING_DEFAULT}/pend-steps`,
};

const API_QUESTIONNAIRE_DEFAULT = `${apiVersion}/questionnaire`;
export const API_QUESTIONNAIRE = {
  DEFAULT: (id = '') => `${API_QUESTIONNAIRE_DEFAULT}/${id}`,
  GET_HISTORY: `${API_QUESTIONNAIRE_DEFAULT}/histories`,
  GET_QUESTIONNAIRE: `${API_QUESTIONNAIRE_DEFAULT}/questions`,
  PRINT_QUESTIONNAIRE: `${API_QUESTIONNAIRE_DEFAULT}/html`,
};
