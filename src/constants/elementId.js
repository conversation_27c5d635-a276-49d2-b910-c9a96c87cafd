const ELEMENT_ID = {
  DYNAMIC_NOTE: {
    VIEWER: {
      FORM: 'DYNAMIC_NOTE-VIEWER-FORM',
    },
  },
  BATCH: {
    BATCH_837: {
      HISTORY: 'BATCH-BATCH_837-HISTORY',
      VALIDATIONS: 'BATCH-BATCH_837-VALIDATION',
      OVERVIEW: 'BATCH-BATCH_837-OVERVIEW',
      PREDICT: 'BATCH-BATCH_837-PREDICT',
    },
    RECONCILE_277: {
      RESUBMISSION: 'resubmission',
      ADJUDICATION: 'adjudication',
      BATCH: 'batch_277',
      EDIT: 'edit_277',
      HISTORY_277: 'history_277',
      REQUEST: 'request',
    },
    RECONCILE_835: {
      RESUBMISSION: 'resubmission',
      CLAIM_ASSOCIATED: 'CLAIM_ASSOCIATED',
    },
    PAYOR_EDITOR: {
      EDIT: 'edit',
      CLASSIFICATIONS: 'classifications',
      ENROLLMENT_CODES: 'enrollmentCodes',
      PROGRAM: 'program',
      EXPIRE: 'expire',
      HISTORY: 'history',
    },
    REMIT_EOB: {
      FORM: 'remit-form',
      ENTRIES: 'remit-entries',
    },
    PROVIDER: {
      LICENSE_DETAIL: 'provider-license-detail',
      CONTRACT_ACTION: 'provider-contract-action',
    },
    ENCOUNTER: {
      HISTORY: 'batch-encounter-history',
    },
  },

  DF: {
    SECTION_VIEW: 'dynamic-form_section-view',
  },
  PCP_COMMUNICATION: {
    INPUT: 'pcp-communication-input',
    HISTORY: 'pcp-communication-history',
  },
  INPATIENT: {
    CONTRABAND: {
      INPUT: 'contraband-input',
      HISTORY: 'contraband-history',
    },
    ROOM_CHECK: {
      INPUT: 'room-check-input',
      HISTORY: 'room-check-history',
    },
    ART: {
      INPUT: 'art-input',
      HISTORY: 'art-history',
    },
    VITALS: {
      INPUT: 'vitals-input',
      HISTORY: 'vitals-history',
    },
    VALUEABLE: {
      INPUT: 'valueable-input',
      HISTORY: 'valueable-history',
    },
    ORDER_LIST: {
      INPUT: 'order-list-input',
      HISTORY: 'order-list-history',
    },
    RESTRAINT: {
      INPUT: 'restraint-seclusion-input',
      HISTORY: 'restraint-seclusion-history',
    },
    DAILY_NURSE: {
      INPUT: 'daily-nurse-input',
      HISTORY: 'daily-nurse-history',
    },
    STAFF_NOTE: {
      INPUT: 'staff-daily-note-input',
      HISTORY: 'staff-daily-note-history',
    },
    COMPREHENSIVE: {
      INPUT: 'comprehensive-input',
      HISTORY: 'comprehensive-history',
    },
    NURSING_ADMISSION_ASSESSMENT: {
      INPUT: 'nursing-admission-assessment-input',
      HISTORY: 'nursing-admission-assessment-history',
    },
    HEALTH_PHYSICAL_NOTE: {
      INPUT: 'health-physical-note-input',
      HISTORY: 'health-physical-note-history',
    },
    ENGAGEMENT: {
      INPUT: 'engagement-input',
      HISTORY: 'engagement-history',
    },
    DISCHARGE_PLAN: {
      INPUT: 'discharge-plan-input',
      HISTORY: 'discharge-plan-history',
    },
    INPATIENT_NOTE: {
      INPUT: 'inpatient-note-input',
      HISTORY: 'inpatient-note-history',
    },
    PSYCHOTHERAPY_PROGRESS_NOTE: {
      INPUT: 'psychotherary-note-input',
      HISTORY: 'psychotherary-note-history',
    },
    PSYCHOSOCIAL_ASSESSMENT_NOTE: {
      INPUT: 'psychosocial-note-input',
      HISTORY: 'psychosocial-note-history',
    },
  },
  INFORMED_CONSENT: {
    HISTORY: 'informed-consent-history',
    INPUT: 'informed-consent-input',
  },
  RESIDENTIAL: {
    DEPENDENTS: {
      INPUT: 'residential-dependents-input',
      HISTORY: 'residential-dependents-history',
    },
    IMPORTANT_DATES: {
      HISTORY: 'residential-importants-dates-comments-history',
      INPUT: 'residential-importants-dates-comments-input',
    },
    CONTRABAND_CHECK: {
      HISTORY: 'residential-contraband-check-history',
      INPUT: 'residential-contraband-check-input',
    },
    SCANED_DOCUMENTS: {
      HISTORY: 'scaned-documents-history',
      INPUT: 'scaned-documents-input',
    },
    ROOM_CHECK: {
      INPUT: 'room-check-input',
      HISTORY: 'room-check-history',
    },
    NURSE_DAILY_NOTE: {
      INPUT: 'nurse-daily-note-input',
      HISTORY: 'nurse-daily-note-history',
    },
    DAILY_STAFF_NOTE: {
      INPUT: 'daily-staff-note-input',
      HISTORY: 'daily-staff-note-history',
    },
  },
  TABLE: {
    BATCH: {
      BATCH_837: {
        DEFAULT: 'batch837',
        VALIDATIONS: 'batch837Validation',
        ENCOUNTER: 'batch837Encounter',
      },
    },
  },

  SIDEBAR: {
    ROOT: 'sidebar-root',
  },
  PATIENT_ASSIGNMENT: {
    SITE: {
      HISTORY: 'site-history',
      FORM: 'site-form',
    },
    TEAM: {
      HISTORY: 'team-history',
      FORM: 'team-form',
    },
    PRIORITY: {
      HISTORY: 'priority-history',
      FORM: 'priority-form',
    },
    PCP: {
      FORM: 'pcp-form',
    },
    GROUP: {
      HISTORY: 'group-history',
      FORM: 'group-form',
    },
  },
  CYBHI: {
    CLIENT_DATA: {
      HISTORY: 'client-data-history',
      IDENTIFY_HISTORY: 'client-data-identify-history',
      FORM: 'client-data-form',
    },
  },
  COVER_SHEET: {
    PATIENT: {
      HISTORY: 'patient-history',
      IDENTIFY_HISTORY: 'patient-identify-history',
      FORM: 'patient-form',
    },
    ADDRESS: {
      HISTORY: 'address-history',
      FORM: 'address-form',
    },
    CONTACT: {
      HISTORY: 'contact-history',
      FORM: 'contact-form',
    },
    PATIENT_PORTAL: {
      FORM: 'portal-form',
    },
    INSURANCE: {
      FORM: 'insurance-form',
    },
    ADDITIONAL_INFO: {
      HISTORY: 'additional-info-history',
      FORM: 'additional-info-form',
    },
    CONSENT: {
      HISTORY: 'consent-history',
      FORM: 'consent-form',
    },
  },
  PROBLEM_LIST: {
    HISTORY: 'problem-list-history',
    INPUT: 'problem-list-input',
  },
  PROGRAM: {
    HISTORY: 'program-history',
    INPUT: 'program-input',
  },
  QUICK_ENROLLMENT: {
    INPUT: 'quick-enrollment-input',
  },
  HEADER: {
    VOICE_ASSISTANT: 'header-voice-assistant',
    CHAT_SCROLL_ID: 'chat-scroll-id',
    PATIENT_AVATAR: 'header-patient-avatar',
    PATIENT_CHAT_SCROLL_ID: 'paient-chat-scroll-id',
  },
  ENCOUNTER_FORM_MANAGERMENT: {
    SITE: {
      HISTORY: 'site-list-history',
      INPUT: 'site-list-input',
    },
    CODE: {
      HISTORY: 'code-list-history',
      INPUT: 'code-list-input',
    },
    GRID: {
      HISTORY: 'grid-list-history',
      INPUT: 'grid-list-input',
    },
    PROGRAM: {
      HISTORY: 'program-list-history',
      INPUT: 'program-list-input',
    },
  },
  FOSTER_HOME: {
    DASHBOARD: 'foster-home-dashboard',
    SEARCH: 'foster-home-search-patient',
    COMPLETE_FOSTER_HOME_REGISTRATION: 'complete-foster-home-registration',
    EDIT_FOSTER_HOME: 'edit-foster-home',
    CHILD_ASSIGNMENT: 'child-assignment-foster-home',
    FOSTER_HOME: 'foster-home',
    ADDRESS: {
      HISTORY: 'address-history',
      FORM: 'address-form',
    },
    LICENSE: {
      HISTORY: 'license-info-history',
      FORM: 'license-info-form',
    },
    REQUIREMENT: {
      HISTORY: 'requirement-history',
      FORM: 'requirement-form',
    },
    MEMBER: {
      HISTORY: 'member-history',
      FORM: 'member-form',
      REQUIREMENT_HISTORY: 'member-requirement-history',
      REQUIREMENT_INPUT: 'member-requirement-input',
    },
    NOTE: {
      HISTORY: 'note-history',
      FORM: 'note-form',
    },
    CHILD_ASSIGNMENT_HISTORY: 'child-assignment-history',
    CHILD_ASSIGNMENT_FORM: 'child-assignment-form',
    CARE_CASE_NOTE: {
      HISTORY: 'foster-home-history-care-case-note-history',
      FORM: 'foster-home-history-care-case-note-form',
      DEFAULT: 'foster-home-history-care-case-note',
    },
    CASE_MANAGEMENT: {
      SITE: 'foster-home-child-assignment-case-management-site',
      TEAM: 'foster-home-child-assignment-case-management-team',
      PRIORITY: 'foster-home-child-assignment-case-management-priority',
      PCP: 'foster-home-child-assignment-case-management-pcp',
      GROUP: 'foster-home-child-assignment-case-management-group',
    },
  },
  GROUP_EDITOR: {
    HISTORY: 'group-editor-history',
    INPUT: 'group-editor-input',
    PATIENT: 'group-editor-patient',
    PATIENT_INPUT: 'group-editor-patient-input',
  },
  REPORT: {
    HISTORY: 'dynamic-report-history',
    FORM: 'dynamic-report-form',
    RECENT: 'recent-reports',
    BUIL_IN: 'buil-in-report-module',
    REPORT_MODULE: 'report-module',
    TABLE_BUILD_BATCH_PRINT: 'table-build-batch-print-report',
    EHI_EXPORT_HISTORY_REPORT: 'ehi-export-history-report',
  },
  LOOKUP_TABLE: {
    HISTORY: 'lookup-table-history',
    TABLE: 'lookup-table-detail',
    TABLE_FORM: 'look-table-form',
  },
  IMMUNIZATION: {
    HISTORY: 'immunization-history',
    REGISTRY_SETTINGS: 'immunixation-registry-settings',
    INPUT: 'immunization-input',
    TIME_LINE: 'immunization-timeline',
  },
  SUPPORT: {
    MFA: {
      BY_PASS: 'support-mfa-by-pass',
      HISTORY: 'support-mfa-history',
      REQUEST: 'support-mfa-request',
    },
    MENU_MANAGEMENT: {
      HISTORY: 'support-menu-management-history',
      INPUT: 'support-menu-management-input',
    },
  },
  SERVICE_PLAN_FIELD_MANAGER: {
    SERVICE_PLAN_MAPPING_FIELDS: 'service-plan-mapping-fields',
    SERVICE_PLAN_MAPPING_FIELD_INPUT: 'service-plan-mapping-field-input',
    SERVICE_PLAN_SERVICE_CODES: 'service-plan-service-codes',
    SERVICE_PLAN_SERVICE_CODE_INPUT: 'service-plan-service-code-input',
    SERVICE_PLAN_FIELDS: 'service-plan-mapping-fields',
    SERVICE_PLAN_FIELD_INPUT: 'service-plan-mapping-field-input',
    SERVICE_PLAN_SIGNATURE_HISTORY: 'service-plan-signature-history',
    SERVICE_PLAN_SIGNATURE_INPUT: 'service-plan-signature-input',
  },
  HEALTH_INFORMATION: {
    MEDICAL_RECORDS_RELEASES: 'health-information-medical-records-releases',
    RELEASES: 'health-information-releases',
    HEALTH_INFORMATION_RELEASE_INPUT: 'health-information-release-input',
  },
  QUALITY_MANAGEMENT: {
    QUALITY_MANAGEMENT_INCIDENT_REPORT_SEARCH: 'quality-management-incident-report-search',
    QUALITY_MANAGEMENT_INCIDENT_REPORT_INPUT: 'quality-management-incident-report-input',
    QUALITY_MANAGEMENT_CGA_SEARCH: 'quality-management-cga-search',
    QUALITY_MANAGEMENT_CGA_INPUT: 'quality-management-cga-input',
  },
  GROUP_NOTE_SCHEDULE: 'group-note-schedule',
  PSYCH_EVALUATION_NOTE: {
    PSYCH_EVALUATION_NOTE_INPUT: 'psych-evaluation-note-input',
  },
  PSYCH_NOTE: {
    PSYCH_NOTE_INPUT: 'psych-evaluation-note-input',
  },
  CM_NOTE: {
    CM_NOTE_INPUT: 'cm-note-input',
  },
  NOTE: {
    ALL_NOTE_HISTORY: 'all-note-history',
  },
  WORKLIST: {
    ASSIGNED: 'worklist-assigned',
    ERRORS: 'worklist-errors',
    FLAGGED: 'worklist-flagged',
  },
  PAYOR_ASSIGNMENT: {
    HISTORY: 'payor-assignment-history',
    INFORMATION: 'payor-assignment-information',
  },
  PHQ_A: {
    HISTORY: 'phq-a-history',
    FORM: 'phq-a-form',
  },
  MAT: {
    ADD_INVENTORY: 'mat-add-inventory',
    MAT_TRANFER: 'mat-tranfer',
  },
  COURT_ORDERED_TREATMENT: {
    COMMENT: {
      HISTORY: 'comment-history',
      FORM: 'comment-form',
    },
    AMENDMENTS: {
      HISTORY: 'amendment-history',
      FORM: 'amendment-form',
    },
    JUDICIAL_REVIEW: {
      HISTORY: 'judicial-review-history',
      FORM: 'judicial-review-form',
    },
    COT: {
      HISTORY: 'cot-history',
      FORM: 'cot-form',
    },
    STATUS_REPORT: {
      HISTORY: 'status-report-history',
      FORM: 'status-report-form',
    },
  },
  ENCOUNTER_ENTRY: {
    ENCOUNTER_ENTRY_BATCH_HISTORY: 'encounter-entry-batch-history',
    ENCOUNTER_ENTRY_BATCH_INFORMATION: 'encounter-entry-batch-information',
    ENCOUNTER_ENTRY_CURRENT_ENTRY: 'encounter-entry-current-entry',
    ENCOUNTER_ENTRY_SERVICES: 'encounter-entry-services',
  },
  SERVICE_PLAN: {
    MEET_OBJECTIVES: 'meet-objectives',
    INPUT: 'service-plan-input',
  },
  PATIENT_PROCEDURE: {
    PATIENT_PROCEDURE_HISTORY: 'patient-procedure-history',
    PATIENT_PROCEDURE_INPUT: 'patient-procedure-input',
  },
  PCP_NOTE: {
    PCP_NOTE_INPUT: 'pcp-note-input',
  },
  IBHIS: {
    PSC_INPUT: 'psc-input',
    DIAGNOSIS_HISTORY: 'diagnosis-history',
    DIAGNOSIS_INPUT: 'diagnosis-input',
    SERVICE_REQUEST_LOG_INPUT_ID: 'service-request-log-input',
  },
  ART_CFT: {
    ART_CFT_MEETING_SUMMARY: 'art-cft-meeting-sumary',
    ART_CFT_MEETING_SUMMARY_HISTORY: 'art-cft-meeting-sumary-history',
  },
  FUNDING_SOURCE: {
    HISTORY: 'funding-source-history',
    INPUT: 'funding-source-input',
  },
};

export default ELEMENT_ID;
