const QUERY_KEY = {
  APPOINTMENT_HISTORY: 'APPOINTMENT_HISTORY',
  APPOINTMENT_CRM_HISTORY: 'APPOINTMENT_CRM_HISTORY',
  APPOINTMENT_BY_SITE: 'APPOINTMENT_BY_SITE',
  APPOINTMENT_CALENDAR: 'APPOINTMENT_CALENDAR',
  APPOINTMENT_CALENDAR_DATE: 'APPOINTMENT_CALENDAR_DATE',
  APPOINTMENT_PROGRESS_NOTE: 'APPOINTMENT_PROGRESS_NOTE',
  APPOINTMENT_TIME_BLOCKED: 'APPOINTMENT_TIME_BLOCKED',
  APPOINTMENT_CALENDAR_TIME_BLOCKED: 'APPOINTMENT_CALENDAR_TIME_BLOCKED',
  PROGRESS_NOTE_CALENDAR: 'PROGRESS_NOTE_CALENDAR',
  AVATAR: 'AVATAR',
  AVATAR_CARD_VIEW: 'AVATAR_CARDVIEW',
  PATIENT_AVATAR: 'PATIENT_AVATAR',
  OTHER_AGENCY_MEDICATION: 'OTHER_AGENCY_MEDICATION',
  PSYCH_NOTE_OTHER_AGENCY_MEDICATION: 'PSYCH_NOTE_OTHER_AGENCY_MEDICATION',
  PSYCH_NOTE_INTERNAL_ORDER_LIST: 'PSYCH_NOTE_INTERNAL_ORDER_LIST',
  SDOH_HISTORY: 'SDOH_HISTORY',
  PCPTSD: 'PCPTSD',
  AIMS_HISTORY: 'AIMS_HISTORY',
  DEMOGRAPHIC: 'DEMOGRAPHIC',
  PATIENT_DEMOGRAPHIC: 'PATIENT_DEMOGRAPHIC',
  RADIOLOGY_HISTORY: 'RADIOLOGY_HISTORY',
  PATIENT_NOTES_HISTORY: 'PATIENT_NOTES_HISTORY',
  PATIENT_DEMOGRAPHIC_HISTORIES: 'PATIENT_DEMOGRAPHIC_HISTORIES',
  PATIENT_CHAT: 'PATIENT_CHAT',
  CURRENT_ALLERGY: 'CURRENT_ALLERGY',
  ALLERGY_HISTORY: 'ALLERGY_HISTORY',
  ANNOUNCEMENTS_HISTORY: 'ANNOUNCEMENTS_HISTORY',
  EMPLOYEE_NOTIFICATION: 'EMPLOYEE_NOTIFICATION',
  EMPLOYEE_ANNOUNCEMENTS_HISTORY: 'EMPLOYEE_ANNOUNCEMENTS_HISTORY',
  CURRENT_PATIENT: 'CURRENT_PATIENT',

  // Dynamic form
  DYNAMIC_FORM_TEMPLATE_PAGING: 'DYNAMIC_FORM_TEMPLATE_PAGING',
  DYNAMIC_FORM_RECENT_FORM: 'DYNAMIC_FORM_RECENT_FORM',
  DYNAMIC_FORM_HISTORY: 'DYNAMIC_FORM_HISTORY',
  DYNAMIC_FORM_ANSWER_HISTORY: 'DYNAMIC_FORM_ANSWER_HISTORY',
  DYNAMIC_FORM_ANSWER_PREVIOUS_DATA: 'DYNAMIC_FORM_ANSWER_PREVIOUS_DATA',

  INJECTION_HISTORY: 'INJECTION_HISTORY',
  RX_HISTORY: 'RX_HISTORY',
  RX_TIMELINE: 'RX_TIMELINE',
  ASAM_HISTORY: 'ASAM_HISTORY',
  SIG_MEDICATION: 'SIG_MEDICATION',
  SENDING_SITE_LOOKUP: 'SENDING_SITE_LOOKUP',
  PROVIDER_LOOKUP: 'PROVIDER_LOOKUP',
  PATIENT_ATTRIBUTE: 'PATIENT_ATTRIBUTE',
  DYNAMIC_ATTRIBUTES: 'PATIENT_ATTRIBUTES',
  PROGRESS_NOTE_AUTO_SAVE: 'PROGRESS_NOTE_AUTO_SAVE',
  PROGRESS_NOTE_HISTORY: 'PROGRESS_NOTE_HISTORY',
  CM_NOTE_SERVICES: 'CM_NOTE_SERVICES',
  PSYCH_NOTE_SERVICES: 'PSYCH_NOTE_SERVICES',
  DYNAMIC_PROGRESS_NOTE_ANSWER_HISTORY: 'DYNAMIC_PROGRESS_NOTE_ANSWER_HISTORY',
  DYNAMIC_PROGRESS_NOTE_ASSESSMENT_PLAN: 'DYNAMIC_PROGRESS_NOTE_ASSESSMENT_PLAN',
  DYNAMIC_PROGRESS_NOTE_INPUT_TYPES: 'DYNAMIC_PROGRESS_NOTE_INPUT_TYPES',
  DYNAMIC_PROGRESS_NOTE_INPUT_TYPES_CONFIG_FLAG: 'DYNAMIC_PROGRESS_NOTE_INPUT_TYPES_CONFIG_FLAG',
  CRISIS_PLAN_HISTORY: 'CRISIS_PLAN_HISTORY',
  BATCH_837_HISTORY: 'BATCH_837_HISTORY',
  BATCH_837_DOWNLOAD_DROPPED: 'BATCH_837_DOWNLOAD_DROPPED',
  BATCH_837_DOWNLOAD_OVERVIEW: 'BATCH_837_DOWNLOAD_OVERVIEW',
  BATCH_837_HIPAA: 'BATCH_837_HIPAA',
  CRISIS_PREVENTION_PLAN_HISTORY: 'CRISIS_PREVENTION_PLAN_HISTORY',
  RECONCILE_835_HISTORY: 'RECONCILE_835_HISTORY',
  SNCD_PLAN_HISTORY: 'SNCD_PLAN_HISTORY',
  PAYOR_ASSIGNMENT_HISTORY: 'PAYOR_ASSIGNMENT_HISTORY',
  SUPPORT_PLAN_HISTORY: 'SUPPORT_PLAN_HISTORY',
  BATCH_PAYOR_EDITOR_HISTORY: 'BATCH_PAYOR_EDITOR_HISTORY',
  RCM_FEE_SCHEDULE_SERVICES_LIST: 'RCM_FEE_SCHEDULE_SERVICES_LIST',
  RCM_FEE_SCHEDULE_SERVICE_MATRIX_LIST: 'RCM_FEE_SCHEDULE_SERVICE_MATRIX_LIST',
  RCM_FEE_SCHEDULE_OVERRIDE: 'RCM_FEE_SCHEDULE_OVERRIDE',
  RCM_FEE_SCHEDULE_PLACE_OF_SERVICE: 'RCM_FEE_SCHEDULE_PLACE_OF_SERVICE',
  RCM_FEE_SCHEDULE_PLACE_OF_SERVICE_MODAL: 'RCM_FEE_SCHEDULE_PLACE_OF_SERVICE_MODAL',
  RCM_FEE_SCHEDULE_QUALIFICATIONS: 'RCM_FEE_SCHEDULE_QUALIFICATIONS',
  RCM_FEE_SCHEDULE_CROSS_REFERENCE: 'RCM_FEE_SCHEDULE_CROSS_REFERENCE',
  RCM_FEE_SCHEDULE_SERVICE_BUNDLE: 'RCM_FEE_SCHEDULE_SERVICE_BUNDLE',
  RCM_FEE_SCHEDULE_NDC: 'RCM_FEE_SCHEDULE_NDC',
  MEDICATION_ACTIVE: 'MEDICATION_ACTIVE',
  MEDICATION_EXPIRED: 'MEDICATION_EXPIRED',
  ADJUDICATION: 'BATCH_ADJUDICATION',
  BATCH_DETAIL_HISTORY: 'BATCH_DETAIL_HISTORY',
  CLAIM_SUBMISSION_DETAIL: 'CLAIM_SUBMISSION_DETAIL',
  RECONCILE_277_HISTORY: 'RECONCILE_277_HISTORY',
  BATCH_834_HISTORY: 'BATCH_834_HISTORY',
  ENCOUNTER_HISTORY: 'ENCOUNTER_HISTORY',
  ENCOUNTER_WRITE_OFF: 'ENCOUNTER_WRITE_OFF',
  FLAG_LIST: 'FLAG_LIST',
  RENDERING_PROVIDER_HISTORY: 'RENDERING_PROVIDER_HISTORY',
  COPAY_HISTORY: 'COPAY_HISTORY',
  COPAY_HISTORY_APPT: 'COPAY_HISTORY_APPT',
  INPATIENT_MODULE_LIST: 'INPATIENT_MODULE_LIST',
  PROVIDER_BATCH_LIST: 'PROVIDER_BATCH_LIST',
  PROVIDER_CLASSIFICATION_LIST: 'PROVIDER_CLASSIFICATION_LIST',
  PROVIDER_IDENTIFIERS_LIST: 'PROVIDER_IDENTIFIERS_LIST',
  SERVICE_PLAN_HISTORY: 'SERVICE_PLAN_HISTORY',
  SERVICE_PLAN_LIST_CAN_BE_REOPEN: 'SERVICE_PLAN_LIST_CAN_BE_REOPEN',
  PENDING_NOTIFICATION_HISTORY: 'PENDING_NOTIFICATION_HISTORY',
  ADMISSION_ORDERS_HISTORY: 'ADMISSION_ORDERS_HISTORY',
  ANCILLARY_ORDERS: 'ANCILLARY_ORDERS',
  ASSOCIATED_FILES_HISTORY: 'ASSOCIATED_FILES_HISTORY',
  DISCHARGE_PLAN_HISTORY: 'DISCHARGE_PLAN_HISTORY',
  RESIDENTIAL_HISTORY_LIST: 'RESIDENTIAL_HISTORY_LIST',
  RESIDENTIAL_DEPENDENTS_HISTORY: 'RESIDENTIAL_DEPENDENTS_HISTORY',
  SERVICE_PLAN_AUTO_SAVE_HISTORY: 'SERVICE_PLAN_AUTO_SAVE_HISTORY',
  SERVICE_PLAN_GET_UI_RENDERING: 'SERVICE_PLAN_GET_UI_RENDERING',
  IMPORTANT_DATE_RESIDENTIAL_COMMENTS: 'IMPORTANT_DATE_RESIDENTIAL_COMMENTS',
  DYNAMIC_SERVICE_PLAN_HISTORIES: 'DYNAMIC_SERVICE_PLAN_HISTORIES',
  DYNAMIC_SERVICE_PLAN_ANSWER_HISTORIES: 'DYNAMIC_SERVICE_PLAN_ANSWER_HISTORIES',
  LIST_QUESTION_DATABASE_DYNAMIC_SERVICE_PLAN: 'LIST_QUESTION_DATABASE_DYNAMIC_SERVICE_PLAN',
  SECURITY_ALL_PAGES: 'SECURITY_ALL_PAGES',
  SECURITY_ALL_MODULES: 'SECURITY_ALL_MODULES',
  RPA: {
    DETAIL: 'RPA-DETAIL',
  },
  GET_INDIVIDUALVALUES_DYNAMIC_REPORT: 'GET_INDIVIDUALVALUES_DYNAMIC_REPORT',
  GET_EMPLOYEE_EDUCATION: 'GET_EMPLOYEE_EDUCATION',
  GET_EMPLOYEE_EDUCATION_QUALIFICATION: 'GET_EMPLOYEE_EDUCATION_QUALIFICATION',
  GET_EMPLOYEE_EDUCATION_LICENSE: 'GET_EMPLOYEE_EDUCATION_LICENSE',
  GET_DYNAMIC_REPORT_HISTORY: 'GET_DYNAMIC_REPORT_HISTORY',
  PROBLEM_LIST_HISTORY_CARD_VIEW: 'PROBLEM_LIST_HISTORY_CARD_VIEW',
  PROBLEM_LIST: 'PROBLEM_LIST',
  PROBLEM_LIST_IN_MODAL_ADD: 'PROBLEM_LIST_IN_MODAL_ADD',
  PROBLEM_PROGRAM_HISTORY: 'PROBLEM_PROGRAM_HISTORY',
  ENCOUNTER_FORM_PROGRAM_HISTORY: 'ENCOUNTER_FORM_PROGRAM_HISTORY',
  AIMS_CARD_VIEW_CURRENT: 'AIMS_CARD_VIEW_CURRENT',
  PROBLEM_LIST_CURRENT_CARD_VIEW: 'PROBLEM_LIST_CURRENT_CARD_VIEW',
  PCL5_CARD_VIEW_CURRENT: 'PCL5_CARD_VIEW_CURRENT',
  PHQ_CURRENT_CARD_VIEW: 'PHQ_CURRENT_CARD_VIEW',
  ACE_CURRENT_CARD_VIEW: 'ACE_CURRENT_CARD_VIEW',
  GAD_7_CURRENT_CARD_VIEW: 'GAD_7_CURRENT_CARD_VIEW',
  PCPTSD_CURRENT_CARD_VIEW: 'PCPTSD_CURRENT_CARD_VIEW',
  AUDIT_CURRENT_CARD_VIEW: 'AUDIT_CURRENT_CARD_VIEW',
  SDOH_CURRENT_CARD_VIEW: 'SDOH_CURRENT_CARD_VIEW',
  ASAM_GET_DETAIL: 'ASAM_GET_DETAIL',
  FOSTER_HOME_SEARCH: 'FOSTER_HOME_SEARCH',
  FOSTER_CHILD_ASSIGNMENT_HISTORY: 'FOSTER_CHILD_ASSIGNMENT_HISTORY',
  FOSTER_HOME_CHILD_ASSIGNMENT_CASE_NOTE_HISTORY: 'FOSTER_HOME_CHILD_ASSIGNMENT_CASE_NOTE_HISTORY',
  FOSTER_HOME_DASHBOARD: 'FOSTER_HOME_DASHBOARD',
  FOSTER_HOME_DETAIL: 'FOSTER_HOME_DETAIL',
  RCM_REPORT_CLAIMS_VOLUME: 'RCM_REPORT_CLAIMS_VOLUME',

  IMMUNIZATION_HISTORY: 'IMMUNIZATION_HISTORY',
  IMMUNIZATION_HISTORY_CARDVIEW: 'IMMUNIZATION_HISTORY_CARDVIEW',
  GET_IMMUNIZATION_REGISTRY_SETTING: 'GET_IMMUNIZATION_REGISTRY_SETTING',

  GROUP_EDITOR_HISTORY: 'GROUP_EDITOR_HISTORY',
  CLOSURE_HISTORY: 'CLOSURE_HISTORY',

  LOOKUP_TABLE_FORM: 'LOOKUP_TABLE_FORM',
  MFA_REQUEST_HISTORY: 'MFA_REQUEST_HISTORY',
  MFA_HISTORY: 'MFA_HISTORY',

  LAB_HISTORY: 'LAB_HISTORY',
  EXAM_OPTIONS_TYPES: 'EXAM_OPTIONS_TYPES',
  EXAM_OPTIONS_OPTIONS: 'EXAM_OPTIONS_OPTIONS',

  SERVICE_PLAN_MAPPING_FIELDS_HISTORY: 'SERVICE_PLAN_MAPPING_FIELDS_HISTORY',
  SERVICE_PLAN_SERVICE_CODES_HISTORY: 'SERVICE_PLAN_SERVICE_CODES_HISTORY',
  SERVICE_PLAN_FIELDS_HISTORY: 'SERVICE_PLAN_FIELDS_HISTORY',

  PATIENT_DEMOGRAPHIC_HISTORY_FOR_INPUT: 'PATIENT_DEMOGRAPHIC_HISTORY_FOR_INPUT',
  PATIENT_DEMOGRAPHIC_HISTORY_TABLE: 'PATIENT_DEMOGRAPHIC_HISTORY_TABLE',

  ALL_MEDICAL_RECORDS_RELEASES_BY_CURRENT_EMPLOYEE:
    'ALL_MEDICAL_RECORDS_RELEASES_BY_CURRENT_EMPLOYEE',
  FIND_PATIENT_HEALTH_INFORMATION: 'FIND_PATIENT_HEALTH_INFORMATION',
  ALL_MEDICAL_RECORDS_RELEASES_BY_PATIENT: 'ALL_MEDICAL_RECORDS_RELEASES_BY_PATIENT',
  FIND_PATIENT_HEALTH_INFORMATION_INPUT: 'FIND_PATIENT_HEALTH_INFORMATION_INPUT',

  PRESCRIPTIONS_LOOKUP: 'PRESCRIPTIONS_LOOKUP',
  PHARMACIES_LOOKUP: 'PHARMACIES_LOOKUP',
  TARGET_SYMPTOM_LOOKUP: 'TARGET_SYMPTOM_LOOKUP',

  LAB_ASSOCIATED_FILES: 'LAB_ASSOCIATED_FILES',

  RCM_PAYOR_ASSIGNMENT_ASSOCIATED_FILES: 'RCM_PAYOR_ASSIGNMENT_ASSOCIATED_FILES',

  INPATIENT_GET_SITE_LOOKUP: 'INPATIENT_GET_SITE_LOOKUP',
  INPATIENT_SITE_CHECK: 'INPATIENT_SITE_CHECK',
  RESIDENTIAL_GET_SITE_LOOKUP: 'RESIDENTIAL_GET_SITE_LOOKUP',
  INPATIENT_VALUEABLE_GET_LOOKUP: 'INPATIENT_VALUEABLE_GET_LOOKUP',
  INPATIENT_RESIDENTIAL_SITE_CHECK_HISTORY: 'INPATIENT_RESIDENTIAL_SITE_CHECK_HISTORY',
  CLINICAL_FORMS_SEARCH_ENCOUNTERS: 'CLINICAL_FORMS_SEARCH_ENCOUNTERS',
  CLINICAL_FORMS_SEARCH_FORMS: 'CLINICAL_FORMS_SEARCH_FORMS',

  QUALITY_MANAGEMENT_INCIDENT_REPORT_HISTORY: 'QUALITY_MANAGEMENT_INCIDENT_REPORT_HISTORY',
  FIND_PATIENT_QUALITY_MANAGEMENT_INCIDENT_REPORT:
    'FIND_PATIENT_QUALITY_MANAGEMENT_INCIDENT_REPORT',
  QUALITY_MANAGEMENT_GET_PATIENT_DETAIL: 'QUALITY_MANAGEMENT_GET_PATIENT_DETAIL',

  QUALITY_MANAGEMENT_CGA_HISTORY: 'QUALITY_MANAGEMENT_CGA_HISTORY',
  FIND_PATIENT_QUALITY_MANAGEMENT_CGA: 'FIND_PATIENT_QUALITY_MANAGEMENT_CGA',
  QUALITY_MANAGEMENT_CGA_GET_PATIENT_DETAIL: 'QUALITY_MANAGEMENT_CGA_GET_PATIENT_DETAIL',

  HEALTH_INFORMATION_GET_PATIENT_DETAIL: 'HEALTH_INFORMATION_GET_PATIENT_DETAIL',

  EMPLOYMENT_ACTION_INFO: 'EMPLOYMENT_ACTION_INFO',

  DATA_IMPORT_RETRIEVE_TABLE_DIAGRAM: 'DATA_IMPORT_RETRIEVE_TABLE_DIAGRAM',
  DATA_IMPORT_RETRIEVE_TABLE: 'DATA_IMPORT_RETRIEVE_TABLE',
  DATA_IMPORT_HISTORY: 'DATA_IMPORT_HISTORY',
  DATA_IMPORT_RUNS_HISTORY_LIST: 'DATA_IMPORT_RUNS_HISTORY_LIST',

  TODO_LIST_HISTORY: 'TODO_LIST_HISTORY',

  // CALL OUTSIDE
  CALL_FAVORITE_LIST: 'CALL_FAVORITE_LIST',

  APPOINTMENT_GUESS_LIST: 'APPOINTMENT_GUESS_LIST',
  APPOINTMENT_GROUP_GUESS_LIST: 'APPOINTMENT_GROUP_GUESS_LIST',
  APPOINTMENT_FAVORITE_GUEST_LIST: 'APPOINTMENT_FAVORITE_GUEST_LIST',
  APPOINTMENT_RECENT_GUEST_LIST: 'APPOINTMENT_RECENT_GUEST_LIST',

  COVER_SHEET_PATIENT_INFO: 'COVER_SHEET_PATIENT_INFO',
  PSYCH_SPI_DETAIL: 'PSYCH_SPI_DETAIL',
  PATIENT_ACCESS_REQUEST: 'PATIENT_ACCESS_REQUEST',
  WORKLIST: 'WORKLIST',

  CRM_NOTE_LIST: 'CRM_NOTE_LIST',
  CRM_TAG: 'CRM_TAG',
  CRM_CATEGORY: 'CRM_CATEGORY',
  CRM_CATEGORY_DROP: 'CRM_CATEGORY_DROP',
  CRM_CLIENT: 'CRM_CLIENT',
  CRM_CLIENT_TAG: 'CRM_CLIENT_TAG',
  CRM_CLIENT_CATEGORY: 'CRM_CLIENT_CATEGORY',
  CRM_CLIENT_CATEGORY_DROP: 'CRM_CLIENT_CATEGORY_DROP',
  CRM_CLIENT_TASK_CREATE: 'CRM_CLIENT_TASK_CREATE',
  CRM_CLIENT_TASK_LIST: 'CRM_CLIENT_TASK_LIST',
  CRM_CLIENT_LOOKUP: 'CRM_CLIENT_LOOKUP',

  AMEND_PROGRESS_NOTE_HISTORY: 'AMEND_PROGRESS_NOTE_HISTORY',
  RECENT_REPORTS_HISTORY: 'RECENT_REPORTS_HISTORY',

  RECENT_REPORTS_STATUS_COUNT: 'RECENT_REPORTS_STATUS_COUNT',
  RECENT_REPORTS_MAT_STATUS_COUNT: 'RECENT_REPORTS_MAT_STATUS_COUNT',
  RECENT_REPORTS_GET_PARAMS: 'RECENT_REPORTS_GET_PARAMS',

  GET_PARAMS_REPORT_DYNAMIC: 'GET_PARAMS_REPORT_DYNAMIC',
  //REPORT_MODULE
  LIST_CATEGORY: 'LIST_CATEGORY',
  GET_PARAMS_REPORT_STANDARD: 'GET_PARAMS_REPORT_STANDARD',

  VIRTUAL_PATIENT_CHAT: 'VIRTUAL_PATIENT_CHAT',
  ASSISTANT_CHAT: 'ASSISTANT_CHAT',

  SURESCRIPT_HISTORY: 'SURESCRIPT_HISTORY',
  SETUP_TRIGGER_HISTORY: 'SETUP_TRIGGER_HISTORY',

  DIAGNOSIS_CODES_LIST: 'DIAGNOSIS_CODES_LIST',
  GROUP_FILTER_LIST: 'GROUP_FILTER_LIST',

  PHQ_A_HISTORY: 'PHQ_A_HISTORY',
  FUNDING_SOURCE_HISTORY: 'FUNDING_SOURCE_HISTORY',
  PHQ_HISTORY: 'PHQ_HISTORY',
  PCL5_HISTORY: 'PCL5_HISTORY',
  GAD_7_HISTORY: 'GAD_7_HISTORY',
  ACE_HISTORY: 'ACE_HISTORY',
  PRAPARE_HISTORY: 'PRAPARE_HISTORY',
  COWS_HISTORY: 'COWS_HISTORY',
  ENGAGEMENT_HISTORY: 'ENGAGEMENT_HISTORY',
  SUICIDE_SEVERITY_RATING_SCALE_HISTORY: 'SUICIDE_SEVERITY_RATING_SCALE_HISTORY',
  FAMILY_HEALTH_HISTORY: 'FAMILY_HEALTH_HISTORY',

  COT_HISTORY: 'COT_HISTORY',
  CALOCUS_HISTORY: 'CALOCUS_HISTORY',
  LOCUS_HISTORY: 'LOCUS_HISTORY',

  MAT_INVENTORY: 'MAT_INVENTORY',
  MAT_INVENTORY_TRANSFER: 'MAT_INVENTORY_TRANSFER',
  MAT_PENDING_INTAKE: 'MAT_PENDING_INTAKE',
  MAT_PENDING_INTAKE_HISTORY: 'MAT_PENDING_INTAKE_HISTORY',
  MAT_ADMISSION: 'MAT_ADMISSION',
  MAT_DISPENSE: 'MAT_DISPENSE',

  TIME_BLOCK_REASON_COLOR: 'TIME_BLOCK_REASON_COLOR',
  SCHEDULE_REASON: 'SCHEDULE_REASON',

  MAT_NO_SHOW: 'MAT_NO_SHOW',
  MAT_DASHBOARD: 'MAT_DASHBOARD',
  MAT_RECENT_REPORT: 'MAT_RECENT_REPORT',
  MAT_FACILITY_SCHEDULE: 'MAT_FACILITY_SCHEDULE',
  MAT_CARE_PLAN_SCHEDULE: 'MAT_CARE_PLAN_SCHEDULE',
  MAT_QUEUE: 'MAT_QUEUE',
  MAT_MONITOR_CONFIG: 'MAT_MONITOR_CONFIG',

  SPECIALIST_REFERRAL_HISTORY: 'SPECIALIST_REFERRAL_HISTORY',
  SERVICE_PLAN_AUTO_SAVE_DETAIL: 'SERVICE_PLAN_AUTO_SAVE_DETAIL',
  CALOCUS_BY_PATIENT: 'CALOCUS_BY_PATIENT',

  PATIENT_ROLE_LIMITER_HISTORY: 'PATIENT_ROLE_LIMITER_HISTORY',
  PATIENT_ROLE_GET_DETAIL_PATIENT: 'PATIENT_ROLE_GET_DETAIL_PATIENT',
  PATIENT_EMPLOYEE_LIMITER_HISTORY: 'PATIENT_EMPLOYEE_LIMITER_HISTORY',

  PLACEMENT_HISTORY: 'PLACEMENT_HISTORY',
  IMPLANTABLE_DEVICES_HISTORY: 'IMPLANTABLE_DEVICES_HISTORY',

  IBHIS_CLIENT_RECORD: 'IBHIS_CLIENT_RECORD',
  IBHIS_CLIENT_RECORD_HISTORY: 'IBHIS_CLIENT_RECORD_HISTORY',
  IBHIS_UMDAP: 'IBHIS_UMDAP',
  IBHIS_PREGNANCY: 'IBHIS_PREGNANCY',
  IBHIS_DIAGNOSES: 'IBHIS_DIAGNOSES',
  IBHIS_CSI: 'IBHIS_CSI',
  IBHIS_FINANCIAL_ELIGIBILITY: 'IBHIS_FINANCIAL_ELIGIBILITY',
  IBHIS_CANS: 'IBHIS_CANS',
  IBHIS_PSC_GET_DETAIL: 'IBHIS_PSC_GET_DETAIL',

  PATIENT_PROCEDURE_HISTORY: 'PATIENT_PROCEDURE_HISTORY',
  PATIENT_PROCEDURES_LOOKUP: 'PATIENT_PROCEDURES_LOOKUP',

  PROGRESS_NOTE_SERVICE_SITE: 'PROGRESS_NOTE_SERVICE_SITE',
  PROGRESS_NOTE_SERVICE_POS: 'PROGRESS_NOTE_SERVICE_POS',
  CYBHI_CLIENT_DATA: 'CYBHI_CLIENT_DATA',
  WHO_ASSIST_HISTORY: 'who-assist-history',
  DIRECT_MESSAGES: 'direct-messages',

  SUICIDE_ASSESSMENT_FIVE_STEP_EVALUATION_AND_TRIAGE_HISTORY:
    'SUICIDE_ASSESSMENT_FIVE_STEP_EVALUATION_AND_TRIAGE_HISTORY',
  SUICIDE_RISK_SCREENING_HISTORY: 'SUICIDE_RISK_SCREENING_HISTORY',
  GET_EMPLOYEE_PROGRAM: 'GET_EMPLOYEE_PROGRAM',
  SERVICE_PLAN_SIGNATURE_HISTORY: 'SERVICE_PLAN_SIGNATURE_HISTORY',
};

export const BE_CODE = {
  DUPLICATED: 'DuplicateEntry',
};

export default QUERY_KEY;
