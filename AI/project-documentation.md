# Axiom-v2 Project Documentation

## Project Overview
Axiom-v2 is a React-based healthcare management application built with FluentUI components. The application manages patient data, clinical forms, assessments, and various healthcare workflows.

## Technology Stack
- **Frontend**: React 18+ with Hooks
- **UI Library**: FluentUI React (@fluentui/react)
- **Form Management**: React Hook Form + Formik (legacy)
- **State Management**: React Context + Custom Hooks
- **Styling**: SCSS + CSS-in-JS
- **Build Tool**: Create React App (CRA)
- **Package Manager**: npm/yarn

## Project Structure

```
src/
├── components/           # Reusable UI components
│   ├── Form/            # Form field components (legacy Formik)
│   ├── HookForm/        # Form field components (React Hook Form)
│   ├── Layout/          # Layout components (ResponsiveGrid, ResponsiveItem)
│   ├── Table/           # Table components
│   ├── Modal/           # Modal components
│   └── ...
├── modules/             # Feature modules (main business logic)
│   ├── PHQA/           # PHQ-A assessment module
│   ├── FundingSource/  # Funding source management
│   └── ...
├── pages/              # Page components
│   └── ContentPage/    # Main content pages
├── constants/          # Application constants
│   ├── routes.js       # Route definitions and module names
│   ├── modules.js      # Module configurations
│   ├── queryKey.js     # React Query keys
│   └── urlRequest.js   # API endpoints
├── contexts/           # React contexts
├── hooks/              # Custom hooks
└── utils/              # Utility functions
```

## Coding Conventions

### Component Structure
Each feature module follows this pattern:
```
ModuleName/
├── ModuleHistory/      # History/listing component
│   └── index.jsx
├── ModuleInput/        # Form input component  
│   └── index.jsx
└── ModuleForm/         # Alternative form component
    └── index.jsx
```

### Naming Conventions
- **Components**: PascalCase (`FundingSourceHistory`)
- **Files**: PascalCase for components (`index.jsx`)
- **Variables**: camelCase (`editItem`, `apiSave`)
- **Constants**: UPPER_SNAKE_CASE (`MODULE_NAME`, `API_METHOD`)
- **CSS Classes**: kebab-case (`funding-source-input`)

### Import Organization
```javascript
// 1. React imports
import React, { useContext, useEffect } from 'react';

// 2. Third-party libraries
import { DefaultButton, PrimaryButton } from '@fluentui/react';
import { FormProvider } from 'react-hook-form';
import { toast } from 'react-toastify';

// 3. Internal components
import useHookForm from 'components/HookForm/useHookForm';
import FieldSelectorPrimary from 'components/HookForm/FieldSelectorPrimary';
import CollapseVertical from 'components/Collapse/CollapseVertical';

// 4. Hooks and utilities
import { useNavigator, useRequest } from 'hooks';
import { handleValidationBE } from 'utils/form';
import { t } from 'utils/string';

// 5. Constants
import { API_METHOD } from 'constants/urlRequest';
import { MODULE_NAME } from 'constants/routes';
import { FormEditContext } from 'contexts/FormEditContext';
```

## Form Management

### React Hook Form (Preferred)
```javascript
const methods = useHookForm({
  defaultValues: editItem || defaultValues,
  dependencies: { editItem }
});

return (
  <FormProvider {...methods}>
    <FieldSelectorPrimary name="fieldName" options={[]} />
    <FieldDate name="dateField" placeholder="mm/dd/yyyy" />
    <FieldText name="textField" />
  </FormProvider>
);
```

### Layout System
Use ResponsiveGrid and ResponsiveItem for layouts:
```javascript
<ResponsiveGrid>
  <ResponsiveItem md={4}>
    <FieldSelectorPrimary title={t('Field 1')} name="field1" options={[]} />
  </ResponsiveItem>
  <ResponsiveItem md={4}>
    <FieldDate title={t('Date Field')} name="dateField" />
  </ResponsiveItem>
  <ResponsiveItem md={12}>
    <FieldText title={t('Comment')} name="comment" />
  </ResponsiveItem>
</ResponsiveGrid>
```

## API Integration

### useRequest Hook Pattern
```javascript
const apiSave = useRequest({
  url: '/api/endpoint',
  method: API_METHOD.POST
});

const onSubmit = (values) => {
  apiSave.request({
    payload: values,
    options: {
      onSuccess: () => {
        toast.success(TEXT_TEMPLATE.SAVE_SUCCESSFULLY(t('Item')));
        navigate({ hash: MODULE_NAME.HISTORY });
      },
      onError: (err) => {
        handleValidationBE({ setError: methods.setError }, err);
      }
    }
  });
};
```

## Module Registration

### 1. Add Route Constants (constants/routes.js)
```javascript
// Route names
FUNDING_SOURCE: '/funding-source',

// Module names  
FUNDING_SOURCE_HISTORY: 'fundingSourceHistory',
FUNDING_SOURCE_INPUT: 'fundingSourceInput',
```

### 2. Register Modules (constants/modules.js)
```javascript
// Import components
const FundingSourceHistory = lazy(() => import('modules/FundingSource/FundingSourceHistory'));
const FundingSourceInput = lazy(() => import('modules/FundingSource/FundingSourceInput'));

// Module configurations
{
  rootPage: ROUTE_NAME.FUNDING_SOURCE,
  key: MODULE_NAME.FUNDING_SOURCE_HISTORY,
  text: t('Funding Source History'),
  icon: 'assets/images/modern-history.png',
  Component: FundingSourceHistory,
},

// Page sections
{
  key: ROUTE_NAME.FUNDING_SOURCE,
  text: t('Funding Source'),
  subMenu: generatePageSections([
    MODULE_NAME.FUNDING_SOURCE_HISTORY,
    MODULE_NAME.FUNDING_SOURCE_INPUT
  ]),
  showCardView: true,
}
```

### 3. Add Page Component (pages/ContentPage/ModuleName/index.jsx)
```javascript
import React from 'react';
import { MainContentWrapper } from '../components';
import { usePageSections } from 'hooks';
import { ROUTE_NAME } from 'constants/routes';

const ModuleName = () => {
  const { pageSections, mapContent } = usePageSections({ key: ROUTE_NAME.MODULE_NAME });
  return (
    <MainContentWrapper pageSections={pageSections} pageName>
      {mapContent()}
    </MainContentWrapper>
  );
};

export default ModuleName;
```

## Edit Context Pattern

### FormEditContext Usage
```javascript
// In History component
const { setEditItem } = useContext(FormEditContext).getEditFunctions(MODULE_NAME.MODULE_INPUT);

const onEdit = (item) => {
  setEditItem(item);
  navigate({ hash: MODULE_NAME.MODULE_INPUT });
};

// In Input component  
const { editItem, setEditItem } = useContext(FormEditContext).getEditFunctions(MODULE_NAME.MODULE_INPUT);

useEffect(() => {
  if (editItem) {
    methods.reset(editItem);
  }
}, [editItem, methods]);
```

### Modal Context Wrapping
```javascript
// For modals containing both History and Input components
<FormEditProvider>
  <ModalLayout>
    <ModuleHistory />
    <ModuleInput />
  </ModalLayout>
</FormEditProvider>
```

## Table Components

### Standard Table Pattern
```javascript
const columns = [
  {
    name: 'Column Name',
    fieldName: 'fieldName',
    sortable: true,
    renderItem: ({ fieldName }, _, c, render) => render({ date: fieldName }), // for dates
  },
  { key: 'Action', name: 'Action', fieldName: 'action' }
];

<Table
  columns={columns}
  items={data?.items || []}
  getMenuProps={getMenu}
  loading={loading}
  totalItems={data?.totalItems}
  metadata={params}
  onMetadataChange={updateParams}
/>
```

## Common Patterns

### Internationalization
```javascript
import { t } from 'utils/string';

// Usage
title={t('Funding Source')}
text={t('Submit')}
```

### Toast Notifications
```javascript
import { TEXT_TEMPLATE } from 'constants/texts';

toast.success(TEXT_TEMPLATE.SAVE_SUCCESSFULLY(t('Item Name')));
toast.error(TEXT_TEMPLATE.DELETE_CONFIRMATION(t('this item')));
```

### Navigation
```javascript
import { MODULE_NAME } from 'constants/routes';

navigate({ hash: MODULE_NAME.MODULE_HISTORY });
```

## Best Practices

1. **Always use empty arrays for dropdown options** until API integration
2. **Wrap FormProvider around entire form**, not individual sections
3. **Use ResponsiveGrid/ResponsiveItem** instead of CSS grid classes
4. **Follow the established module structure** for consistency
5. **Use FormEditContext** for edit functionality between components
6. **Import components lazily** in modules.js for better performance
7. **Use optional chaining** for potentially undefined objects (`ELEMENT_ID.MODULE?.FIELD`)
8. **Handle loading states** in API calls and disable buttons during submission

## FluentUI Migration Notes

The project is migrating from FluentUI v8 to v9. When creating new components:
- Use the latest FluentUI v9 components when possible
- Follow the migration guide for component mapping
- Maintain backward compatibility with existing v8 components
